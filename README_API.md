# API与架构文档

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        FE[Vue 3 前端]
        WEB[Web界面]
        CLI[CLI工具]
        LARK[飞书集成]
    end
    
    subgraph "API层"
        API[FastAPI服务]
        WS[WebSocket]
        SSE[Server-Sent Events]
    end
    
    subgraph "核心引擎"
        LG[LangGraph引擎]
        PLAN[planner_agent]
        WORK[worker_agent]
        RPT[reporter_agent]
    end
    
    subgraph "数据层"
        PG[(PostgreSQL)]
        RDS[(Redis)]
        DC[DataCenter]
    end
    
    subgraph "外部服务"
        LLM[多LLM提供方]
        DB[业务数据库]
    end
    
    FE --> API
    WEB --> API
    CLI --> LG
    LARK --> LG
    
    API --> LG
    API --> WS
    API --> SSE
    
    LG --> PLAN
    LG --> WORK
    LG --> RPT
    
    PLAN --> DC
    WORK --> DC
    RPT --> DC
    
    DC --> PG
    API --> RDS
    
    WORK --> LLM
    WORK --> DB
```

### LangGraph图谱设计

基于Functional API的智能体编排：

```mermaid
graph LR
    subgraph "图谱流转"
        A[用户输入] --> B[db_health_check]
        B --> C[planner_agent]
        C --> D{Functional调度}
        D -->|并发| E[worker_task_1]
        D -->|并发| F[worker_task_2]
        D -->|并发| G[worker_task_n]
        E --> C
        F --> C
        G --> C
        C -->|聚合/再规划| D
        C -->|完成| H[reporter_agent]
        H --> I[结构化输出]
    end
```

### 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant A as API服务
    participant L as LangGraph
    participant D as DataCenter
    participant P as PostgreSQL
    participant R as Redis
    participant LLM as LLM服务

    U->>A: 创建任务
    A->>R: 存储任务状态
    A->>L: 启动图谱执行
    L->>D: 预取数据
    D->>P: 查询/存储数据
    L->>LLM: 规划任务
    L->>LLM: 并发分析
    L->>D: 存储结果
    D->>P: 持久化
    L->>A: 返回结果
    A->>R: 更新状态
    A->>U: 推送更新
```

## 🔌 REST API接口

### 基础信息

- **Base URL**: `http://localhost:2026`
- **API文档**: `http://localhost:2026/docs`
- **认证方式**: 暂无（个人项目）

### 任务管理API

#### 1. 创建任务（新接口）

```http
POST /api/tasks/create
Content-Type: application/json

{
  "user_input": "分析推流成功率趋势",
  "max_parallel_workers": 5,
  "recursion_limit": 30
}
```

**响应**:
```json
{
  "thread_id": "uuid-string",
  "status": "running",
  "created_at": "2025-08-22T07:50:18.065907",
  "user_input": "分析推流成功率趋势",
  "config": {
    "max_parallel_workers": 5,
    "recursion_limit": 30
  },
  "message": "Task created and started successfully"
}
```

#### 2. 批量创建任务

```http
POST /api/tasks/batch-create
Content-Type: application/json

{
  "tasks": [
    {
      "user_input": "分析推流成功率趋势",
      "max_parallel_workers": 3,
      "recursion_limit": 25
    },
    {
      "user_input": "分析推流错误码分布",
      "max_parallel_workers": 5,
      "recursion_limit": 30
    }
  ]
}
```

#### 3. 获取任务列表

```http
GET /api/tasks/?limit=20&offset=0
```

**响应**:
```json
{
  "tasks": [
    {
      "thread_id": "uuid",
      "status": "running",
      "agent_status": "running",
      "created_at": "2025-08-22T08:45:24.576523",
      "user_input": "分析推流成功率",
      "config": {}
    }
  ],
  "total": 100,
  "running_tasks": ["thread_id1", "thread_id2"]
}
```

#### 4. 获取任务详情

```http
GET /api/tasks/{thread_id}
```

#### 5. 任务操作

```http
POST /api/tasks/{thread_id}/continue    # 继续执行
DELETE /api/tasks/{thread_id}           # 删除任务
```

### 实时通信API

#### 1. 事件流（SSE）

```http
GET /api/tasks/events/{thread_id}
Accept: text/event-stream
```

**事件类型**:
- `task_created`: 任务创建
- `status_update`: 状态更新
- `progress_update`: 进度更新
- `task_completed`: 任务完成
- `task_failed`: 任务失败

#### 2. LangGraph流式执行

```http
POST /api/tasks/{thread_id}/stream
Content-Type: application/json

{
  "stream_mode": "updates",  // 或 "values"
  "input": {...}
}
```

### 健康检查API

```http
GET /api/health/           # 整体健康状态
GET /api/health/redis      # Redis连接状态
GET /api/health/postgres   # PostgreSQL连接状态
GET /api/health/tasks      # 任务系统状态
```

## 🔧 核心组件

### 1. TaskStateManager

**职责**: 任务状态管理、事件流处理

**关键方法**:
```python
class TaskStateManager:
    async def create_task(self, thread_id: str, user_input: str, config: Dict) -> Dict
    async def start_task_execution(self, thread_id: str) -> bool
    async def subscribe_task_events(self, thread_id: str) -> AsyncGenerator
    async def get_running_tasks(self) -> List[str]
    async def update_task_status(self, thread_id: str, status: str) -> bool
```

### 2. CheckpointManager

**职责**: PostgreSQL持久化存储管理

**关键方法**:
```python
class CheckpointManager:
    async def get_task_list(self, limit: int, offset: int) -> List[Dict]
    async def get_task_detail(self, thread_id: str) -> Optional[Dict]
    async def get_checkpoints(self, thread_id: str) -> List[Dict]
```

### 3. DataCenter

**职责**: 统一数据存储与复用

**特性**:
- 内容寻址存储（SQL → SHA256前16位）
- 参数键映射（param_key → sql_hash）
- 自动去重与缓存

**核心方法**:
```python
class DataCenter:
    async def store_data(self, key: str, data: pd.DataFrame, error_info: str = None)
    async def get_dataframe(self, param_key: str) -> Optional[pd.DataFrame]
    async def batch_query_and_store_data(self, tasks: List[DAGPlanStep]) -> List[DAGPlanStep]
```

### 4. ContextBuilder

**职责**: 统一上下文构建和消息工程

**特性**:
- 解决GLM API "messages参数非法"问题
- 统一SystemMessage和HumanMessage构建
- 支持JSON Schema输出

**使用示例**:
```python
ctx = ContextBuilder(state=state)
ctx.add_base_analysis_role()
ctx.add_json_schema(WorkerResult)
ctx.add_plan_goal().add_plan_thinking()
ctx.set_human_task("请分析以下数据。")
messages = ctx.build_messages()
```

## 🗄️ 数据存储

### PostgreSQL存储

#### LangGraph Checkpoint表结构

```sql
-- checkpoints表
CREATE TABLE checkpoints (
    thread_id VARCHAR NOT NULL,
    checkpoint_id VARCHAR NOT NULL,
    parent_checkpoint_id VARCHAR,
    type VARCHAR,
    checkpoint JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (thread_id, checkpoint_id)
);

-- checkpoint_writes表
CREATE TABLE checkpoint_writes (
    thread_id VARCHAR NOT NULL,
    checkpoint_id VARCHAR NOT NULL,
    task_id VARCHAR NOT NULL,
    idx INTEGER,
    channel VARCHAR,
    type VARCHAR,
    value JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### LangGraph Store表结构

```sql
-- store_items表（KV/Vector存储）
CREATE TABLE store_items (
    namespace_path VARCHAR[] NOT NULL,
    key VARCHAR NOT NULL,
    value JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (namespace_path, key)
);

-- 向量索引（如果启用）
CREATE INDEX idx_store_items_vector ON store_items 
USING ivfflat (vector) WITH (lists = 100);
```

### Redis存储

#### 数据结构设计

```
# 任务基本信息
task:{thread_id}                 (Hash)
├── data                         # 任务JSON数据
├── status                       # 任务状态
└── updated_at                   # 更新时间

# 任务状态索引
task_status:{thread_id}          (String) -> pending|running|done|failed

# Agent状态
agent_status:{thread_id}         (String) -> pending|running|done|failed

# 运行中任务集合
running_tasks                    (Set) -> [thread_id1, thread_id2, ...]

# 任务事件流
task_events:{thread_id}          (List) -> [event1, event2, ...]

# 发布订阅频道
task_events:{thread_id}          (Channel) -> 实时事件推送
```

### 存储策略

| 存储类型 | 用途 | 特点 | 数据示例 |
|---------|------|------|----------|
| **PostgreSQL** | 持久化存储 | 一致性、可靠性 | 任务历史、检查点、分析结果 |
| **Redis** | 实时状态 | 高性能、易失性 | 运行状态、事件流、临时缓存 |
| **LangGraph Store** | 数据中心 | 内容寻址、向量检索 | SQL缓存、经验库、分析数据 |

## 🤖 LLM集成

### 支持的模型提供方

```python
SUPPORTED_PROVIDERS = {
    "deepseek": {
        "base_url_env": "DEEPSEEK_BASE_URL",
        "api_key_env": "DEEPSEEK_API_KEY",
        "models": ["deepseek-chat", "deepseek-coder"]
    },
    "ali": {
        "base_url_env": "ALI_BASE_URL", 
        "api_key_env": "ALI_API_KEY",
        "models": ["qwen-turbo", "qwen-plus", "qwen-max"]
    },
    "openrouter": {
        "base_url_env": "OPENROUTER_BASE_URL",
        "api_key_env": "OPENROUTER_API_KEY", 
        "models": ["openrouter/auto"]
    },
    "glm": {
        "base_url_env": "GLM_BASE_URL",
        "api_key_env": "GLM_API_KEY",
        "models": ["glm-4", "glm-4-flash"]
    },
    "kimi": {
        "base_url_env": "KIMI_BASE_URL",
        "api_key_env": "KIMI_API_KEY",
        "models": ["moonshot-v1-8k", "moonshot-v1-32k"]
    }
}
```

### 结构化输出

使用统一的结构化请求方法：

```python
async def structured_request(
    model: BaseLanguageModel,
    messages: List[BaseMessage],
    response_model: Type[BaseModel],
    max_input_tokens: int = 128000
) -> Tuple[BaseModel, Dict]:
    """
    统一结构化输出接口
    
    Returns:
        Tuple[结构化对象, usage_metadata]
    """
    result = await model.with_structured_output(
        response_model, 
        method="json_mode", 
        include_raw=True
    ).ainvoke(messages)
    
    return result.parsed, result.raw.usage_metadata
```

### Token统计与限制

- **输入限制**: 128k tokens（可配置）
- **统计方式**: 自动统计input_tokens和output_tokens
- **回写机制**: 通过`create_token_update()`回写到State

## 🔍 SQL生成与执行

### SQL生成流程

```mermaid
graph LR
    A[DataQueryParams] --> B[选择指标定义]
    B --> C[选择表类型]
    C --> D[生成SQL模板]
    D --> E[参数替换]
    E --> F[SQL优化]
    F --> G[返回最终SQL]
```

### 执行器设计

```python
async def execute_sql_async(
    type: Literal["ocean", "themis"],
    sql: str,
    max_retries: int = 3,
    base_delay: float = 1.0,
    themis_db: Optional[str] = None
) -> SqlExecutorQueryResult:
    """
    SQL执行器
    
    Features:
    - 指数退避重试
    - 错误码抽取与语义化
    - 多数据源支持
    - SOCKS5代理支持
    """
```

### 数据沉淀策略

1. **内容寻址**: `sql → sha256(sql)[:16]`
2. **键值映射**: `param_key → sql_hash`
3. **错误处理**: 失败信息与SQL一同存储
4. **自动去重**: 相同SQL自动复用结果

## 🔧 配置管理

### 环境变量配置

所有配置通过环境变量管理，支持：

- **LLM配置**: 各厂商的BASE_URL和API_KEY
- **数据库配置**: 连接信息和代理设置
- **应用配置**: 并发限制、重试参数等

### 配置验证

启动时自动验证所有必需配置：

```python
def validate_environment():
    """验证环境配置"""
    required_vars = ["DEEPSEEK_API_KEY", "ALI_API_KEY"]  # 至少一个
    missing = [var for var in required_vars if not os.getenv(var)]
    
    if len(missing) == len(required_vars):
        raise EnvironmentError("至少需要配置一个LLM提供方")
```

### 动态配置

支持运行时配置切换：

```python
config = {
    "configurable": {
        "thread_id": "session-123",
        "max_parallel_workers": 5,
        "recursion_limit": 30,
        "model_provider": "deepseek"
    }
}
```

## 🔐 安全设计

### 网络安全

- **代理分离**: LLM请求直连，数据库查询走代理
- **连接池**: 避免连接泄漏
- **超时控制**: 防止请求hang住

### 数据安全

- **输入验证**: Pydantic严格验证
- **SQL注入防护**: 参数化查询
- **敏感信息**: 环境变量管理，不记录日志

### 访问控制

- **无认证模式**: 个人项目，暂无用户系统
- **IP限制**: 可配置允许访问的IP段
- **速率限制**: 防止接口滥用

## 📊 监控与可观测性

### 日志系统

```python
# 统一日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 组件日志
logger = logging.getLogger("qflow.api")
logger.info("任务创建", extra={"thread_id": thread_id})
```

### 指标监控

- **任务指标**: 创建速率、完成速率、失败率
- **性能指标**: 响应时间、并发数、内存使用
- **系统指标**: Redis连接、PostgreSQL连接状态

### 健康检查

```python
class HealthChecker:
    async def check_redis(self) -> bool
    async def check_postgres(self) -> bool  
    async def check_llm_providers(self) -> Dict[str, bool]
    async def get_system_status(self) -> Dict
```

## 🚀 性能优化

### 数据库优化

- **连接池**: PostgreSQL和Redis连接复用
- **批量操作**: 减少数据库交互次数
- **索引优化**: 关键查询字段建立索引

### 缓存策略

- **L1缓存**: 内存LRU缓存
- **L2缓存**: Redis分布式缓存  
- **L3缓存**: PostgreSQL持久化缓存

### 并发控制

- **任务并发**: 可配置worker数量
- **连接并发**: 数据库连接池限制
- **请求并发**: API限流保护

## 🔄 扩展性设计

### 水平扩展

- **无状态API**: 支持多实例部署
- **任务分片**: 大任务自动拆分
- **负载均衡**: Redis作为协调中心

### 垂直扩展

- **插件化**: 新增LLM提供方无需修改核心代码
- **模块化**: 各组件独立，便于替换
- **配置化**: 通过配置文件调整行为

---

*更多实现细节请参考源代码和 [开发指南](README_DEVELOPMENT.md)*