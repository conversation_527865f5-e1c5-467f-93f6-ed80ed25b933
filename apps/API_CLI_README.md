# QFlowAgent API CLI 工具使用说明

## 概述

`api_cli.py` 是一个用于调试 QFlowAgent 后端API的命令行工具，可以脱离前端直接与后端服务交互，启动分析任务、查看任务状态等。

## 前置条件

1. 确保后端API服务已启动（默认端口2026）
2. 安装必要的Python依赖：
   ```bash
   uv pip install httpx
   ```

## 快速开始

### 1. 检查API服务状态

```bash
# 检查API健康状态
python api_cli.py --action health

# 测试数据库连接
python api_cli.py --action db-test
```

### 2. 启动分析任务

```bash
# 创建新任务
python api_cli.py --action create --input "分析3s拉流请求成功率"

# 使用自定义参数
python api_cli.py --action create \
  --input "分析推流成功率趋势" \
  --max-workers 3 \
  --recursion-limit 50
```

### 3. 监控任务执行

```bash
# 流式执行任务（实时查看执行过程）
python api_cli.py --action stream \
  --thread-id <your_thread_id> \
  --input "分析3s拉流请求成功率"

# 等待任务完成
python api_cli.py --action wait \
  --thread-id <your_thread_id> \
  --timeout 600
```

### 4. 查看任务信息

```bash
# 获取任务列表
python api_cli.py --action list --limit 10

# 获取特定任务详情
python api_cli.py --action detail --thread-id <your_thread_id>
```

## 完整参数说明

### 全局参数

- `--url`: API服务地址，默认为 `http://localhost:2026`
- `--action`: 执行的操作，必需参数

### 操作类型

#### 1. `health` - API健康检查
```bash
python api_cli.py --action health
```

#### 2. `db-test` - 数据库连接测试
```bash
python api_cli.py --action db-test
```

#### 3. `create` - 创建新任务
```bash
python api_cli.py --action create --input "你的分析需求"
```
**必需参数:**
- `--input`: 用户输入内容

**可选参数:**
- `--max-workers`: 最大并行工作数，默认5
- `--recursion-limit`: 递归限制，默认100

#### 4. `list` - 获取任务列表
```bash
python api_cli.py --action list
```
**可选参数:**
- `--limit`: 返回数量限制，默认20
- `--offset`: 偏移量，默认0

#### 5. `detail` - 获取任务详情
```bash
python api_cli.py --action detail --thread-id <thread_id>
```
**必需参数:**
- `--thread-id`: 任务线程ID

#### 6. `stream` - 流式执行任务
```bash
python api_cli.py --action stream --thread-id <thread_id> --input "分析需求"
```
**必需参数:**
- `--thread-id`: 任务线程ID
- `--input`: 用户输入内容

**可选参数:**
- `--max-workers`: 最大并行工作数，默认5
- `--recursion-limit`: 递归限制，默认100

#### 7. `wait` - 等待任务完成
```bash
python api_cli.py --action wait --thread-id <thread_id>
```
**必需参数:**
- `--thread-id`: 任务线程ID

**可选参数:**
- `--timeout`: 等待超时时间(秒)，默认300
- `--check-interval`: 检查间隔(秒)，默认5

## 使用流程示例

### 完整的分析任务流程

```bash
# 1. 检查服务状态
python api_cli.py --action health

# 2. 创建分析任务
python api_cli.py --action create --input "分析最近一周的拉流成功率趋势"

# 3. 从输出中获取 thread_id，然后流式执行
python api_cli.py --action stream \
  --thread-id <从步骤2获取的thread_id> \
  --input "分析最近一周的拉流成功率趋势"

# 4. 或者等待任务完成
python api_cli.py --action wait \
  --thread-id <从步骤2获取的thread_id> \
  --timeout 600
```

### 使用自定义API地址

```bash
# 如果后端运行在其他端口
python api_cli.py --url http://localhost:2026 --action health

# 如果后端运行在其他主机
python api_cli.py --url http://*************:2026 --action health
```

## 输出说明

### 流式执行输出格式

流式执行会实时显示任务的执行过程：

```
开始流式执行任务 web_12345678-1234-1234-1234-123456789abc...
============================================================
[2024-01-01T12:00:00] UPDATES
  消息: 1 条
  计划: 0 个
  工作结果: 0 个
----------------------------------------
[2024-01-01T12:00:01] MESSAGES
  human: 分析3s拉流请求成功率
----------------------------------------
[2024-01-01T12:00:02] VALUES
  状态更新: ['messages', 'plans']
----------------------------------------
[2024-01-01T12:00:03] SUCCESS
  任务执行完成!
----------------------------------------
[2024-01-01T12:00:03] CLOSE
  流式连接关闭
============================================================
流式执行结束
```

### 任务状态说明

- `pending`: 任务已创建，等待执行
- `running`: 任务正在执行中
- `completed`: 任务执行完成
- `failed`: 任务执行失败

## 故障排除

### 常见问题

1. **连接被拒绝**
   ```bash
   # 检查后端服务是否启动
   python api_cli.py --action health
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   python api_cli.py --action db-test
   ```

3. **任务执行超时**
   ```bash
   # 增加等待超时时间
   python api_cli.py --action wait --thread-id <thread_id> --timeout 1800
   ```

### 调试模式

如果遇到问题，可以查看详细的错误信息：

```bash
# 使用Python的详细错误输出
python -u api_cli.py --action create --input "测试"
```

## 注意事项

1. **线程ID管理**: 每次创建任务都会生成新的 `thread_id`，需要妥善保存用于后续操作
2. **超时设置**: 长时间运行的分析任务建议设置合适的超时时间
3. **资源控制**: 可以通过 `--max-workers` 参数控制并发度，避免资源过载
4. **错误处理**: CLI工具会自动处理网络错误和API错误，并提供友好的错误信息

## 扩展功能

CLI工具基于 `QFlowAgentCLI` 类实现，可以轻松扩展新功能：

```python
# 示例：添加新的API调用
async def custom_api_call(self, endpoint: str):
    response = await self.client.get(f"{self.base_url}/{endpoint}")
    return response.json()
```

## 联系支持

如果遇到问题或需要新功能，请参考项目文档或联系开发团队。
