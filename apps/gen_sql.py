from src.langgraph.zego_tools.sql_generator.generate_sql import generate_sql
from src.langgraph.zego_tools.sql_generator.params import DataQueryParams


if __name__ == "__main__":
    data_query_params = DataQueryParams(
        metric_name="拉流错误码分布",
        # drilldown_dimension="isp",
        appid_filter="2067045686",
        # country_filter="美国",
        where='isp="t-mobile"',
    )
    print(generate_sql(data_query_params))
