import asyncio

from src.storage.database import get_session
from src.storage.services.task_service import TaskService

query_id = "0e55fc62-f21e-4148-928c-9cb8fb975e00"


async def main():
    async with get_session() as db_session:
        task_service = TaskService(db_session)
        xx = await task_service.get_task_with_relations(query_id)
        print(xx)
        print(xx.plans)
        print(xx.plans[0].steps)
        print(xx.plans[0].steps[0].results)


if __name__ == "__main__":
    asyncio.run(main())
