-- Active: 1747627759014@@127.0.0.1@5433@qagent

-- 查看所有schema
SELECT schema_name
FROM information_schema.schemata;

-- 查看当前数据库中所有表名
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
ORDER BY table_name;
-- workspaces
-- tasks
-- task_plans
-- task_steps
-- task_results

select * from workspaces;
select workspace_id,count(*) as 任务数 from tasks group by workspace_id;
select task_id,count(*) as 计划数 from task_plans group by task_id;
select task_id,count(*) as 步骤数 from task_steps group by task_id;
select task_id,count(*) as 结果数 from task_results group by task_id;


select * from tasks order by timestamp desc limit 10;
select * from task_plans order by timestamp desc limit 10;
select * from task_steps order by timestamp desc limit 10;
select * from task_results order by timestamp desc limit 10;


select * from task_steps where id = 8;
