
# QFlowAgent API 快速测试文档

## 📋 目录
- [服务器启动](#服务器启动)
- [健康检查](#健康检查)
- [工作空间管理](#工作空间管理)
- [任务管理](#任务管理)
- [流式监控](#流式监控)
- [常用测试场景](#常用测试场景)

## 🚀 服务器启动

### 启动API服务器
```bash
# 方式1：直接运行
uv run python src/api/main.py

# 方式2：使用脚本
./scripts/run_server.sh

# 方式3：启动前后端
./scripts/run_web.sh  # 启动前端
./scripts/run_server.sh  # 启动后端
```

### 服务器信息
- **Base URL**: `http://localhost:2026`
- **API文档**: `http://localhost:2026/docs`
- **前端界面**: `http://localhost:3000`

---

## 🏥 健康检查

### 基础健康检查
```bash
# 简单健康检查
curl -s http://localhost:2026/health/simple | jq .

# 完整健康检查
curl -s http://localhost:2026/health/ | jq .

# 存储系统检查
curl -s http://localhost:2026/health/storage | jq .
```

### 预期响应
```json
{
  "status": "ok",
  "message": "QFlowAgent API is healthy",
  "version": "3.0.0"
}
```

---

## 🏢 工作空间管理

### 创建工作空间
```bash
curl -s -X 'POST' 'http://localhost:2026/api/workspaces/' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "测试工作空间",
    "description": "用于API测试的工作空间"
  }' | jq .
```

### 获取工作空间列表
```bash
curl -s http://localhost:2026/api/workspaces/ | jq .
```

### 获取工作空间详情
```bash
curl -s http://localhost:2026/api/workspaces/1 | jq .
```

### 更新工作空间
```bash
curl -s -X 'PUT' 'http://localhost:2026/api/workspaces/1' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "更新后的工作空间",
    "description": "更新描述"
  }' | jq .
```

### 删除工作空间
```bash
curl -s -X 'DELETE' 'http://localhost:2026/api/workspaces/1' | jq .
```

---

## 📋 任务管理

### 创建任务

#### 基础任务创建
```bash
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'accept: application/json' -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析1850816294推流成功率",
    "workspace_id": 0,
    "max_parallel_workers": 5,
    "recursion_limit": 4
  }' | jq .
```

#### 启用Mock模式任务
```bash
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'accept: application/json' -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析推流成功率趋势",
    "workspace_id": 0,
    "max_parallel_workers": 3,
    "recursion_limit": 10,
    "configurable": {
      "is_config_mock_mode": true
    }
  }' | jq .
```

#### 指定LLM提供商
```bash
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'accept: application/json' -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析登录成功率",
    "workspace_id": 0,
    "max_parallel_workers": 5,
    "recursion_limit": 15,
    "configurable": {
      "model_provider": "deepseek"
    }
  }' | jq .
```

### 批量创建任务
```bash
curl -s -X 'POST' 'http://localhost:2026/api/tasks/batch-create' \
  -H 'Content-Type: application/json' \
  -d '{
    "tasks": [
      {
        "user_input": "分析推流成功率趋势",
        "max_parallel_workers": 3,
        "recursion_limit": 25
      },
      {
        "user_input": "分析推流错误码分布",
        "max_parallel_workers": 5,
        "recursion_limit": 30
      },
      {
        "user_input": "分析推流调度成功率",
        "max_parallel_workers": 4,
        "recursion_limit": 20
      }
    ]
  }' | jq .
```

### 获取任务列表
```bash
# 获取所有任务（分页）
curl -s "http://localhost:2026/api/tasks/?page=1&page_size=10" | jq .

# 获取特定工作空间的任务
curl -s "http://localhost:2026/api/tasks/?workspace_id=1&page=1&page_size=5" | jq .
```

### 获取任务详情
```bash
# 替换 TASK_ID 为实际的任务ID
curl -s http://localhost:2026/api/tasks/TASK_ID | jq .
```

### 获取任务增量更新
```bash
# 获取最新的增量更新
curl -s http://localhost:2026/api/tasks/TASK_ID/incremental | jq .

# 获取指定序列号之后的更新
curl -s "http://localhost:2026/api/tasks/TASK_ID/incremental?since_sequence=5" | jq .
```

---

## 📡 流式监控

### 实时任务流监控
```bash
# 监控任务执行状态（Server-Sent Events）
curl -s -N http://localhost:2026/api/tasks/TASK_ID/stream
```

### 使用curl监控完整流程
```bash
# 1. 创建任务并获取task_id
TASK_RESPONSE=$(curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'accept: application/json' -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析推流成功率",
    "workspace_id": 0,
    "max_parallel_workers": 3,
    "recursion_limit": 10
  }')

TASK_ID=$(echo $TASK_RESPONSE | jq -r '.query_id')
echo "Task ID: $TASK_ID"

# 2. 等待一段时间让任务开始执行
sleep 2

# 3. 监控任务执行（会持续输出事件）
curl -s -N http://localhost:2026/api/tasks/$TASK_ID/stream
```

---

## 🎯 常用测试场景

### 推流分析场景
```bash
# 1. 推流成功率分析
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析1850816294推流成功率",
    "max_parallel_workers": 5,
    "recursion_limit": 15
  }' | jq .

# 2. 推流错误码分布
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析推流错误码分布",
    "max_parallel_workers": 4,
    "recursion_limit": 12
  }' | jq .

# 3. 推流调度成功率
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析推流调度成功率",
    "max_parallel_workers": 3,
    "recursion_limit": 10
  }' | jq .
```

### 拉流分析场景
```bash
# 拉流成功率分析
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析拉流成功率",
    "max_parallel_workers": 5,
    "recursion_limit": 15
  }' | jq .

# 拉流错误码分布
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析拉流错误码分布",
    "max_parallel_workers": 4,
    "recursion_limit": 12
  }' | jq .

# 拉流丢包率分析
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析拉流丢包率",
    "max_parallel_workers": 3,
    "recursion_limit": 10
  }' | jq .
```

### 登录分析场景
```bash
# 登录成功率分析
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析登录成功率",
    "max_parallel_workers": 3,
    "recursion_limit": 10
  }' | jq .

# 登录错误码分布
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析登录错误码分布",
    "max_parallel_workers": 4,
    "recursion_limit": 12
  }' | jq .
```

### Mock模式测试
```bash
# 使用Mock模式快速测试（不需要真实数据）
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析系统性能指标",
    "max_parallel_workers": 2,
    "recursion_limit": 5,
    "configurable": {
      "is_config_mock_mode": true
    }
  }' | jq .
```

---

## 🔧 高级配置参数

### Configurable参数说明
```json
{
  "configurable": {
    "is_config_mock_mode": true,        // 启用Mock模式
    "model_provider": "deepseek",       // 指定LLM提供商
    "max_parallel_workers": 5,          // 最大并发数
    "recursion_limit": 15,              // 递归限制
    "workspace_path": "/path/to/workspace"  // 工作空间路径
  }
}
```

### 错误处理
```bash
# 测试错误场景
curl -s -X 'POST' 'http://localhost:2026/api/tasks/' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "",
    "max_parallel_workers": -1,
    "recursion_limit": 0
  }' | jq .
```

---

## 📝 快速测试脚本

### 一键测试脚本
```bash
#!/bin/bash
# test_api.sh - QFlowAgent API快速测试脚本

BASE_URL="http://localhost:2026"

echo "=== QFlowAgent API 测试 ==="

# 1. 健康检查
echo "1. 健康检查..."
curl -s $BASE_URL/health/simple | jq .

# 2. 创建测试工作空间
echo "2. 创建测试工作空间..."
curl -s -X POST $BASE_URL/api/workspaces/ \
  -H 'Content-Type: application/json' \
  -d '{"name": "测试工作空间", "description": "API测试"}' | jq .

# 3. 创建测试任务
echo "3. 创建测试任务..."
TASK_RESPONSE=$(curl -s -X POST $BASE_URL/api/tasks/ \
  -H 'Content-Type: application/json' \
  -d '{
    "user_query": "分析推流成功率",
    "max_parallel_workers": 2,
    "recursion_limit": 5,
    "configurable": {"is_config_mock_mode": true}
  }')

echo $TASK_RESPONSE | jq .
TASK_ID=$(echo $TASK_RESPONSE | jq -r '.query_id')

if [ "$TASK_ID" != "null" ]; then
    echo "4. 获取任务详情..."
    sleep 1
    curl -s $BASE_URL/api/tasks/$TASK_ID | jq .
fi

echo "=== 测试完成 ==="
```

### 保存并运行
```bash
# 保存为可执行脚本
chmod +x test_api.sh
./test_api.sh
```

---

## 💡 使用技巧

1. **使用jq格式化输出**: 在所有curl命令后添加`| jq .`来格式化JSON输出
2. **保存响应**: 使用`-o response.json`保存响应到文件
3. **详细输出**: 添加`-v`参数查看详细的HTTP请求/响应信息
4. **静默模式**: 使用`-s`参数抑制进度条和错误信息
5. **并发测试**: 使用`&`后台运行，或使用工具如`ab`、`wrk`进行压力测试

## 🔍 故障排查

### 常见错误码
- `404`: 资源不存在
- `422`: 请求参数错误
- `500`: 服务器内部错误

### 查看服务器日志
```bash
# 查看API服务器日志
tail -f logs/api.log

# 查看数据库连接状态
curl -s http://localhost:2026/health/storage | jq .
```

---

*最后更新: $(date)*

