"""
QFlowAgent 数据库连接测试工具

这个工具用于测试和验证数据库连接，特别是SOCKS代理的配置。
支持多种测试模式，包括基础连接测试、API环境模拟、性能测试等。
"""

import asyncio
import os

from dotenv import load_dotenv

from src.langgraph.zego_tools.ocean_executor import (
    execute_sql_async,
    ocean_connection,
    SqlExecutorQueryResult,
    themis_connection,
)


async def test_connection(db_type: str, sql: str = "SELECT 1 as test") -> bool:
    """测试数据库连接"""
    connection = ocean_connection if db_type == "ocean" else themis_connection

    print(f"\n🔍 测试 {db_type.upper()} 数据库连接...")

    # 测试连接
    is_connected = await connection.test_connection(timeout_seconds=10)
    if not is_connected:
        print(f"❌ {db_type.upper()} 连接失败")
        return False

    print(f"✅ {db_type.upper()} 连接成功")

    # 执行测试查询
    try:
        db_param = "sdk" if db_type == "themis" else None
        result: SqlExecutorQueryResult = await execute_sql_async(
            db_type, sql=sql, max_retries=2, base_delay=0.5, themis_db=db_param
        )

        if result.is_successful:
            print(f"✅ {db_type.upper()} 查询成功: {len(result.data)} 条记录")
            if len(result.data) > 0:
                print(f"📊 数据预览:\n{result.data.to_string()}")
            return True
        else:
            print(f"❌ {db_type.upper()} 查询失败: {result.error_message}")
            return False

    except Exception as e:
        print(f"❌ {db_type.upper()} 查询异常: {e}")
        return False


async def test_basic_connectivity():
    """基础连接测试"""
    print("🚀 基础数据库连接测试")
    print("=" * 50)

    # 加载环境变量
    load_dotenv()
    db_env_path = os.path.join(
        os.path.dirname(__file__), "..", "src", "langgraph", "zego_tools", "ocean_executor", ".env"
    )
    load_dotenv(db_env_path)

    # 测试Themis（直接连接）
    themis_ok = await test_connection("themis")

    # 测试Ocean（SOCKS代理）
    ocean_ok = await test_connection("ocean")

    # 总结
    print("\n📋 测试结果总结")
    print("=" * 30)
    print(f"Themis数据库: {'✅ 正常' if themis_ok else '❌ 失败'}")
    print(f"Ocean数据库:  {'✅ 正常' if ocean_ok else '❌ 失败'}")

    return themis_ok and ocean_ok


async def test_api_environment():
    """模拟API服务环境测试"""
    print("🔧 API服务环境模拟测试")
    print("=" * 50)

    # 模拟API服务的完整初始化过程
    load_dotenv()
    db_env_path = os.path.join(
        os.path.dirname(__file__), "..", "src", "langgraph", "zego_tools", "ocean_executor", ".env"
    )
    load_dotenv(db_env_path)

    # 初始化socket路由（模拟API服务启动）
    from src.langgraph.common.utils.socket_router import _patch_loop_instance, ensure_socket_patched

    ensure_socket_patched()

    # 模拟API服务中的事件循环patching
    current_loop = asyncio.get_running_loop()
    _patch_loop_instance(current_loop)
    print("✅ Socket patching 和事件循环patching已应用")

    # 模拟HTTP请求处理
    async def simulate_http_request():
        print("\n🌐 模拟HTTP请求处理...")

        # 测试Ocean连接（需要SOCKS上下文）
        from src.langgraph.common.utils.socket_router import db_socks_context

        async with db_socks_context():
            ocean_ok = await test_connection("ocean", "SELECT COUNT(*) as total FROM information_schema.tables")

        # 测试Themis连接（直接连接）
        themis_ok = await test_connection("themis", "SELECT 1 as health_check")

        return ocean_ok and themis_ok

    # 使用asyncio.create_task模拟并发HTTP请求
    success = await asyncio.create_task(simulate_http_request())

    print(f"\n🎯 API环境测试结果: {'✅ 成功' if success else '❌ 失败'}")
    return success


async def main():
    """主函数 - 根据参数选择测试模式"""

    await test_basic_connectivity()
    await test_api_environment()


if __name__ == "__main__":
    asyncio.run(main())
