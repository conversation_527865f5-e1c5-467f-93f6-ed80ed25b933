import asyncio
import logging
import os
import uuid
from pathlib import Path

from langchain_core.messages import HumanMessage

from src.langgraph.common.init_log import init_log

# zego_graph已废弃，使用workflow_v2
from src.langgraph.graph_v2 import workflow_v2
from src.storage.database import get_session
from src.storage.services import TaskService, WorkspaceService

init_log()


thread_id = f"{os.path.basename(__file__)}_{str(uuid.uuid4())}"
query_id = f"cli_{str(uuid.uuid4())}"
config = {
    "configurable": {
        "query_id": query_id,
        "thread_id": thread_id,
        "max_parallel_workers": 5,
        "mock_mode": 1,
        "mock_delay": 0.3,
    },
    "recursion_limit": 4,
}


logger = logging.getLogger(__name__)


async def setup_database_context(user_query: str):
    """设置数据库上下文，创建workspace和task记录"""
    from src.storage.database import init_database

    await init_database()
    async with get_session() as db_session:
        workspace_service = WorkspaceService(db_session)
        task_service = TaskService(db_session)

        # 创建或获取默认工作空间
        try:
            workspace = await workspace_service.create_workspace(
                name="CLI Workspace", description="Command line interface workspace", config={}
            )
        except ValueError:
            # 如果工作空间已存在，获取它
            workspace = await workspace_service.get_workspace_by_name("CLI Workspace")

        task = await task_service.create_task(
            query_id=query_id,
            user_query=user_query,
            workspace_id=workspace.id,
            thread_id=thread_id,
            config=config,
        )

        return config


# values: 每个节点触发一次，并行节点合并触发一次；将state返回上来
# message: human的没有，llm请求是流式的token by token;
# updates: 返回每个节点的upate内容
async def main():
    async def stream_graph_updates(user_input: str):
        # 设置数据库上下文
        _config = await setup_database_context(user_input)

        input_state = {"messages": [HumanMessage(content=user_input)]}
        stream_mode = ["values", "messages", "updates", "custom"]
        async for ev in workflow_v2.astream(input_state, _config, stream_mode=stream_mode, debug=False):
            mode, event = ev
            print(f"\n\n### {mode} start ====================================================================\n\n")
            print(f"event: {str(event)[:1000]}")
            print(f"\n\n### {mode} end   ====================================================================\n\n")

    # 测试简化版数据库连接
    print("=== 测试简化版数据库连接（完全替换后）===")
    try:
        # 现在直接从模块导入，应该是简化版本
        from src.langgraph.zego_tools.ocean_executor import execute_sql_async, ocean_connection

        # 测试连接
        print("测试数据库连接...")
        is_connected = await ocean_connection.test_connection()
        if is_connected:
            print("✅ 数据库连接成功")
        else:
            print("❌ 数据库连接失败, 直接退出")
            exit(-1)

        # 测试SQL执行
        print("测试SQL执行...")
        result = await execute_sql_async("ocean", "SELECT 1 as test")
        if result.is_successful:
            print(f"✅ SQL执行成功: {result.data}")
        else:
            print(f"❌ SQL执行失败: {result.error_message}")

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback

        traceback.print_exc()

    print("=== 数据库测试完成，开始正常流程 ===")

    # await stream_graph_updates("""看下阿联酋 966811601 的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    # await stream_graph_updates("""看下沙特阿拉伯 3206531758 的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    # await stream_graph_updates("""看下沙特阿拉伯 997297939 的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    # await stream_graph_updates("""看下 英国的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    await stream_graph_updates("""分析下 1850816294 推流成功率下降的问题""")


asyncio.run(main())
