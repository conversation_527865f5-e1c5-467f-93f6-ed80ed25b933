"""
统一依赖注入模块
极简设计，只包含核心依赖
"""

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.storage.database import get_db_session
from src.storage.services import TaskService, WorkspaceService


# === 服务依赖 ===


async def get_task_service(db: AsyncSession = Depends(get_db_session)) -> TaskService:
    """获取任务服务实例"""
    return TaskService(db)


async def get_workspace_service(db: AsyncSession = Depends(get_db_session)) -> WorkspaceService:
    """获取工作空间服务实例"""
    return WorkspaceService(db)


# === 业务依赖 ===


async def ensure_default_workspace(workspace_service: WorkspaceService = Depends(get_workspace_service)) -> int:
    """确保默认工作空间存在并返回ID"""
    try:
        workspace = await workspace_service.get_workspace_by_name("Default Workspace")
        if workspace:
            return workspace.id
    except Exception:
        pass

    # 创建默认工作空间
    workspace = await workspace_service.create_workspace(
        name="Default Workspace", description="Default workspace", config={}
    )
    return workspace.id
