"""
API 工作空间管理接口
简化的工作空间管理，专注于核心功能
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field

from src.api.dependencies import get_workspace_service
from src.storage.services import WorkspaceService

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Workspaces"])


# === 请求/响应模型 ===


class WorkspaceCreateRequest(BaseModel):
    """创建工作空间请求"""

    name: str = Field(..., description="工作空间名称")
    description: Optional[str] = Field(None, description="工作空间描述")


class WorkspaceUpdateRequest(BaseModel):
    """更新工作空间请求"""

    name: Optional[str] = Field(None, description="工作空间名称")
    description: Optional[str] = Field(None, description="工作空间描述")


class WorkspaceResponse(BaseModel):
    """工作空间响应"""

    id: int = Field(..., description="工作空间ID")
    name: str = Field(..., description="工作空间名称")
    description: Optional[str] = Field(None, description="工作空间描述")
    task_count: int = Field(0, description="任务数量")
    created_at: str = Field(..., description="创建时间")


class WorkspaceListResponse(BaseModel):
    """工作空间列表响应"""

    workspaces: List[WorkspaceResponse] = Field(..., description="工作空间列表")
    total_count: int = Field(..., description="总数量")


# === API接口 ===


@router.post("/", response_model=WorkspaceResponse, status_code=201)
async def create_workspace(
    request: WorkspaceCreateRequest, workspace_service: WorkspaceService = Depends(get_workspace_service)
):
    """创建新工作空间"""
    try:
        workspace = await workspace_service.create_workspace(
            name=request.name, description=request.description or "", config={}
        )

        return WorkspaceResponse(
            id=workspace.id,
            name=workspace.name,
            description=workspace.description,
            task_count=0,  # 新创建的工作空间任务数为0
            created_at=workspace.created_at.isoformat(),
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to create workspace: {e}")
        raise HTTPException(status_code=500, detail=f"创建工作空间失败: {str(e)}")


@router.get("/", response_model=WorkspaceListResponse)
async def list_workspaces(workspace_service: WorkspaceService = Depends(get_workspace_service)):
    """获取工作空间列表"""
    try:
        workspaces, total_count = await workspace_service.list_workspaces()

        workspace_responses = []
        for workspace in workspaces:
            # 获取任务统计
            stats = await workspace_service.get_workspace_stats(workspace.id)

            workspace_responses.append(
                WorkspaceResponse(
                    id=workspace.id,
                    name=workspace.name,
                    description=workspace.description,
                    task_count=stats.get("task_count", 0),
                    created_at=workspace.created_at.isoformat(),
                )
            )

        return WorkspaceListResponse(workspaces=workspace_responses, total_count=total_count)

    except Exception as e:
        logger.error(f"Failed to list workspaces: {e}")
        raise HTTPException(status_code=500, detail=f"获取工作空间列表失败: {str(e)}")


@router.get("/{workspace_id}", response_model=WorkspaceResponse)
async def get_workspace(workspace_id: int, workspace_service: WorkspaceService = Depends(get_workspace_service)):
    """获取工作空间详情"""
    try:
        workspace = await workspace_service.get_workspace_by_id(workspace_id)
        if not workspace:
            raise HTTPException(status_code=404, detail=f"工作空间 '{workspace_id}' 不存在")

        # 获取任务统计
        stats = await workspace_service.get_workspace_stats(workspace_id)

        return WorkspaceResponse(
            id=workspace.id,
            name=workspace.name,
            description=workspace.description,
            task_count=stats.get("task_count", 0),
            created_at=workspace.created_at.isoformat(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get workspace: {e}")
        raise HTTPException(status_code=500, detail=f"获取工作空间失败: {str(e)}")


@router.put("/{workspace_id}", response_model=WorkspaceResponse)
async def update_workspace(
    workspace_id: int,
    request: WorkspaceUpdateRequest,
    workspace_service: WorkspaceService = Depends(get_workspace_service),
):
    """更新工作空间"""
    try:
        workspace = await workspace_service.update_workspace(
            workspace_id=workspace_id, name=request.name, description=request.description, config=None  # 保持原有配置
        )

        if not workspace:
            raise HTTPException(status_code=404, detail=f"工作空间 '{workspace_id}' 不存在")

        # 获取任务统计
        stats = await workspace_service.get_workspace_stats(workspace_id)

        return WorkspaceResponse(
            id=workspace.id,
            name=workspace.name,
            description=workspace.description,
            task_count=stats.get("task_count", 0),
            created_at=workspace.created_at.isoformat(),
        )

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to update workspace: {e}")
        raise HTTPException(status_code=500, detail=f"更新工作空间失败: {str(e)}")


@router.get("/{workspace_id}/stats")
async def get_workspace_stats(workspace_id: int, workspace_service: WorkspaceService = Depends(get_workspace_service)):
    """获取工作空间统计信息"""
    try:
        # 检查工作空间是否存在
        workspace = await workspace_service.get_workspace_by_id(workspace_id)
        if not workspace:
            raise HTTPException(status_code=404, detail=f"工作空间 '{workspace_id}' 不存在")

        # 获取详细统计信息
        stats = await workspace_service.get_workspace_stats(workspace_id)

        return stats

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get workspace stats: {e}")
        raise HTTPException(status_code=500, detail=f"获取工作空间统计失败: {str(e)}")


@router.delete("/{workspace_id}")
async def delete_workspace(workspace_id: int, workspace_service: WorkspaceService = Depends(get_workspace_service)):
    """删除工作空间"""
    try:
        # 检查工作空间是否存在
        workspace = await workspace_service.get_workspace_by_id(workspace_id)
        if not workspace:
            raise HTTPException(status_code=404, detail=f"工作空间 '{workspace_id}' 不存在")

        # 检查是否有关联的任务
        stats = await workspace_service.get_workspace_stats(workspace_id)
        if stats.get("total_tasks", 0) > 0:
            raise HTTPException(
                status_code=400, detail=f"工作空间 '{workspace_id}' 还有 {stats['total_tasks']} 个任务，无法删除"
            )

        # 删除工作空间
        success = await workspace_service.delete_workspace(workspace_id)
        if not success:
            raise HTTPException(status_code=500, detail="删除工作空间失败")

        return {"message": f"工作空间 '{workspace_id}' 已成功删除"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete workspace: {e}")
        raise HTTPException(status_code=500, detail=f"删除工作空间失败: {str(e)}")
