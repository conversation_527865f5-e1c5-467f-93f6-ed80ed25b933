"""
API 任务管理接口
基于workflow_v2和SQLModel存储，简化API层逻辑
"""

import asyncio
import logging
import time
import uuid
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field, TypeAdapter

from src.api.dependencies import ensure_default_workspace, get_task_service
from src.langgraph.graph_v2 import workflow_v2
from src.storage.database import get_session
from src.storage.enums import TaskStatus
from src.storage.models.sqlmodel_task import TaskModel
from src.storage.pg_notifier import get_pg_notifier
from src.storage.services import TaskService
from src.storage.services.storage_service import db_get_task_with_relations, db_get_tasks_by_workspace_id

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Tasks"])


# === 请求/响应模型 ===
class TaskCreateRequest(BaseModel):
    """创建任务请求"""

    user_query: str = Field(..., description="用户查询内容")
    workspace_id: Optional[int] = Field(None, description="工作空间ID，不提供则使用默认工作空间")
    max_parallel_workers: int = Field(5, description="最大并发工作数")
    recursion_limit: int = Field(4, description="递归限制")
    configurable: Optional[Dict[str, Any]] = Field(None, description="可配置参数，支持mock_mode等")


class TaskListResponse(BaseModel):
    """任务列表响应"""

    tasks: list[TaskModel] = Field(..., description="任务列表")
    total_count: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    page_size: int = Field(..., description="页大小")


class BatchTaskCreateRequest(BaseModel):
    """批量创建任务请求"""

    user_queries: list[str] = Field(..., description="批量的用户查询内容")
    workspace_id: Optional[int] = Field(None, description="工作空间ID，不提供则使用默认工作空间")
    max_parallel_workers: int = Field(5, description="最大并发工作数")
    recursion_limit: int = Field(4, description="递归限制")
    configurable: Optional[Dict[str, Any]] = Field(None, description="可配置参数，支持mock_mode等")


class BatchTaskCreateResponse(BaseModel):
    """批量创建任务响应"""

    total_tasks: int = Field(..., description="总任务数")
    created_tasks: list[TaskModel] = Field(..., description="成功创建的任务ID列表")
    failed_tasks: list[Dict[str, Any]] = Field(..., description="创建失败的任务信息")
    success_count: int = Field(..., description="成功创建的任务数量")
    failed_count: int = Field(..., description="创建失败的任务数量")


@router.get("/", response_model=TaskListResponse)
async def list_tasks(
    workspace_id: int,  # 改为必需参数，移除 Optional
    page: int = 1,
    page_size: int = 20,
    task_service: TaskService = Depends(get_task_service),
):
    """获取任务列表"""
    try:
        tasks, total_count = await db_get_tasks_by_workspace_id(workspace_id, page, page_size)

        rsp = TaskListResponse(tasks=tasks, total_count=total_count, page=page, page_size=page_size)
        return rsp

    except Exception as e:
        logger.error(f"Failed to list tasks: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}") from e


@router.post("/", response_model=TaskModel, status_code=201)
async def create_task(
    request: TaskCreateRequest,
    task_service: TaskService = Depends(get_task_service),
    default_workspace_id: int = Depends(ensure_default_workspace),
):
    """创建并启动新任务"""
    try:
        # 确定工作空间ID
        workspace_id = request.workspace_id or default_workspace_id

        # 生成唯一标识
        query_id = f"{str(uuid.uuid4())}"
        thread_id = f"{str(uuid.uuid4())}"

        # 准备workflow_v2配置
        configurable = {
            "query_id": query_id,
            "thread_id": thread_id,
            "max_parallel_workers": request.max_parallel_workers,
            "workspace_id": workspace_id,
        }

        # 合并用户提供的configurable参数
        if request.configurable:
            configurable.update(request.configurable)
            logger.info(f"[create_task] 使用用户配置: {request.configurable}")

        config = {
            "configurable": configurable,
            "recursion_limit": request.recursion_limit,
        }

        # 创建任务记录
        task_model = await task_service.create_task(
            query_id=query_id,
            user_query=request.user_query,
            workspace_id=workspace_id,
            thread_id=thread_id,
            config=config,
        )

        # 后台启动workflow_v2执行（使用新的数据库会话）
        input_state = {"messages": [HumanMessage(content=request.user_query)]}
        asyncio.create_task(_execute_workflow_v2(input_state, config, query_id))

        return task_model

    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}") from e


@router.get("/{query_id}", response_model=dict)
async def get_task(query_id: str, task_service: TaskService = Depends(get_task_service)):
    """获取任务详情"""
    try:
        task_detail = await db_get_task_with_relations(query_id)
        if not task_detail:
            raise HTTPException(status_code=404, detail=f"任务 '{query_id}' 不存在")

        task_full_dict = task_detail.to_full_dict()
        return task_full_dict

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务失败: {str(e)}") from e


@router.post("/batch-create", response_model=BatchTaskCreateResponse, status_code=201)
async def batch_create_tasks(
    request: BatchTaskCreateRequest,
    task_service: TaskService = Depends(get_task_service),
    default_workspace_id: int = Depends(ensure_default_workspace),
):
    """批量创建并启动任务"""
    try:
        created_tasks = []
        failed_tasks = []

        logger.info(f"Starting batch task creation: {len(request.user_queries)} tasks")

        for i, user_query in enumerate(request.user_queries):
            try:

                # 确定工作空间ID
                workspace_id = request.workspace_id or default_workspace_id

                # 生成唯一标识
                query_id = f"{str(uuid.uuid4())}"
                thread_id = f"{str(uuid.uuid4())}"

                # 准备workflow_v2配置
                configurable = {
                    "query_id": query_id,
                    "thread_id": thread_id,
                    "max_parallel_workers": request.max_parallel_workers,
                    "workspace_id": workspace_id,
                }

                # 合并用户提供的configurable参数
                if request.configurable:
                    configurable.update(request.configurable)
                    logger.info(f"[create_task] 使用用户配置: {request.configurable}")

                config = {
                    "configurable": configurable,
                    "recursion_limit": request.recursion_limit,
                }

                # 创建任务记录
                task_model = await task_service.create_task(
                    query_id=query_id,
                    user_query=user_query,
                    workspace_id=workspace_id,
                    thread_id=thread_id,
                    config=config,
                )

                # 后台启动workflow_v2执行（使用新的数据库会话）
                input_state = {"messages": [HumanMessage(content=user_query)]}
                asyncio.create_task(_execute_workflow_v2(input_state, config, query_id))

                created_tasks.append(task_model)
                logger.info(f"Batch task {i+1}/{len(request.user_queries)} created: {query_id}")

            except Exception as e:
                logger.error(f"Failed to create batch task {i+1}: {e}")
                failed_tasks.append({"index": i, "user_input": user_query, "error": str(e)})

        response = BatchTaskCreateResponse(
            total_tasks=len(request.user_queries),
            created_tasks=created_tasks,
            failed_tasks=failed_tasks,
            success_count=len(created_tasks),
            failed_count=len(failed_tasks),
        )

        logger.info(f"Batch task creation completed: {len(created_tasks)} success, {len(failed_tasks)} failed")
        return response

    except Exception as e:
        logger.error(f"Batch task creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"批量创建任务失败: {str(e)}") from e


@router.get("/{query_id}/stream")
async def stream_task_execution(query_id: str, task_service: TaskService = Depends(get_task_service)):
    """流式获取任务执行状态（Server-Sent Events）"""
    try:

        async def event_generator():
            import asyncio
            import time

            pg_notifier = None
            notification_queue = None

            try:
                # 检查任务是否存在
                async with get_session() as db:
                    task_service_local = TaskService(db)
                    task = await task_service_local.get_task_by_query_id(query_id)
                if not task:
                    error_event = {
                        "type": "error",
                        "query_id": query_id,
                        "value": {"error": "Task not found"},
                        "timestamp": time.time(),
                    }
                    yield f"data: {TypeAdapter(Dict[str, Any]).dump_json(error_event).decode()}\n\n"
                    return

                # 发送开始事件
                start_event = {
                    "type": "custom",
                    "query_id": query_id,
                    "value": {"status": "started", "message": "Task execution started", "task_id": query_id},
                    "timestamp": time.time(),
                }
                yield f"data: {TypeAdapter(Dict[str, Any]).dump_json(start_event).decode()}\n\n"

                # 创建通知队列
                notification_queue = asyncio.Queue()

                # 定义通知处理器
                async def notification_handler(data):
                    assert data.get("query_id") == query_id, f"query_id mismatch: {data.get('query_id')} != {query_id}"
                    await notification_queue.put(data)

                # 获取PG通知器并开始监听
                pg_notifier = await get_pg_notifier()
                async with pg_notifier.listen_context(f"channel_{query_id}", notification_handler):
                    # 监听通知和超时
                    max_wait_time = 300  # 最多等待5分钟
                    start_time = time.time()

                    while time.time() - start_time < max_wait_time:
                        try:
                            # 等待通知，设置超时
                            notification = await asyncio.wait_for(notification_queue.get(), timeout=60.0)

                            # 处理通知 - 根据通知类型决定是否包含完整状态数据
                            event_value = notification

                            # 如果是计划创建或任务更新通知，获取最新的任务状态
                            if notification.get("type") in ["plan_record", "step_record", "result_record"]:
                                try:
                                    # 获取最新的任务状态
                                    async with get_session() as db:
                                        task_with_plans = await db_get_task_with_relations(query_id)
                                        task_full_dict = task_with_plans.to_full_dict()
                                        # 将完整的任务状态包含在事件中
                                        event_value = {**notification, "task_state": task_full_dict}
                                except Exception as e:
                                    logger.error(f"Failed to get updated task state for {query_id}: {e}")
                                    raise e

                            # 处理通知
                            event = {
                                "type": "custom",  # TODO 多余字段去掉
                                "query_id": query_id,
                                "value": event_value,
                                "timestamp": time.time(),
                            }
                            yield f"data: {TypeAdapter(Dict[str, Any]).dump_json(event).decode()}\n\n"

                            # 如果是任务完成通知，退出循环
                            if notification.get("type") == "task_completed":
                                complete_event = {
                                    "type": "success",
                                    "query_id": query_id,
                                    "value": {"status": "completed", "message": "Task completed"},
                                    "timestamp": time.time(),
                                }
                                yield f"data: {TypeAdapter(Dict[str, Any]).dump_json(complete_event).decode()}\n\n"
                                break

                        except asyncio.TimeoutError:
                            # 超时，发送心跳事件
                            heartbeat_event = {
                                "type": "heartbeat",
                                "query_id": query_id,
                                "value": {"message": "Connection alive"},
                                "timestamp": time.time(),
                            }
                            yield f"data: {TypeAdapter(Dict[str, Any]).dump_json(heartbeat_event).decode()}\n\n"
                            continue

                        except asyncio.CancelledError:
                            # 客户端断开连接，记录日志但不抛出异常
                            logger.info(f"Client disconnected for query_id: {query_id}")
                            break

                        except Exception as e:
                            logger.error(f"Error processing notification for {query_id}: {e}")
                            error_event = {
                                "type": "error",
                                "query_id": query_id,
                                "value": {"error": str(e)},
                                "timestamp": time.time(),
                            }
                            yield f"data: {TypeAdapter(Dict[str, Any]).dump_json(error_event).decode()}\n\n"
                            break

                    # 如果超时，发送超时事件
                    if time.time() - start_time >= max_wait_time:
                        timeout_event = {
                            "type": "error",
                            "query_id": query_id,
                            "value": {"error": "Task execution timeout"},
                            "timestamp": time.time(),
                        }
                        yield f"data: {TypeAdapter(Dict[str, Any]).dump_json(timeout_event).decode()}\n\n"

            except asyncio.CancelledError:
                # 客户端断开连接，记录日志
                logger.info(f"Client disconnected for query_id: {query_id}")
            except Exception as e:
                logger.error(f"Error in event generator for {query_id}: {e}")
                error_event = {
                    "type": "error",
                    "query_id": query_id,
                    "value": {"error": str(e)},
                    "timestamp": time.time(),
                }
                yield f"data: {TypeAdapter(Dict[str, Any]).dump_json(error_event).decode()}\n\n"

        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control",
            },
        )

    except Exception as e:
        logger.error(f"Failed to stream task: {e}")
        raise HTTPException(status_code=500, detail=f"流式获取任务状态失败: {str(e)}") from e


# === 内部辅助函数 ===


async def _execute_workflow_v2(input_state: Dict[str, Any], config: Dict[str, Any], query_id: str):
    """后台执行workflow_v2"""
    try:

        logger.info(f"Starting workflow_v2 execution for query_id: {query_id}")

        # 使用新的数据库会话更新任务状态为运行中
        async with get_session() as db:
            task_service = TaskService(db)
            await task_service.update_task_status(query_id, TaskStatus.RUNNING)

        # 执行workflow_v2
        async for event in workflow_v2.astream(input_state, config, stream_mode=["values", "updates"]):
            mode, data = event
            logger.debug(f"Workflow event [{mode}]: {str(data)[:200]}...")

        # 使用新的数据库会话更新任务状态为完成
        async with get_session() as db:
            task_service = TaskService(db)
            await task_service.update_task_status(query_id, TaskStatus.COMPLETED)

        logger.info(f"Workflow_v2 execution completed for query_id: {query_id}")

    except Exception as e:
        logger.error(f"Workflow_v2 execution failed for query_id {query_id}: {e}")
        try:
            async with get_session() as db:
                task_service = TaskService(db)
                await task_service.update_task_status(query_id, TaskStatus.FAILED)

            # 发送任务失败通知
            from src.storage.pg_notifier import get_pg_notifier

            pg_notifier_todo_delme = await get_pg_notifier()
            await pg_notifier_todo_delme.notify(
                f"channel_{query_id}",
                {"type": "task_failed", "query_id": query_id, "error": str(e), "timestamp": time.time()},
            )
        except Exception:
            pass  # 避免二次异常
