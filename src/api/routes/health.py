"""
极简健康检查API
只保留核心功能
"""

import logging
import time
from typing import Any, Dict

from fastapi import APIRouter

logger = logging.getLogger(__name__)
router = APIRouter(tags=["health"])

# 记录应用启动时间
_app_start_time = time.time()


def _format_utc8_time(timestamp: float) -> str:
    """格式化时间为UTC+8格式"""
    # UTC时间 + 8小时 = 北京时间
    utc8_timestamp = timestamp + (8 * 3600)
    return time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime(utc8_timestamp))


def _get_uptime_info() -> Dict[str, Any]:
    """获取系统运行时间信息"""
    current_time = time.time()
    uptime_seconds = current_time - _app_start_time

    # 转换为可读格式
    days = int(uptime_seconds // 86400)
    hours = int((uptime_seconds % 86400) // 3600)
    minutes = int((uptime_seconds % 3600) // 60)
    seconds = int(uptime_seconds % 60)

    if days > 0:
        uptime_str = f"{days}d {hours:02d}h {minutes:02d}m {seconds:02d}s"
    elif hours > 0:
        uptime_str = f"{hours:02d}h {minutes:02d}m {seconds:02d}s"
    elif minutes > 0:
        uptime_str = f"{minutes:02d}m {seconds:02d}s"
    else:
        uptime_str = f"{seconds}s"

    return {
        "uptime_seconds": round(uptime_seconds, 2),
        "uptime_formatted": uptime_str,
        "start_time": _format_utc8_time(_app_start_time),
    }


async def _check_storage() -> tuple[bool, float]:
    """检查存储系统并返回响应时间"""
    import time

    try:
        from sqlalchemy import text

        from src.storage.database import get_session

        start_time = time.time()
        async with get_session() as session:
            result = await session.execute(text("SELECT 1"))
            response_time = time.time() - start_time
            return result.scalar() == 1, round(response_time * 1000, 2)  # 返回毫秒
    except Exception as e:
        logger.error(f"Storage check failed: {e}")
        return False, 0.0


@router.get("/")
async def get_health() -> Dict[str, Any]:
    """系统健康检查"""

    try:
        # 检查存储系统
        storage_ok, response_time = await _check_storage()
        # 使用UTC+8时间戳
        current_time = time.time()
        timestamp = _format_utc8_time(current_time)

        # 获取真正的uptime信息
        uptime_info = _get_uptime_info()

        # 综合评估
        all_healthy = storage_ok

        if all_healthy:
            return {
                "status": "ok",
                "message": "All systems operational",
                "timestamp": timestamp,
                "version": "3.0.0",
                "components": {
                    "storage": {
                        "status": "ok",
                        "message": "Database connection successful",
                        "response_time_ms": response_time,
                    }
                },
                "uptime": uptime_info,
            }
        else:
            return {
                "status": "error",
                "message": "System degradation detected",
                "timestamp": timestamp,
                "version": "3.0.0",
                "components": {
                    "storage": {
                        "status": "error",
                        "message": "Database connection failed",
                        "response_time_ms": response_time,
                    }
                },
                "uptime": uptime_info,
            }

    except Exception as e:
        logger.error(f"健康检查异常: {e}")
        current_time = time.time()
        timestamp = _format_utc8_time(current_time).replace(" UTC+8", "")
        uptime_info = _get_uptime_info()
        return {
            "status": "error",
            "message": "Health check execution failed",
            "timestamp": timestamp,
            "version": "3.0.0",
            "error": str(e),
            "uptime": uptime_info,
        }


@router.get("/simple")
async def get_simple_health() -> Dict[str, Any]:
    """最简单的健康检查"""
    current_time = time.time()
    timestamp = _format_utc8_time(current_time).replace(" UTC+8", "")

    return {
        "status": "ok",
        "message": "Service is running",
        "timestamp": timestamp,
        "version": "3.0.0",
    }


@router.get("/storage")
async def get_storage_health() -> Dict[str, Any]:
    """存储系统健康检查"""
    storage_ok, response_time = await _check_storage()
    current_time = time.time()
    timestamp = _format_utc8_time(current_time).replace(" UTC+8", "")

    if storage_ok:
        return {
            "status": "ok",
            "service": "storage",
            "message": "Database connection successful",
            "timestamp": timestamp,
            "details": {"connection": "healthy", "response_time_ms": response_time},
        }
    else:
        return {
            "status": "error",
            "service": "storage",
            "message": "Database connection failed",
            "timestamp": timestamp,
            "details": {"connection": "unhealthy", "response_time_ms": response_time},
        }
