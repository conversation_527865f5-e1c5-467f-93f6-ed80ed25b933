"""
问题库API接口
提供历史问题数据的RESTful接口
"""

import json
import logging
import os
from typing import List, Optional

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Issues"])


# === 数据模型 ===

class Issue(BaseModel):
    """问题模型"""
    id: str = Field(..., description="问题唯一标识")
    question: str = Field(..., description="问题内容")
    answer: str = Field(..., description="问题答案")
    category_type: str = Field(..., description="分类类型")
    category_name: str = Field(..., description="分类名称")


class IssueCategory(BaseModel):
    """问题分类模型"""
    type: str = Field(..., description="分类类型")
    name: str = Field(..., description="分类名称")
    issue_count: int = Field(..., description="问题数量")


class IssueListResponse(BaseModel):
    """问题列表响应"""
    data: List[Issue] = Field(..., description="问题列表")
    categories: List[IssueCategory] = Field(..., description="分类信息")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    limit: int = Field(..., description="每页数量")


class IssueDetailResponse(BaseModel):
    """问题详情响应"""
    issue: Issue = Field(..., description="问题详情")


# === 数据加载器 ===

class IssueDataLoader:
    """问题数据加载器"""
    
    def __init__(self):
        self._data = None
        self._issues_cache = None
        self._categories_cache = None
    
    def _load_data(self):
        """加载JSON数据"""
        if self._data is not None:
            return self._data
            
        try:
            # 获取JSON文件路径
            json_path = os.path.join(
                os.path.dirname(__file__), 
                "..", "..", "web", "src", "utils", "history_issue.json"
            )
            
            with open(json_path, 'r', encoding='utf-8') as f:
                self._data = json.load(f)
                
            logger.info(f"Successfully loaded issue data from {json_path}")
            return self._data
            
        except Exception as e:
            logger.error(f"Failed to load issue data: {e}")
            raise HTTPException(status_code=500, detail=f"加载问题数据失败: {str(e)}")
    
    def _build_issues_cache(self):
        """构建问题缓存"""
        if self._issues_cache is not None:
            return self._issues_cache
            
        data = self._load_data()
        issues = []
        
        for category in data.get("issue_list", []):
            category_type = category.get("type", "")
            category_name = category.get("name", "")
            
            for idx, issue_data in enumerate(category.get("issues", [])):
                # 生成唯一ID：分类类型_索引
                issue_id = f"{category_type}_{idx}"
                
                issue = Issue(
                    id=issue_id,
                    question=issue_data.get("question", ""),
                    answer=issue_data.get("answer", ""),
                    category_type=category_type,
                    category_name=category_name
                )
                issues.append(issue)
        
        self._issues_cache = issues
        logger.info(f"Built issues cache with {len(issues)} issues")
        return self._issues_cache
    
    def _build_categories_cache(self):
        """构建分类缓存"""
        if self._categories_cache is not None:
            return self._categories_cache
            
        data = self._load_data()
        categories = []
        
        for category in data.get("issue_list", []):
            category_obj = IssueCategory(
                type=category.get("type", ""),
                name=category.get("name", ""),
                issue_count=len(category.get("issues", []))
            )
            categories.append(category_obj)
        
        self._categories_cache = categories
        logger.info(f"Built categories cache with {len(categories)} categories")
        return self._categories_cache
    
    def get_issues(
        self, 
        page: int = 1, 
        limit: int = 50, 
        category_type: Optional[str] = None
    ) -> tuple[List[Issue], List[IssueCategory], int]:
        """获取问题列表"""
        issues = self._build_issues_cache()
        categories = self._build_categories_cache()
        
        # 按分类过滤
        if category_type:
            issues = [issue for issue in issues if issue.category_type == category_type]
        
        total = len(issues)
        
        # 分页
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_issues = issues[start_idx:end_idx]
        
        return paginated_issues, categories, total
    
    def get_issue_by_id(self, issue_id: str) -> Optional[Issue]:
        """根据ID获取问题详情"""
        issues = self._build_issues_cache()
        
        for issue in issues:
            if issue.id == issue_id:
                return issue
        
        return None


# 全局数据加载器实例
issue_loader = IssueDataLoader()


# === API接口 ===

@router.get("/list", response_model=IssueListResponse)
async def get_issue_list(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(50, ge=1, le=100, description="每页数量"),
    category: Optional[str] = Query(None, description="分类类型过滤")
):
    """获取问题列表"""
    try:
        issues, categories, total = issue_loader.get_issues(
            page=page, 
            limit=limit, 
            category_type=category
        )
        
        return IssueListResponse(
            data=issues,
            categories=categories,
            total=total,
            page=page,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Failed to get issue list: {e}")
        raise HTTPException(status_code=500, detail=f"获取问题列表失败: {str(e)}")


@router.get("/details/{issue_id}", response_model=IssueDetailResponse)
async def get_issue_details(issue_id: str):
    """获取问题详情"""
    try:
        issue = issue_loader.get_issue_by_id(issue_id)
        
        if not issue:
            raise HTTPException(status_code=404, detail=f"问题 '{issue_id}' 不存在")
        
        return IssueDetailResponse(issue=issue)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get issue details: {e}")
        raise HTTPException(status_code=500, detail=f"获取问题详情失败: {str(e)}")


@router.get("/categories")
async def get_categories():
    """获取问题分类列表"""
    try:
        _, categories, _ = issue_loader.get_issues()
        return {"categories": categories}
        
    except Exception as e:
        logger.error(f"Failed to get categories: {e}")
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")
