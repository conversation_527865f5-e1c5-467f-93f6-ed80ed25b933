"""健康检查模块

提供系统各组件的健康检查功能，包括数据库连接测试等。
"""

import logging
from typing import Dict, Any
from enum import Enum

from src.langgraph.zego_tools.ocean_executor import ocean_connection, themis_connection

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """健康状态枚举"""
    OK = "ok"
    ERROR = "error"
    WARNING = "warning"


class DatabaseHealthChecker:
    """数据库健康检查器"""
    
    @staticmethod
    async def check_ocean_database() -> Dict[str, Any]:
        """检查Ocean数据库连接
        
        Returns:
            包含状态、消息和详细信息的字典
        """
        try:
            is_connected = await ocean_connection.test_connection()
            
            if is_connected:
                return {
                    "status": HealthStatus.OK.value,
                    "message": "Ocean数据库连接正常",
                    "database": "ocean",
                    "timestamp": None  # 可以添加时间戳
                }
            else:
                return {
                    "status": HealthStatus.ERROR.value,
                    "message": "Ocean数据库连接失败",
                    "database": "ocean",
                    "timestamp": None
                }
                
        except Exception as e:
            logger.error(f"Ocean数据库连接测试异常: {e}")
            return {
                "status": HealthStatus.ERROR.value,
                "message": f"Ocean数据库连接测试异常: {str(e)}",
                "database": "ocean",
                "error": str(e),
                "timestamp": None
            }
    
    @staticmethod
    async def check_themis_database() -> Dict[str, Any]:
        """检查Themis数据库连接
        
        Returns:
            包含状态、消息和详细信息的字典
        """
        try:
            # 测试Themis连接（不使用SOCKS）
            result = await themis_connection.execute_query("SELECT 1 as test", themis_db="sdk")
            
            if result.is_successful:
                return {
                    "status": HealthStatus.OK.value,
                    "message": "Themis数据库连接正常",
                    "database": "themis",
                    "data": result.data.to_dict() if result.data is not None else None,
                    "timestamp": None
                }
            else:
                return {
                    "status": HealthStatus.ERROR.value,
                    "message": f"Themis数据库连接失败: {result.error_message}",
                    "database": "themis",
                    "error": result.error_message,
                    "timestamp": None
                }
                
        except Exception as e:
            logger.error(f"Themis数据库连接测试异常: {e}")
            return {
                "status": HealthStatus.ERROR.value,
                "message": f"Themis数据库连接测试异常: {str(e)}",
                "database": "themis",
                "error": str(e),
                "timestamp": None
            }
    
    @staticmethod
    async def check_all_databases() -> Dict[str, Any]:
        """检查所有数据库连接
        
        Returns:
            包含所有数据库检查结果的汇总字典
        """
        ocean_result = await DatabaseHealthChecker.check_ocean_database()
        themis_result = await DatabaseHealthChecker.check_themis_database()
        
        # 确定整体状态
        all_ok = (ocean_result["status"] == HealthStatus.OK.value and 
                  themis_result["status"] == HealthStatus.OK.value)
        
        overall_status = HealthStatus.OK.value if all_ok else HealthStatus.ERROR.value
        
        return {
            "status": overall_status,
            "message": "所有数据库连接正常" if all_ok else "部分数据库连接异常",
            "databases": {
                "ocean": ocean_result,
                "themis": themis_result
            },
            "summary": {
                "total": 2,
                "healthy": sum(1 for r in [ocean_result, themis_result] if r["status"] == HealthStatus.OK.value),
                "unhealthy": sum(1 for r in [ocean_result, themis_result] if r["status"] != HealthStatus.OK.value)
            }
        }


class SystemHealthChecker:
    """系统健康检查器"""
    
    @staticmethod
    async def check_system_health() -> Dict[str, Any]:
        """检查系统整体健康状态
        
        Returns:
            系统健康状态汇总
        """
        # 检查数据库
        db_health = await DatabaseHealthChecker.check_all_databases()
        
        # 可以在这里添加其他组件的健康检查
        # 例如：Redis、消息队列、外部API等
        
        # 确定系统整体状态
        system_healthy = db_health["status"] == HealthStatus.OK.value
        
        return {
            "status": HealthStatus.OK.value if system_healthy else HealthStatus.ERROR.value,
            "message": "系统运行正常" if system_healthy else "系统存在异常",
            "components": {
                "databases": db_health
                # 可以添加其他组件的检查结果
            },
            "timestamp": None  # 可以添加检查时间戳
        }


# 便捷的导出函数
async def check_ocean_db() -> Dict[str, Any]:
    """检查Ocean数据库连接的便捷函数"""
    return await DatabaseHealthChecker.check_ocean_database()


async def check_themis_db() -> Dict[str, Any]:
    """检查Themis数据库连接的便捷函数"""
    return await DatabaseHealthChecker.check_themis_database()


async def check_all_databases() -> Dict[str, Any]:
    """检查所有数据库连接的便捷函数"""
    return await DatabaseHealthChecker.check_all_databases()


async def check_system_health() -> Dict[str, Any]:
    """检查系统整体健康状态的便捷函数"""
    return await SystemHealthChecker.check_system_health()
