"""
QFlowAgent API 主应用
只保留核心功能，移除所有遗留代码
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.api.routes import health, tasks, workspaces
from src.langgraph.common.init_log import init_log

# 初始化日志
init_log()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("Starting QFlowAgent API server...")

    # 初始化存储系统
    try:
        from src.storage.database import init_database

        await init_database()
        logger.info("Storage system initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize storage system: {e}")
        raise

    # 初始化PostgreSQL通知管理器
    try:
        from src.storage.pg_notifier import init_pg_notifier

        await init_pg_notifier()
        logger.info("PostgreSQL notifier initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize PostgreSQL notifier: {e}")
        raise

    yield

    # 关闭时清理
    logger.info("Shutting down QFlowAgent API server...")

    # 关闭PostgreSQL通知管理器
    try:
        from src.storage.pg_notifier import close_pg_notifier

        await close_pg_notifier()
        logger.info("PostgreSQL notifier closed successfully")
    except Exception as e:
        logger.error(f"Failed to close PostgreSQL notifier: {e}")


# 创建FastAPI应用
app = FastAPI(title="QFlowAgent API", description="QFlowAgent 核心API服务", version="3.0.0", lifespan=lifespan)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 健康检查路由
app.include_router(health.router, prefix="/health")

# API路由
app.include_router(tasks.router, prefix="/api/tasks")
app.include_router(workspaces.router, prefix="/api/workspaces")


@app.get("/")
async def root():
    """根路径 - API信息"""
    return {
        "message": "QFlowAgent API Server",
        "version": "3.0.0",
        "docs": "/docs",
        "api": {"tasks": "/api/tasks", "workspaces": "/api/workspaces", "health": "/health"},
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=2026)
