"""
任务队列管理器
管理workflow_v2任务的并发执行，避免系统过载
"""

import asyncio
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, Optional

from langchain_core.messages import HumanMessage

from src.langgraph.graph_v2 import workflow_v2
from src.storage.database import get_session
from src.storage.enums import TaskStatus
from src.storage.services import TaskService

logger = logging.getLogger(__name__)

# 配置常量
MAX_CONCURRENT_TASKS = 5  # 最大并发任务数
MAX_QUEUE_SIZE = 200  # 最大队列大小


class TaskQueueStatus(Enum):
    """任务队列状态"""

    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"  # 执行失败


@dataclass
class QueuedTask:
    """队列中的任务"""

    query_id: str
    input_state: Dict[str, Any]
    config: Dict[str, Any]
    status: TaskQueueStatus = TaskQueueStatus.PENDING
    error: Optional[str] = None


class TaskQueueManager:
    """任务队列管理器"""

    def __init__(self, max_concurrent: int = MAX_CONCURRENT_TASKS):
        self.max_concurrent = max_concurrent
        self.queue = asyncio.Queue(maxsize=MAX_QUEUE_SIZE)
        self.running_tasks = {}  # query_id -> asyncio.Task
        self.completed_tasks = {}  # query_id -> QueuedTask
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.worker_task = None
        self.is_running = False

    async def start(self):
        """启动队列管理器"""
        if self.is_running:
            return

        self.is_running = True
        self.worker_task = asyncio.create_task(self._worker())
        logger.info(f"TaskQueueManager started with max_concurrent={self.max_concurrent}")

    async def stop(self):
        """停止队列管理器"""
        if not self.is_running:
            return

        self.is_running = False

        # 等待当前运行的任务完成
        if self.running_tasks:
            logger.info(f"Waiting for {len(self.running_tasks)} running tasks to complete...")
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)

        # 取消工作协程
        if self.worker_task and not self.worker_task.done():
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass

        logger.info("TaskQueueManager stopped")

    async def add_task(self, query_id: str, input_state: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """添加任务到队列"""
        if not self.is_running:
            raise RuntimeError("TaskQueueManager is not running")

        try:
            queued_task = QueuedTask(query_id=query_id, input_state=input_state, config=config)

            # 非阻塞添加到队列
            self.queue.put_nowait(queued_task)
            logger.info(f"Task {query_id} added to queue. Queue size: {self.queue.qsize()}")
            return True

        except asyncio.QueueFull:
            logger.error(f"Task queue is full. Cannot add task {query_id}")
            return False

    async def _worker(self):
        """队列工作协程"""
        logger.info("TaskQueueManager worker started")

        while self.is_running:
            try:
                # 从队列获取任务
                queued_task = await asyncio.wait_for(self.queue.get(), timeout=1.0)

                # 等待信号量（控制并发数）
                await self.semaphore.acquire()

                # 创建任务执行协程
                task = asyncio.create_task(self._execute_task(queued_task))
                self.running_tasks[queued_task.query_id] = task

                logger.info(
                    f"Started executing task {queued_task.query_id}. "
                    f"Running: {len(self.running_tasks)}, Queue: {self.queue.qsize()}"
                )

            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.error(f"Error in task queue worker: {e}")
                await asyncio.sleep(1)

    async def _execute_task(self, queued_task: QueuedTask):
        """执行单个任务"""
        try:
            queued_task.status = TaskQueueStatus.RUNNING

            # 更新数据库中的任务状态
            async with get_session() as db:
                task_service = TaskService(db)
                await task_service.update_task_status(queued_task.query_id, TaskStatus.RUNNING)

            # 执行workflow_v2
            logger.info(f"Executing workflow_v2 for task {queued_task.query_id}")

            async for event in workflow_v2.astream(queued_task.input_state, queued_task.config):
                # 处理流式事件（如果需要的话）
                pass

            # 任务完成
            queued_task.status = TaskQueueStatus.COMPLETED
            async with get_session() as db:
                task_service = TaskService(db)
                await task_service.update_task_status(queued_task.query_id, TaskStatus.COMPLETED)

            logger.info(f"Task {queued_task.query_id} completed successfully")

        except Exception as e:
            # 任务失败
            error_msg = str(e)
            queued_task.status = TaskQueueStatus.FAILED
            queued_task.error = error_msg

            logger.error(f"Task {queued_task.query_id} failed: {error_msg}")

            try:
                async with get_session() as db:
                    task_service = TaskService(db)
                    await task_service.update_task_status(queued_task.query_id, TaskStatus.FAILED)
            except Exception as db_error:
                logger.error(f"Failed to update task status in database: {db_error}")

        finally:
            # 清理和释放资源
            self.running_tasks.pop(queued_task.query_id, None)
            self.completed_tasks[queued_task.query_id] = queued_task
            self.semaphore.release()

            logger.info(
                f"Task {queued_task.query_id} finished. "
                f"Running: {len(self.running_tasks)}, Queue: {self.queue.qsize()}"
            )

    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        return {
            "is_running": self.is_running,
            "max_concurrent": self.max_concurrent,
            "queue_size": self.queue.qsize(),
            "running_tasks": len(self.running_tasks),
            "completed_tasks": len(self.completed_tasks),
            "running_task_ids": list(self.running_tasks.keys()),
        }


# 全局任务队列管理器实例
_task_queue_manager: Optional[TaskQueueManager] = None


async def get_task_queue_manager() -> TaskQueueManager:
    """获取全局任务队列管理器实例"""
    global _task_queue_manager

    if _task_queue_manager is None:
        _task_queue_manager = TaskQueueManager()
        await _task_queue_manager.start()

    return _task_queue_manager


async def init_task_queue_manager():
    """初始化任务队列管理器"""
    await get_task_queue_manager()
    logger.info("Task queue manager initialized")


async def close_task_queue_manager():
    """关闭任务队列管理器"""
    global _task_queue_manager

    if _task_queue_manager:
        await _task_queue_manager.stop()
        _task_queue_manager = None
        logger.info("Task queue manager closed")
