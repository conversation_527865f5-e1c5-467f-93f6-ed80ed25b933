import asyncio
import datetime
import logging
from typing import Set

import lark_oapi as lark
from langchain_core.messages import HumanMessage

from src.langgraph.graph_v2 import workflow_v2

from src.lark.card.elements import card_element_markdown, card_element_zhedie
from src.lark.notify import GraphLarkNotifier
from src.lark.types import LarkTask

logger = logging.getLogger(__name__)

# 全局任务池，用于存储正在处理的 message_id
_processing_messages: Set[str] = set()
_processing_lock = asyncio.Lock()


async def get_processing_messages_count() -> int:
    """获取当前正在处理的消息数量"""
    async with _processing_lock:
        return len(_processing_messages)


async def get_processing_messages() -> Set[str]:
    """获取当前正在处理的所有 message_id"""
    async with _processing_lock:
        return _processing_messages.copy()


async def clear_processing_messages() -> int:
    """清空任务池（谨慎使用，仅在需要强制重置时使用）"""
    async with _processing_lock:
        count = len(_processing_messages)
        _processing_messages.clear()
        logger.warning(f"已强制清空任务池，清理了 {count} 个任务")
        return count


async def process_lark_message(lark_msg: lark.im.v1.P2ImMessageReceiveV1, zlark_instance) -> None:
    """处理飞书消息（解决循环引用问题，通过参数传入 zlark 实例）"""
    # 获取 message_id 用于去重
    message_id = lark_msg.event.message.message_id
    message_ts = int(lark_msg.event.message.create_time)  # 1753894717369，毫秒时间戳
    now_ts = int(datetime.datetime.now().timestamp() * 1000)  # 当前时间的毫秒时间戳
    # 如果与当前时间差超过10分钟，则忽略
    if abs(now_ts - message_ts) > 10 * 60 * 1000:
        logger.warning(f"消息 {message_id} 创建时间 {message_ts} 与当前时间差超过10分钟，忽略处理")
        return

    # 检查是否已经在处理该消息
    async with _processing_lock:
        if message_id in _processing_messages:
            logger.info(f"消息 {message_id} 已在处理中，忽略重复推送")
            return

        # 添加到正在处理的任务池
        _processing_messages.add(message_id)
        logger.debug(f"开始处理消息 {message_id}，当前任务池大小: {len(_processing_messages)}")

    try:
        lark_task = LarkTask(lark_msg=lark_msg)
        if not lark_task.valid():
            logger.warning(f"消息 {message_id} 无效，忽略处理 ({lark_task.lark_msg.event.message.content})")
            return

        result = await run_graph(lark_task, zlark_instance)
        logger.info(f"消息 {message_id} 处理完成")
        print(result)
    except Exception as e:
        logger.error(f"处理消息 {message_id} 时发生错误: {e}", exc_info=True)
        raise
    finally:
        # 无论成功还是失败，都要从任务池中移除
        async with _processing_lock:
            _processing_messages.discard(message_id)
            logger.debug(f"消息 {message_id} 处理结束，从任务池移除，当前任务池大小: {len(_processing_messages)}")


async def run_graph(lark_task: LarkTask, zlark_instance):
    """运行图并处理结果"""

    # 开始任务
    zlark_instance.reply_msg("wkk", message_id=lark_task.lark_msg.event.message.message_id)

    input = {"messages": [HumanMessage(content=lark_task.content)]}
    thread_id = lark_task.lark_msg.event.message.message_id
    config = {"configurable": {"thread_id": thread_id}}
    # values: 每个节点触发一次，并行节点合并触发一次；将state返回上来
    # message: human的没有，llm请求是流式的token by token;
    # updates: 返回每个节点的upate内容

    notifier = GraphLarkNotifier(zlark_instance, thread_message_id=lark_task.lark_msg.event.message.message_id)

    # 最终汇报卡片仅发送一次
    final_card_sent = False

    # 使用workflow_v2替代已废弃的zego_graph
    # 订阅更新（每次都是完整 state 快照，由 Functional writer(state) 推送）
    async for state in workflow_v2.astream(input, config, stream_mode=["updates"]):
        if not isinstance(state, dict):
            continue
        # 基于 state 渲染进度卡
        await notifier.update_state(state)

        # 若已有 reporter_result，则另发独立最终卡片（避免被进度卡淹没）
        if not final_card_sent and state.get("reporter_result") is not None:
            try:
                rr = state.get("reporter_result")
                zlark_instance.reply_custom_card_msg(
                    title="reporter",
                    subtitle="最终汇报",
                    message_id=lark_task.lark_msg.event.message.message_id,
                    elements=[
                        card_element_zhedie(
                            "thinking",
                            [card_element_markdown(str(getattr(rr, "thinking", "") or ""))],
                        ),
                        card_element_markdown(str(getattr(rr, "executive_summary", "") or "")),
                    ],
                )
                final_card_sent = True
            except Exception:
                pass

    # 最后一轮刷新进度卡
    await notifier.flush(force=True)


# def transform_langchain_event_to_lark_api():
