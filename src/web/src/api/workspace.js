import api from "./index";

/**
 * 工作空间相关API
 */
export const workspaceApi = {
  /**
   * 获取工作空间列表
   */
  async getWorkspaces() {
    const response = await api.get("/workspaces");
    return response;
  },

  /**
   * 获取工作空间详情
   * @param {number} workspaceId - 工作空间ID
   */
  async getWorkspace(workspaceId) {
    const response = await api.get(`/workspaces/${workspaceId}`);
    return response;
  },

  /**
   * 创建工作空间
   * @param {Object} data - 工作空间数据
   * @param {string} data.name - 工作空间名称
   * @param {string} [data.description] - 工作空间描述
   */
  async createWorkspace(data) {
    const response = await api.post("/workspaces", data);
    return response;
  },

  /**
   * 更新工作空间
   * @param {number} workspaceId - 工作空间ID
   * @param {Object} data - 更新数据
   * @param {string} [data.name] - 工作空间名称
   * @param {string} [data.description] - 工作空间描述
   * @param {Object} [data.config] - 工作空间配置
   */
  async updateWorkspace(workspaceId, data) {
    const response = await api.put(`/workspaces/${workspaceId}`, data);
    return response;
  },

  /**
   * 删除工作空间
   * @param {number} workspaceId - 工作空间ID
   */
  async deleteWorkspace(workspaceId) {
    const response = await api.delete(`/workspaces/${workspaceId}`);
    return response;
  },

  /**
   * 获取工作空间统计信息
   * @param {number} workspaceId - 工作空间ID
   */
  async getWorkspaceStats(workspaceId) {
    const response = await api.get(`/workspaces/${workspaceId}/stats`);
    return response;
  },
};
