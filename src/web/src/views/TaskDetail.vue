<template>
  <div class="task-detail">
    <el-card v-if="taskStore.currentTask" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-button type="primary" size="small" @click="$router.back()">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <span>任务详情 - {{ taskStore.currentTask.query_id }}</span>
          <el-tag :type="getStatusTagType(taskStore.currentTask.status)">
            {{ getStatusLabel(taskStore.currentTask.status) }}
          </el-tag>
        </div>
        <div style="margin-left: auto; display: flex; gap: 8px">
          <el-button
            v-if="taskStore.currentTask.status === 'dead'"
            size="small"
            type="primary"
            @click="resumeTask"
            >恢复执行</el-button
          >
          <el-button
            v-else-if="taskStore.currentTask.status === 'done'"
            size="small"
            type="primary"
            @click="rerunTask"
            >重新运行</el-button
          >
        </div>
      </template>

      <!-- 基本信息 -->
      <div class="task-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务内容" :span="2">
            <div class="user-input">
              {{ taskStore.currentTask.user_query }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item label="创建时间">
            {{ taskStore.currentTask.timestamp }}
          </el-descriptions-item>

        </el-descriptions>
      </div>

      <!-- 智能体分析状态 -->
      <el-divider content-position="left">分析状态</el-divider>
      <div class="agent-status-section">
        <AgentStatusDisplay
          :task-state="taskStore.currentTask"
          :show-latest-update="false"
          :status="taskStore.currentTask.status"
        />
      </div>

      <!-- 消息历史 -->
      <el-divider content-position="left">消息历史</el-divider>
      <el-collapse class="messages-section" accordion>
        <el-collapse-item
          v-for="(message, index) in taskStore.currentTask.messages || []"
          :key="index"
          :name="String(index)"
        >
          <template #title>
            <div class="message-header">
              <el-tag :type="getMessageTagType(message.type)" size="small">
                {{ getMessageTypeLabel(message.type) }}
              </el-tag>
              <span v-if="message.name" class="message-name">{{
                message.name
              }}</span>
            </div>
          </template>
          <div class="message-content">
            {{ message.content }}
          </div>
        </el-collapse-item>
        <div
          v-if="
            !taskStore.currentTask.messages ||
            taskStore.currentTask.messages.length === 0
          "
          class="empty-messages"
        >
          <el-empty description="暂无消息历史" />
        </div>
      </el-collapse>
    </el-card>

    <div v-else class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { taskApi } from "@/api";
import dayjs from "dayjs";
import {
  getChatMessageTypeLabel,
  getChatMessageTagType,
  getTaskStatusLabel,
  getTaskStatusTagType,
} from "@/utils/messageUtils";
import { useTaskStore } from "@/stores/task";

import AgentStatusDisplay from "@/components/AgentStatusDisplay.vue";

const route = useRoute();
const taskStore = useTaskStore();

const queryId = route.params.queryId;

// 使用统一的工具函数替代重复的本地函数
const getStatusTagType = getTaskStatusTagType;
const getStatusLabel = getTaskStatusLabel;
const getMessageTagType = getChatMessageTagType;
const getMessageTypeLabel = getChatMessageTypeLabel;

const formatDateTime = (time) => {
  return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
};

// 任务操作功能已简化 - 专注核心功能
const rerunTask = async () => {
  try {
    const created = await taskApi.createTask({
      user_query: taskStore.currentTask.user_query,
      workspace_id: taskStore.currentTask.workspace_id || 2,
      max_parallel_workers: 5,
      recursion_limit: 30,
    });
    ElMessage.success(`已重新运行，新任务ID: ${created.query_id.slice(-8)}`);
  } catch (e) {
    ElMessage.error(e.message || "重新运行失败");
  }
};

onMounted(() => {
  if (queryId) {
    taskStore.loadTaskDetail(queryId);
  }
});
</script>

<style scoped>
.task-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: bold;
}

.task-info {
  margin-bottom: 20px;
}

.agent-status-section {
  margin-bottom: 20px;
}

.task-id-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thread-id {
  font-family: monospace;
  font-size: 12px;
}

.checkpoint-tag {
  font-family: monospace;
}

.user-input {
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  white-space: pre-wrap;
  line-height: 1.6;
}

.config-info {
  margin-bottom: 20px;
}

.json-display {
  background: var(--el-fill-color-lighter);
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.messages-section {
  margin-bottom: 20px;
}

.message-item {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid var(--el-color-primary);
  background: var(--el-fill-color-lighter);
}

.message-human {
  border-left-color: var(--el-color-primary);
}

.message-ai {
  border-left-color: var(--el-color-success);
}

.message-system {
  border-left-color: var(--el-color-info);
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.message-name {
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.message-content {
  white-space: pre-wrap;
  line-height: 1.6;
}

.empty-messages {
  text-align: center;
  padding: 40px;
}

.loading-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}
</style>
