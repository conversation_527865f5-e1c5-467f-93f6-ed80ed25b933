<template>
  <div class="task-center">
    <!-- 工作空间选择区域 -->
    <el-card class="workspace-card" shadow="never">
      <div class="workspace-header">
        <div class="workspace-info">
          <el-icon><FolderOpened /></el-icon>
          <span class="workspace-title">当前工作空间</span>
        </div>
        <WorkspaceSelector
          v-model="currentWorkspace"
          @change="handleWorkspaceChange"
        />
      </div>
    </el-card>

    <!-- 任务创建区域 -->
    <el-card class="task-creation-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Plus /></el-icon>
          <span>任务创建</span>
        </div>
      </template>

      <el-tabs v-model="activeTab" class="task-tabs">
        <!-- 创建单个任务标签 -->
        <el-tab-pane label="创建任务" name="single">
          <CreateTaskDialog
            :workspace="currentWorkspace"
            @task-created="handleTaskCreated"
            @show-batch-dialog="activeTab = 'batch'"
          />
        </el-tab-pane>

        <!-- 批量创建任务标签 -->
        <el-tab-pane label="批量任务" name="batch">
          <div class="batch-task-container">
            <BatchTaskDialog
              :workspace-id="currentWorkspace?.id"
              @task-created="handleBatchTasksCreated"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 实时任务流 -->
    <el-card v-if="currentTaskId" class="stream-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Monitor /></el-icon>
          <span>任务执行中 - {{ currentTaskId }}</span>
        </div>
      </template>

      <!-- 智能体状态展示（仅在统一状态为 running 时展示“最新进展”） -->
      <AgentStatusDisplay
        :stream-messages="currentTaskMessages"
        :status="'running'"
      />
    </el-card>

    <!-- 任务管理 -->
    <el-card class="task-management-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>任务管理</span>
          <div style="margin-left: auto; display: flex; gap: 8px">
            <el-button
              type="primary"
              size="small"
              @click="handleRefresh"
              :loading="taskStore.loading"
            >
              刷新
            </el-button>
            <el-button
              size="small"
              @click="showAdvancedFilters = !showAdvancedFilters"
            >
              {{ showAdvancedFilters ? "收起筛选" : "展开筛选" }}
            </el-button>
          </div>
          <el-dropdown trigger="click" style="margin-left: 8px">
            <el-button size="small" type="primary">
              批量操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleBatchRerunSelected"
                  >重新运行所选</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>

      <!-- 高级筛选器 -->
      <div v-show="showAdvancedFilters" class="filters">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
              <el-option label="全部" value="" />
              <el-option label="运行中" value="running" />
              <el-option label="已完成" value="done" />
              <el-option label="已中断" value="dead" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索任务内容"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-input-number
              v-model="pageSize"
              :min="5"
              :max="100"
              controls-position="right"
              @change="handleSizeChange"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 任务表格 -->
      <el-table
        :data="displayTasks"
        style="width: 100%"
        v-loading="taskStore.loading"
        row-key="query_id"
        @row-click="handleRowClick"
        @selection-change="onSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="query_id" label="任务ID" width="200">
          <template #default="{ row }">
            <el-link
              type="primary"
              @click.stop="$router.push(`/task/${row.query_id}`)"
            >
              {{ row.query_id.slice(-8) }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          prop="user_query"
          label="查询内容"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span v-if="row.user_query && row.user_query.trim()">
              {{ row.user_query.slice(0, 50)
              }}{{ row.user_query.length > 50 ? "..." : "" }}
            </span>
            <span v-else class="empty-content"> 暂无查询内容 </span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="任务状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="Agent状态" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.status === 'running'" type="primary" size="small"
              >执行中</el-tag
            >
            <el-tag
              v-else-if="row.status === 'done'"
              type="success"
              size="small"
              >已完成</el-tag
            >
            <el-tag v-else-if="row.status === 'dead'" type="danger" size="small"
              >已中断</el-tag
            >
            <span v-else class="empty-content">-</span>
          </template>
        </el-table-column>

        <el-table-column prop="timestamp" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.timestamp) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-dropdown trigger="click" @click.stop>
              <el-button size="small" type="primary" plain @click.stop>
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :disabled="row.status !== 'dead'"
                    @click="resumeTask(row)"
                    >恢复执行</el-dropdown-item
                  >
                  <el-dropdown-item
                    :disabled="row.status === 'running'"
                    @click="rerunTask(row)"
                    >重新运行</el-dropdown-item
                  >
                  <el-dropdown-item
                    :disabled="row.status === 'running'"
                    @click="deleteTask(row)"
                    >删除</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container" v-if="taskStore.totalCount > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 50, 100]"
          :total="taskStore.totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          small
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import {
  Plus,
  Monitor,
  Document,
  Search,
  Delete,
  ArrowDown,
  FolderOpened,
  Lightning,
  DocumentChecked,
} from "@element-plus/icons-vue";
import { useTaskStore } from "@/stores/task";
import { useWorkspaceStore } from "@/stores/workspace";
import WorkspaceSelector from "@/components/WorkspaceSelector.vue";
import BatchTaskDialog from "@/components/BatchTaskDialog.vue";
import CreateTaskDialog from "@/components/CreateTaskDialog.vue";
import dayjs from "dayjs";

import { getTaskStatusLabel, getTaskStatusTagType } from "@/utils/messageUtils";
import AgentStatusDisplay from "@/components/AgentStatusDisplay.vue";

// 添加去抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const router = useRouter();
const taskStore = useTaskStore();
const workspaceStore = useWorkspaceStore();

// Workspace相关数据
const currentWorkspace = ref(null);
const currentTaskId = ref(null);
const currentTaskQueryId = ref(null); // 添加完整的query_id

// 任务管理相关数据
const showAdvancedFilters = ref(false);
const statusFilter = ref("");
const searchKeyword = ref("");
const currentPage = ref(1);
const pageSize = ref(10);

// 任务创建标签页相关数据
const activeTab = ref("single"); // 默认显示创建任务标签

// 操作按钮加载状态
const operationLoading = ref(new Set());

// 多选
const selectedRows = ref([]);
const onSelectionChange = (rows) => {
  selectedRows.value = rows || [];
};

const handleBatchRerunSelected = async () => {
  try {
    const toRerun = selectedRows.value.filter((r) => r.status !== "running");
    if (toRerun.length === 0) {
      ElMessage.info("请选择非运行中的任务");
      return;
    }
    await ElMessageBox.confirm(
      `确认重新运行 ${toRerun.length} 个任务？`,
      "批量重新运行",
      { type: "warning" },
    );
    for (const t of toRerun) {
      await taskApi.createTask({
        user_query: t.user_query,
        workspace_id: t.workspace_id || 2,
        max_parallel_workers: 5,
        recursion_limit: 30,
      });
    }
    ElMessage.success("批量重新运行请求已提交");
    if (currentWorkspace.value?.id) {
      await taskStore.loadTasks({
        page: currentPage.value,
        page_size: pageSize.value,
        workspace_id: currentWorkspace.value.id
      });
    }
    updateEventSourcesForCurrentPage();
  } catch (e) {
    if (e !== "cancel") ElMessage.error(e?.message || "批量重跑失败");
  }
};

// 批量恢复 dead 任务
// eslint-disable-next-line no-unused-vars
const handleBatchResumeDead = async (onlyDead = false) => {
  try {
    const targets = (selectedRows.value || []).filter(
      (r) => r.status !== "running",
    );
    // 修夏：仅处理当前页显示的dead任务
    const deadTargets = displayTasks.value.filter((t) => t.status === "dead");
    const list = onlyDead ? deadTargets : targets;
    if (list.length === 0) {
      ElMessage.info(
        onlyDead ? "当前页没有可恢复的 dead 任务" : "请选择非运行中的任务",
      );
      return;
    }
    await ElMessageBox.confirm(`确认恢复 ${list.length} 个任务？`, "批量恢复", {
      type: "warning",
    });
    const resp = await fetch("/api/tasks/batch-resume", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ query_ids: list.map((t) => t.query_id) }),
    });
    if (!resp.ok) {
      const err = await resp.json().catch(() => ({}));
      throw new Error(err.detail || `HTTP ${resp.status}`);
    }
    ElMessage.success("批量恢复请求已提交");
    if (currentWorkspace.value?.id) {
      await taskStore.loadTasks({
        page: currentPage.value,
        page_size: pageSize.value,
        workspace_id: currentWorkspace.value.id
      });
    }
    // 为运行中的任务开启事件流
    updateEventSourcesForCurrentPage();
  } catch (e) {
    if (e !== "cancel") ElMessage.error(e.message || "批量恢复失败");
  }
};

// 计算属性
const currentTaskMessages = computed(() => {
  if (!currentTaskQueryId.value) return [];

  // 只返回当前任务的消息
  return taskStore.streamMessages.filter(
    (msg) =>
      msg.query_id === currentTaskQueryId.value ||
      (msg.data && msg.data.query_id === currentTaskQueryId.value),
  );
});

const filteredTasks = computed(() => {
  let tasks = taskStore.tasks;

  // 状态筛选
  if (statusFilter.value) {
    tasks = tasks.filter((task) => task.status === statusFilter.value);
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    tasks = tasks.filter(
      (task) =>
        task.user_query.toLowerCase().includes(keyword) ||
        task.query_id.toLowerCase().includes(keyword),
    );
  }

  return tasks;
});

// 注意：现在我们使用后端分页，totalCount 由 taskStore.totalCount 提供

const displayTasks = computed(() => {
  // 直接返回后端已经分页好的数据
  return taskStore.tasks;
});

// Workspace相关方法
const handleWorkspaceChange = async (workspace) => {
  try {
    currentWorkspace.value = workspace;

    if (!workspace?.id) {
      ElMessage.error("无效的工作空间");
      return;
    }

    // 重置分页到第一页
    currentPage.value = 1;

    // 重新加载任务列表
    await taskStore.loadTasks({
      page: 1,
      page_size: pageSize.value,
      workspace_id: workspace.id,
    });

    // 清理当前的事件流连接
    activeEventSources.value.forEach((eventSource) => {
      eventSource.close();
    });
    activeEventSources.value.clear();

    // 重新启动运行中任务的事件流监听
    await startRunningTasksEventStreams();
  } catch (error) {
    console.error("切换工作空间失败:", error);
    ElMessage.error("切换工作空间失败");
  }
};

// 方法
// 处理任务创建成功事件
const handleTaskCreated = async (task) => {
  // 设置当前任务ID以显示实时进度
  currentTaskId.value = task.query_id.slice(-8);
  currentTaskQueryId.value = task.query_id; // 保存完整的query_id

  // 监听任务完成事件
  if (taskStore.currentEventSource) {
    taskStore.currentEventSource.on("complete", () => {
      setTimeout(() => {
        currentTaskId.value = null;
        // 不清除currentTaskQueryId，保持最终状态显示
      }, 1000);
    });

    taskStore.currentEventSource.on("error", () => {
      setTimeout(() => {
        currentTaskId.value = null;
        currentTaskQueryId.value = null;
      }, 3000);
    });
  }

  // 刷新任务列表
  if (currentWorkspace.value?.id) {
    await taskStore.loadTasks({
      page: currentPage.value,
      page_size: pageSize.value,
      workspace_id: currentWorkspace.value.id
    });
  }
};

// 任务管理方法
const handleRefresh = async () => {
  if (!currentWorkspace.value?.id) {
    ElMessage.warning("请先选择工作空间");
    return;
  }

  await taskStore.loadTasks({
    page: currentPage.value,
    page_size: pageSize.value,
    workspace_id: currentWorkspace.value.id
  });
};

const handleSearch = () => {
  currentPage.value = 1;
  // 筛选逻辑在计算属性中处理
};

const handleRowClick = (row) => {
  router.push(`/task/${row.query_id}`);
};

const handleSizeChange = async (size) => {
  pageSize.value = size;
  currentPage.value = 1;

  if (!currentWorkspace.value?.id) {
    ElMessage.warning("请先选择工作空间");
    return;
  }

  // 使用正确的分页参数
  await taskStore.loadTasks({
    page: 1,
    page_size: size,
    workspace_id: currentWorkspace.value.id
  });
};

const handleCurrentChange = async (page) => {
  currentPage.value = page;

  if (!currentWorkspace.value?.id) {
    ElMessage.warning("请先选择工作空间");
    return;
  }

  // 切换页面时重新加载数据
  await taskStore.loadTasks({
    page: page,
    page_size: pageSize.value,
    workspace_id: currentWorkspace.value.id
  });
};

// 使用统一的工具函数替代重复的本地函数
const getStatusTagType = getTaskStatusTagType;
const getStatusLabel = getTaskStatusLabel;

// 批量任务创建成功处理
const handleBatchTasksCreated = async (createdTasks) => {
  // 启动事件流监听
  if (createdTasks.length > 0) {
    startBatchEventStreams(createdTasks);

    // 设置最后一个任务为当前显示的任务（实时进展）
    const lastTaskId = createdTasks[createdTasks.length - 1];
    if (lastTaskId) {
      currentTaskId.value = lastTaskId.slice(-8);
      currentTaskQueryId.value = lastTaskId;

      // 启动最后一个任务的实时流
      await taskStore.startTaskStream(lastTaskId);
    }
  }

  // 刷新任务列表并更新EventSource连接
  if (currentWorkspace.value?.id) {
    await taskStore.loadTasks({
      page: currentPage.value,
      page_size: pageSize.value,
      workspace_id: currentWorkspace.value.id
    });
  }
  updateEventSourcesForCurrentPage();
};

// 事件流管理
const activeEventSources = ref(new Map());

const startBatchEventStreams = (queryIds) => {
  queryIds.forEach((queryId) => {
    startEventStream(queryId);
  });
};

const startEventStream = (queryId) => {
  if (activeEventSources.value.has(queryId)) {
    return; // 已经在监听
  }

  console.log(`Starting EventSource for ${queryId}`);

  try {
    const eventSource = new EventSource(`/api/tasks/${queryId}/stream`);

    eventSource.onopen = () => {
      console.log(`EventSource opened for ${queryId}`);
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log(`EventSource message for ${queryId}:`, data);

        // 更新任务状态
        if (data.type === "custom" && data.value) {
          const task = taskStore.tasks.find((t) => t.query_id === queryId);
          if (task && data.value.status) {
            task.status = data.value.status;

            // 如果任务完成，关闭EventSource
            if (
              ["completed", "failed", "cancelled"].includes(data.value.status)
            ) {
              stopEventStream(queryId);
            }
          }
        }
      } catch (error) {
        console.error(
          `Failed to parse EventSource message for ${queryId}:`,
          error,
        );
      }
    };

    eventSource.onerror = (error) => {
      console.error(`EventSource error for ${queryId}:`, error);
      stopEventStream(queryId);
    };

    activeEventSources.value.set(queryId, eventSource);
  } catch (error) {
    console.error(`Failed to create EventSource for ${queryId}:`, error);
  }
};

const stopEventStream = (queryId) => {
  const eventSource = activeEventSources.value.get(queryId);
  if (eventSource) {
    eventSource.close();
    activeEventSources.value.delete(queryId);
  }
};

// 页面卸载时清理事件源
onUnmounted(() => {
  activeEventSources.value.forEach((eventSource) => {
    eventSource.close();
  });
  activeEventSources.value.clear();
});

// 使用统一的时间格式化函数，但保留本地的简单格式化
const formatDateTime = (time) => {
  return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
};

// 启动运行中任务的事件流监听（只为当前页显示的running任务创建EventSource）
const startRunningTasksEventStreams = async () => {
  try {
    // 仅为当前页显示的running任务创建EventSource
    const runningTasks = displayTasks.value.filter(
      (task) => task.status === "running",
    );

    console.log(
      `Found ${runningTasks.length} running tasks on current page, starting event streams`,
    );
    runningTasks.forEach((task) => {
      startEventStream(task.query_id);
    });
  } catch (error) {
    console.error("Failed to start event streams for running tasks:", error);
  }
};

// 清理不在当前页的EventSource
const cleanupEventSourcesNotOnCurrentPage = () => {
  const currentPageTaskIds = new Set(displayTasks.value.map((t) => t.query_id));

  activeEventSources.value.forEach((eventSource, queryId) => {
    if (!currentPageTaskIds.has(queryId)) {
      console.log(`Stopping EventSource for ${queryId} (not on current page)`);
      stopEventStream(queryId);
    }
  });
};

// 更新当前页的EventSource连接
const updateEventSourcesForCurrentPage = () => {
  // 先清理不在当前页的EventSource
  cleanupEventSourcesNotOnCurrentPage();

  // 为当前页的running任务启动EventSource
  startRunningTasksEventStreams();
};

// 创建去抖的EventSource更新函数（2秒去抖）
const debouncedEventSourceUpdate = debounce(() => {
  updateEventSourcesForCurrentPage();
}, 2000);

import { taskApi } from "@/api";

// 任务操作方法

const rerunTask = async (task) => {
  const opKey = `rerun_${task.query_id}`;
  try {
    operationLoading.value.add(opKey);
    const created = await taskApi.createTask({
      user_query: task.user_query,
      workspace_id: task.workspace_id || 2,
      max_parallel_workers: 5,
      recursion_limit: 30,
    });
    ElMessage.success(`已重新运行，新的任务ID: ${created.query_id.slice(-8)}`);
    await taskStore.startTaskStream(created.query_id);
    if (currentWorkspace.value?.id) {
      await taskStore.loadTasks({
        page: currentPage.value,
        page_size: pageSize.value,
        workspace_id: currentWorkspace.value.id
      });
    }
    updateEventSourcesForCurrentPage();
  } catch (e) {
    ElMessage.error(e?.message || "重新运行失败");
  } finally {
    operationLoading.value.delete(opKey);
  }
};

// 生命周期
onMounted(async () => {
  // 等待工作空间初始化，不在这里立即加载数据
  // 数据加载将在工作空间选择器触发change事件时进行
  await startRunningTasksEventStreams();
});

// 监听分页变化，触发EventSource更新（去抖）
watch([currentPage, pageSize], () => {
  console.log("Page changed, debouncing EventSource update...");
  debouncedEventSourceUpdate();
});

// 监听工作空间变化，自动加载数据
watch(currentWorkspace, async (newWorkspace, oldWorkspace) => {
  if (newWorkspace && newWorkspace.id && newWorkspace !== oldWorkspace) {
    // 工作空间发生变化时，重新加载数据
    currentPage.value = 1;
    await taskStore.loadTasks({
      page: 1,
      page_size: pageSize.value,
      workspace_id: newWorkspace.id,
    });
  }
});

onUnmounted(() => {
  taskStore.stopTaskStream();
  // 清理所有EventSource
  activeEventSources.value.forEach((eventSource) => {
    eventSource.close();
  });
  activeEventSources.value.clear();
});
</script>

<style scoped>
.task-center {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 任务创建区域样式 */
.task-creation-card {
  margin-bottom: 20px;
}

.task-tabs {
  --el-tabs-header-height: 40px;
}

.task-tabs :deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

.task-tabs :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.batch-task-container {
  padding: 0;
}

/* Workspace相关样式 */
.workspace-card {
  margin-bottom: 20px;
}

.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}

.workspace-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.workspace-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

/* 勾选框标签样式 */
.checkbox-label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  margin-left: 8px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .mode-config-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}

.stream-card {
  margin-bottom: 20px;
}

.task-management-card {
  flex: 1;
}

.filters {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.el-table {
  cursor: pointer;
}

.el-table .el-table__row:hover {
  background-color: var(--el-fill-color-light);
}

.empty-content {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

.checkpoint-tag {
  font-family: monospace;
}
</style>
