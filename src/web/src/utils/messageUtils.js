/**
 * 消息相关的工具函数
 * 统一处理不同类型的消息标签和状态
 */

/**
 * 获取流式消息类型的标签
 * 用于TaskCenter等流式消息显示
 */
export const getStreamMessageTypeLabel = (type) => {
  const labelMap = {
    values: "状态值",
    messages: "消息",
    updates: "更新",
    custom: "自定义",
    success: "完成",
    error: "错误",
    close: "关闭",
  };
  return labelMap[type] || type;
};

/**
 * 获取流式消息类型的标签样式
 * 用于TaskCenter等流式消息显示
 */
export const getStreamMessageTagType = (type) => {
  const typeMap = {
    values: "success",
    messages: "primary",
    updates: "info",
    custom: "warning",
    success: "success",
    error: "danger",
    close: "info",
  };
  return typeMap[type] || "info";
};

/**
 * 获取聊天消息类型的标签
 * 用于TaskDetail等聊天消息显示
 */
export const getChatMessageTypeLabel = (type) => {
  const labelMap = {
    human: "用户",
    ai: "AI",
    system: "系统",
  };
  return labelMap[type] || type;
};

/**
 * 获取聊天消息类型的标签样式
 * 用于TaskDetail等聊天消息显示
 */
export const getChatMessageTagType = (type) => {
  const typeMap = {
    human: "primary",
    ai: "success",
    system: "info",
  };
  return typeMap[type] || "info";
};

/**
 * 获取任务状态标签
 * 通用的任务状态显示
 */
export const getTaskStatusLabel = (status) => {
  const labelMap = {
    running: "运行中",
    done: "已完成",
    dead: "已中断",
  };
  return labelMap[status] || status;
};

/**
 * 获取任务状态标签样式
 * 通用的任务状态显示
 */
export const getTaskStatusTagType = (status) => {
  const typeMap = {
    running: "warning",
    done: "success",
    dead: "danger",
  };
  return typeMap[status] || "info";
};

/**
 * 通用的消息类型标签获取函数
 * 根据上下文自动选择合适的标签映射
 *
 * @param {string} type - 消息类型
 * @param {string} context - 上下文类型：'stream' | 'chat' | 'status'
 * @returns {string} 标签文本
 */
export const getMessageTypeLabel = (type, context = "stream") => {
  switch (context) {
    case "stream":
      return getStreamMessageTypeLabel(type);
    case "chat":
      return getChatMessageTypeLabel(type);
    case "status":
      return getTaskStatusLabel(type);
    default:
      return getStreamMessageTypeLabel(type);
  }
};

/**
 * 通用的消息类型标签样式获取函数
 * 根据上下文自动选择合适的样式映射
 *
 * @param {string} type - 消息类型
 * @param {string} context - 上下文类型：'stream' | 'chat' | 'status'
 * @returns {string} 标签样式
 */
export const getMessageTagType = (type, context = "stream") => {
  switch (context) {
    case "stream":
      return getStreamMessageTagType(type);
    case "chat":
      return getChatMessageTagType(type);
    case "status":
      return getTaskStatusTagType(type);
    default:
      return getStreamMessageTagType(type);
  }
};

/**
 * 格式化时间显示
 * 统一的时间格式化函数
 */
export const formatTime = (timestamp) => {
  if (!timestamp) return "";

  const date = new Date(timestamp);
  const now = new Date();
  const diff = now - date;

  // 如果是今天，只显示时间
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  }

  // 否则显示完整日期时间
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};
