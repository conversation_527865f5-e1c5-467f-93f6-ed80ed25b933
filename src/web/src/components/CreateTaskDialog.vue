<template>
  <div class="create-task-content">
    <!-- 执行配置区域 -->
    <div class="config-section">
      <TaskConfig v-model="taskConfig" />
    </div>

    <!-- 查询内容输入区域 -->
    <div class="queries-section">
      <div class="queries-input-container">
        <el-input 
          v-model="userInput"
          type="textarea"
          placeholder="请输入您的查询内容，例如：分析下 1850816294 推流成功率下降的问题"
          maxlength="1000"
          show-word-limit
          class="queries-textarea"
          :autosize="{ minRows: 2, maxRows: 5 }"
          style="width: 100%"
        />
      </div>
    <el-button
        type="primary"
        @click="handleCreateTask"
        :loading="loading"
        :disabled="!userInput.trim()"
        size="large"
        style="width: 100%"
      >
        <el-icon><VideoPlay /></el-icon>
        启动任务
      </el-button>
  </div>
</div>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { Plus, VideoPlay } from "@element-plus/icons-vue";
import { useTaskStore } from "@/stores/task";
import TaskConfig from "./TaskConfig.vue";

// Props
const props = defineProps({
  workspace: {
    type: Object,
    default: null,
  },
});

// Emits
const emit = defineEmits(["task-created"]);

// 响应式数据
const userInput = ref("分析1850816294推流成功率");
const loading = ref(false);

// 统一配置对象
const taskConfig = ref({
  maxParallelWorkers: 5,
  recursionLimit: 30,
  enableMock: false,
  mockDelay: 2,
});

// Store
const taskStore = useTaskStore();

// 创建任务方法
const handleCreateTask = async () => {
  if (!userInput.value.trim()) {
    ElMessage.warning("请输入查询内容");
    return;
  }

  try {
    loading.value = true;

    // 准备配置参数
    const options = {
      maxParallelWorkers: taskConfig.value.maxParallelWorkers,
      recursionLimit: taskConfig.value.recursionLimit,
      workspaceId: props.workspace?.id || 2,
    };

    // 添加Mock模式配置
    if (taskConfig.value.enableMock) {
      options.configurable = {
        mock_mode: 1,
        mock_delay: taskConfig.value.mockDelay,
      };
      console.log("🎭 启用Mock模式:", options.configurable);
    }

    // 创建任务
    const task = await taskStore.createTask(userInput.value, options);

    // 启动实时流
    await taskStore.startTaskStream(task.query_id);

    // 监听任务完成事件
    if (taskStore.currentEventSource) {
      taskStore.currentEventSource.on("complete", () => {
        setTimeout(() => {
          // 任务完成后保持状态显示
        }, 1000);
      });

      taskStore.currentEventSource.on("error", () => {
        setTimeout(() => {
          // 出错时清除状态
        }, 3000);
      });
    }

    // 清空表单
    userInput.value = "";

    // 通知父组件任务已创建
    emit("task-created", task);

    // 刷新任务列表由父组件处理，不在这里直接调用
  } catch (error) {
    console.error("任务创建失败:", error);
    ElMessage.error("任务创建失败");
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.create-task-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 配置区域样式 */
.config-section {
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  padding: 20px;
  max-height: 120px;
}


/* 查询内容区域样式 */
.queries-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  overflow: hidden;
}


.queries-textarea {
  width: 100%;
  height: 100%;
}

.queries-container {
  flex: 1;
}

</style>
