<template>
  <div class="agent-status-display">
    <!-- 智能体状态展示 -->
    <div v-if="displayTaskModel" class="agent-state-section">
      <h3 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        智能体分析状态
      </h3>

      <!-- Plans 展示 -->
      <div
        v-if="displayTaskModel.plans && displayTaskModel.plans.length > 0"
        class="plans-container"
      >
        <el-collapse v-model="activePlans" class="plans-collapse">
          <el-collapse-item
            v-for="(plan, planIndex) in displayTaskModel.plans"
            :key="planIndex"
            :name="planIndex.toString()"
            class="plan-item"
          >
            <template #title>
              <div class="plan-title">
                <el-icon><Aim /></el-icon>
                <span class="plan-goal">{{ plan.goal }}</span>
                <el-tag
                  :type="getTaskPlanStatusType(plan)"
                  size="small"
                  class="plan-status-tag"
                >
                  {{ getTaskPlanStatusText(plan) }}
                </el-tag>
              </div>
            </template>

            <div class="plan-content">
              <!-- 思路展示 -->
              <div v-if="plan.thinking" class="plan-thinking">
                <div class="thinking-header">
                  <el-icon><Sunny /></el-icon>
                  <span>分析思路</span>
                </div>
                <div class="thinking-content">{{ plan.thinking }}</div>
              </div>

              <!-- 任务列表 -->
              <div
                v-if="plan.steps && plan.steps.length > 0"
                class="tasks-container"
              >
                <div class="tasks-header">
                  <el-icon><List /></el-icon>
                  <span>分析任务</span>
                  <div class="tasks-progress">
                    <el-progress
                      :percentage="getPlanStepsProgress(plan.steps)"
                      :stroke-width="6"
                      :show-text="false"
                      class="progress-bar"
                    />
                    <span class="progress-text">{{
                      getPlanStepsProgressText(plan.steps)
                    }}</span>
                  </div>
                </div>
                <div class="tasks-list">
                  <div
                    v-for="step in plan.steps"
                    :key="step.id"
                    class="task-item"
                    :class="`task-status-${step.status?.toLowerCase()}`"
                  >
                    <div class="task-content">
                      <div
                        class="task-header clickable"
                        @click="toggleTaskResult(step.id)"
                        @keydown.enter="toggleTaskResult(step.id)"
                        @keydown.space.prevent="toggleTaskResult(step.id)"
                        tabindex="0"
                        role="button"
                        :aria-expanded="expandedTasks.includes(step.id)"
                        :aria-label="`任务${step.id}: ${step.query_params?.query_title || '未命名任务'}, 点击查看详细信息`"
                      >
                        <div class="task-title">
                          <span class="task-id">{{ step.id }}.</span>
                          <span class="task-query-title">{{
                            step.query_params?.query_title || "未命名任务"
                          }}</span>
                          <el-icon class="expand-icon">
                            <ArrowDown
                              v-if="!expandedTasks.includes(step.id)"
                            />
                            <ArrowUp v-else />
                          </el-icon>
                        </div>
                        <div class="step-meta">
                          <el-tag
                            :type="getStepStatusShowType(step.status)"
                            size="small"
                            class="task-status-tag"
                          >
                            {{ step.status || "UNKNOWN" }}
                          </el-tag>
                          <span
                            v-if="hasTaskDependencies(step)"
                            class="task-dependencies"
                          >
                            依赖: {{ formatTaskDependencies(step.depends_on) }}
                          </span>
                        </div>
                      </div>

                      <!-- 任务结果展开区域 -->
                      <div
                        v-if="expandedTasks.includes(step.id)"
                        class="task-result"
                      >
                        <div class="result-header">
                          <el-icon><Document /></el-icon>
                          <span>分析结果</span>
                        </div>
                        <div class="result-content">
                          {{ getTaskResult(step) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 无计划时的提示 -->
      <div v-else class="no-plans">
        <el-icon><Loading /></el-icon>
        <span>{{ getNoPlansMessage() }}</span>
      </div>

      <!-- 最终分析汇报 -->
      <div
        v-if="displayTaskModel && displayTaskModel.result"
        class="reporter-section"
      >
        <el-divider content-position="left">
          <div class="reporter-title">
            <el-icon><Document /></el-icon>
            <span>最终分析汇报</span>
          </div>
        </el-divider>

        <div class="reporter-content">
          <!-- 结论先行 -->
          <div class="reporter-item conclusion-item">
            <div class="reporter-item-header">
              <el-icon><Flag /></el-icon>
              <span>结论先行</span>
            </div>
            <div class="reporter-item-content conclusion-content">
              {{ displayTaskModel.result.structured_data.executive_summary }}
            </div>
          </div>

          <!-- 关键发现 -->
          <div
            v-if="
              displayTaskModel.result.structured_data.key_findings &&
              displayTaskModel.result.structured_data.key_findings.length > 0
            "
            class="reporter-item"
          >
            <div class="reporter-item-header">
              <el-icon><Search /></el-icon>
              <span>关键发现</span>
            </div>
            <div class="reporter-item-content">
              <ul class="findings-list">
                <li
                  v-for="finding in displayTaskModel.result.structured_data.key_findings"
                  :key="finding"
                >
                  {{ finding }}
                </li>
              </ul>
            </div>
          </div>

          <!-- 证据要点 -->
          <div
            v-if="
              displayTaskModel.result.structured_data.supporting_evidence &&
              displayTaskModel.result.structured_data.supporting_evidence.length > 0
            "
            class="reporter-item"
          >
            <div class="reporter-item-header">
              <el-icon><DataBoard /></el-icon>
              <span>证据要点</span>
            </div>
            <div class="reporter-item-content">
              <ul class="evidence-list">
                <li
                  v-for="evidence in displayTaskModel.result.structured_data.supporting_evidence"
                  :key="evidence"
                >
                  {{ evidence }}
                </li>
              </ul>
            </div>
          </div>

          <!-- 推理链路 -->
          <div class="reporter-item">
            <div class="reporter-item-header">
              <el-icon><Connection /></el-icon>
              <span>推理链路</span>
            </div>
            <div class="reporter-item-content">
              {{ displayTaskModel.result.structured_data.analysis_reasoning }}
            </div>
          </div>

          <!-- 关键问题详情 -->
          <div class="reporter-item">
            <div class="reporter-item-header">
              <el-icon><Warning /></el-icon>
              <span>关键问题详情</span>
            </div>
            <div class="reporter-item-content critical-issues">
              {{ displayTaskModel.result.structured_data.critical_issues }}
            </div>
          </div>

          <!-- 综合思考 -->
          <div
            v-if="displayTaskModel.result.structured_data.thinking"
            class="reporter-item"
          >
            <div class="reporter-item-header">
              <el-icon><Sunny /></el-icon>
              <span>综合思考</span>
            </div>
            <div class="reporter-item-content">
              {{ displayTaskModel.result.structured_data.thinking }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最新更新展示 -->
    <div
      v-if="showLatestUpdate && latestUpdate && status === 'running'"
      class="latest-update-section"
    >
      <h3 class="section-title">
        <el-icon><Refresh /></el-icon>
        最新进展
      </h3>
      <div class="update-content">
        <div class="update-header">
          <el-tag type="info" size="small">{{
            getMessageTypeLabel(latestUpdate.type)
          }}</el-tag>
          <span class="update-time">{{
            formatTime(latestUpdate.timestamp)
          }}</span>
        </div>
        <div class="update-data">
          <template v-if="latestUpdate.type === 'messages'">
            <div class="messages-content">
              <div
                v-for="(msg, msgIndex) in Array.isArray(latestUpdate.data)
                  ? latestUpdate.data
                  : [latestUpdate.data]"
                :key="msgIndex"
                class="single-message"
              >
                <strong v-if="msg.name">{{ msg.name }}:</strong>
                <div class="message-text">
                  {{ msg.content || JSON.stringify(msg) }}
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="working-indicator">
              <el-icon class="rotating"><Loading /></el-icon>
              <span>智能体正在工作中...</span>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<!--
  AgentStatusDisplay 组件

  功能：展示智能体分析状态，包括分析计划、任务进度、最终汇报等

  使用场景：
  1. TaskCenter页面 - 实时展示任务执行状态
  2. TaskDetail页面 - 展示历史任务的完整状态

  Props:
  - streamMessages: 流式消息数组 (用于实时更新)
  - taskState: 任务状态对象 (用于静态展示)
  - showLatestUpdate: 是否显示最新进展 (默认true)

  特性：
  - 支持任务结果展开/收起
  - 智能进度计算 (考虑任务状态权重)
  - 完整的Reporter汇报展示
  - 可访问性支持 (键盘导航、屏幕阅读器)
  - 错误边界处理
  - 性能优化 (计算属性缓存)
-->

<script setup>
import { ref, computed, watch } from "vue";
import {
  DataAnalysis,
  Aim,
  List,
  Refresh,
  Loading,
  Sunny,
  ArrowDown,
  ArrowUp,
  Document,
  Flag,
  Search,
  DataBoard,
  Connection,
  Warning,
} from "@element-plus/icons-vue";
import { formatTime } from "@/utils/messageUtils";
import { getStreamMessageTypeLabel } from "@/utils/messageUtils";

// 任务状态常量 - 消除魔法字符串，提高代码可维护性
const PLAN_STATUS = {
  running: "running",
  suspended: "suspended", // 一般是服务进程退出了，导致挂起，后续新增恢复执行功能
  done: "done",
  failed: "failed",
};

const STEP_STATUS = {
  pending: "pending",
  ready: "ready",
  running: "running",
  done: "done",
  failed: "failed",
};

// Props
const props = defineProps({
  streamMessages: {
    type: Array,
    default: () => [],
  },
  // 直接传入的state数据（用于任务详情页）
  taskState: {
    type: Object,
    default: null,
  },
  // 是否显示最新进展（任务详情页不需要）
  showLatestUpdate: {
    type: Boolean,
    default: true,
  },
  // 统一状态（用于控制是否显示“正在工作中”）
  status: {
    type: String,
    default: "",
  },
});

// 响应式数据
const activePlans = ref([]);
const expandedTasks = ref([]);

// 计算属性
const latestTask = computed(() => {
  // 如果直接传入了taskState，优先使用
  if (props.taskState) {
    return props.taskState;
  }

  // 否则从streamMessages中获取最新的custom事件数据
  const customMessages = props.streamMessages.filter(
    (msg) => msg.type === "custom",
  );
  if (customMessages.length === 0) return null;

  const latest = customMessages[customMessages.length - 1];

  // 检查是否包含task_state数据（新格式）
  if (latest.data && latest.data.task_state) {
    return latest.data.task_state;
  }

  // 兼容旧格式
  return latest.data || null;
});

// 直接使用latestTask，移除补丁代码
const displayTaskModel = computed(() => {
  return latestTask.value;
});

const latestUpdate = computed(() => {
  // 获取最新的updates事件数据
  const updateMessages = props.streamMessages.filter(
    (msg) => msg.type === "updates",
  );
  if (updateMessages.length === 0) return null;

  return updateMessages[updateMessages.length - 1];
});

const getTaskPlanStatusType = (plan) => {
  switch (plan.status) {
    case PLAN_STATUS.done:
      return "success";
    case PLAN_STATUS.failed:
      return "danger";
    case PLAN_STATUS.running:
      return "primary";
    case PLAN_STATUS.suspended:
      return "warning";
    default:
      return "info";
  }
};

const getTaskPlanStatusText = (plan) => {
  const steps = plan.steps;
  if (!steps || steps.length === 0) return "为什么steps是0";

  const doneCount = steps.filter(
    (step) => step.status === STEP_STATUS.done,
  ).length;
  const totalCount = steps.length;
  const allDone = doneCount === totalCount;
  const hasFailed = steps.some((step) => step.status === STEP_STATUS.failed);
  const hasRunning = steps.some((step) => step.status === STEP_STATUS.running);

  if (allDone) return "已完成";
  if (hasFailed) return "部分失败";
  if (hasRunning) return "执行中";
  return `进行中 (${doneCount}/${totalCount})`;
};

const getStepStatusShowType = (status) => {
  const statusMap = {
    "pending": "info",
    "ready": "primary",
    "running": "warning",
    "done": "success",
    "failed": "danger",
  };
  return statusMap[status] || "info";
};

const getMessageTypeLabel = getStreamMessageTypeLabel;

const getNoPlansMessage = () => {
  // 根据数据源类型显示不同的提示信息
  if (props.taskState) {
    return "该任务暂无分析计划信息";
  }
  return "等待智能体生成分析计划...";
};

const getPlanStepsProgress = (steps) => {
  if (!steps || steps.length === 0) return 0;

  const statusWeights = {
    pending: 0,
    ready: 0.2,
    running: 0.6,
    done: 1,
    failed: 0,
  };

  const totalWeight = steps.reduce((sum, step) => {
    return sum + (statusWeights[step.status] || 0);
  }, 0);

  return Math.round((totalWeight / steps.length) * 100);
};

const getPlanStepsProgressText = (steps) => {
  if (!steps || steps.length === 0) return "0/0";
  const doneCount = steps.filter((step) => step.status === "done").length;
  return `${doneCount}/${steps.length}`;
};

const toggleTaskResult = (taskId) => {
  const index = expandedTasks.value.indexOf(taskId);
  if (index > -1) {
    expandedTasks.value.splice(index, 1);
  } else {
    expandedTasks.value.push(taskId);
  }
};

const hasTaskDependencies = (task) => {
  return (
    task &&
    task.depends_on &&
    Array.isArray(task.depends_on) &&
    task.depends_on.length > 0
  );
};

const formatTaskDependencies = (depends_on) => {
  if (!depends_on || !Array.isArray(depends_on)) return "";
  return depends_on.join(", ");
};

const getTaskResult = (task) => {
  // 获取任务结果内容
  try {
    const result = [];

    // 1. 优先显示worker分析结果 - 修复字段名不匹配问题
    const workerResult = task.result || task.worker_result; // 后端发送result，前端兼容worker_result
    if (workerResult) {
      result.push("=== 分析结果 ===");

      if (workerResult.objective_conclusion) {
        result.push("【客观结论】");
        result.push(workerResult.objective_conclusion);
        result.push("");
      }

      if (workerResult.objective_evidence) {
        result.push("【客观证据】");
        result.push(workerResult.objective_evidence);
        result.push("");
      }

      if (workerResult.subjective_thinking) {
        result.push("【主观分析】");
        result.push(workerResult.subjective_thinking);
        result.push("");
      }
    }

    // 2. 显示查询参数（压缩JSON格式）
    if (task.query_params) {
      result.push("=== 查询参数 ===");
      result.push(JSON.stringify(task.query_params, null, 2));
      result.push("");
    }

    // 3. 如果有查询结果统计信息
    if (task.query_result && task.query_result.data) {
      result.push("=== 查询统计 ===");
      result.push(`返回记录数: ${task.query_result.data.length || 0}`);
      result.push("");
    }

    // 4. 如果什么都没有，显示基本信息
    if (result.length === 0) {
      result.push("任务已完成，暂无详细结果");
    }

    return result.join("\n");
  } catch (error) {
    console.error("Error processing task result:", error);
    return "处理任务结果时发生错误，请稍后重试";
  }
};

// 监听状态变化，自动展开计划
watch(
  displayTaskModel,
  (newState, oldState) => {
    if (newState && newState.plans && newState.plans.length > 0) {
      // 如果没有任何展开的计划，展开第一个计划
      if (activePlans.value.length === 0) {
        activePlans.value = ["0"];
      }
      // 如果计划数量增加了（新增了计划），自动展开最新的计划
      else if (
        oldState &&
        oldState.plans &&
        newState.plans.length > oldState.plans.length
      ) {
        // 找到新增的计划并展开
        const newPlanIndex = (newState.plans.length - 1).toString();
        if (!activePlans.value.includes(newPlanIndex)) {
          activePlans.value.push(newPlanIndex);
        }
      }
    }
  },
  { immediate: true },
);

// 性能监控 (开发环境)
if (import.meta.env.DEV) {
  watch(
    () => props.streamMessages.length,
    (newLength, oldLength) => {
      if (newLength > oldLength) {
        console.log(
          `[AgentStatusDisplay] Stream messages updated: ${oldLength} -> ${newLength}`,
        );
      }
    },
  );
}
</script>

<style scoped>
.agent-status-display {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.plans-container {
  width: 100%;
}

.plans-collapse {
  border: none;
}

.plan-item {
  margin-bottom: 12px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.plan-title {
  padding-left: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.plan-goal {
  flex: 1;
  font-weight: 500;
}

.plan-status-tag {
  margin-left: auto;
}

.plan-content {
  padding-left: 24px;
  background: var(--el-fill-color-lighter);
}

.plan-thinking {
  font-size: 14px;
  margin-bottom: 16px;
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.thinking-content {
  padding: 12px;
  background: var(--el-bg-color);
  border-radius: 6px;
  line-height: 1.6;
  color: var(--el-text-color-regular);
}

.tasks-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.tasks-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.progress-bar {
  width: 80px;
}

.progress-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  min-width: 30px;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  padding: 12px;
  background: var(--el-bg-color);
  border-radius: 6px;
  border-left: 3px solid var(--el-border-color);
  transition: all 0.2s ease;
}

.task-item.task-status-done {
  border-left-color: var(--el-color-success);
}

.task-item.task-status-running {
  border-left-color: var(--el-color-warning);
}

.task-item.task-status-failed {
  border-left-color: var(--el-color-danger);
}

.task-item.task-status-ready {
  border-left-color: var(--el-color-primary);
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.task-header.clickable {
  cursor: pointer;
}

.task-header.clickable:hover {
  background-color: var(--el-fill-color-light);
}

.task-header.clickable:focus {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.expand-icon {
  margin-left: auto;
  color: var(--el-text-color-secondary);
  transition: transform 0.2s ease;
}

.expand-icon:hover {
  color: var(--el-color-primary);
}

.task-id {
  font-weight: 600;
  color: var(--el-text-color-secondary);
  min-width: 24px;
}

.task-query-title {
  flex: 1;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.step-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-dependencies {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.task-result {
  margin-top: 12px;
  padding: 12px;
  background: var(--el-fill-color-extra-light);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-success);
}

.result-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.result-content {
  line-height: 1.6;
  color: var(--el-text-color-regular);
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.no-plans {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px;
  color: var(--el-text-color-secondary);
}

.latest-update-section {
  border-top: 1px solid var(--el-border-color);
  padding-top: 20px;
}

.update-content {
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
}

.update-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.update-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.working-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.messages-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.single-message {
  padding: 8px 12px;
  background: var(--el-bg-color);
  border-radius: 6px;
}

.message-text {
  margin-top: 4px;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Reporter 汇报样式 */
.reporter-section {
  margin-top: 24px;
}

.reporter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.reporter-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.reporter-item {
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  border-left: 4px solid var(--el-color-primary);
}

.reporter-item-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.reporter-item-content {
  line-height: 1.6;
  color: var(--el-text-color-regular);
  white-space: pre-wrap;
}

.findings-list,
.evidence-list {
  margin: 0;
  padding-left: 20px;
}

.findings-list li,
.evidence-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}

/* 结论先行样式 */
.conclusion-item {
  border-left-color: var(--el-color-danger);
}

.conclusion-content {
  color: var(--el-color-danger);
  font-weight: 600;
}
</style>
