<template>
  <div class="workspace-selector">
    <el-select
      v-model="selectedWorkspaceId"
      placeholder="选择工作空间"
      :loading="workspaceStore.loading"
      @change="handleWorkspaceChange"
      class="workspace-select"
    >
      <el-option
        v-for="workspace in workspaceStore.workspaceList"
        :key="workspace.id"
        :label="workspace.name"
        :value="workspace.id"
      >
        <div class="workspace-option">
          <span class="workspace-name">{{ workspace.name }}</span>
          <span class="workspace-desc" v-if="workspace.description">
            {{ workspace.description }}
          </span>
        </div>
      </el-option>
    </el-select>

    <el-button
      type="primary"
      :icon="Plus"
      @click="showCreateDialog = true"
      class="create-workspace-btn"
    >
      新建
    </el-button>

    <!-- 创建工作空间对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建工作空间"
      width="500px"
      :before-close="handleCreateDialogClose"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        label-width="100px"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="createForm.name"
            placeholder="请输入工作空间名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            placeholder="请输入工作空间描述（可选）"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCreateDialogClose">取消</el-button>
          <el-button
            type="primary"
            :loading="workspaceStore.loading"
            @click="handleCreateWorkspace"
          >
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useWorkspaceStore } from "../stores/workspace";

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "change"]);

// Store
const workspaceStore = useWorkspaceStore();

// 响应式数据
const showCreateDialog = ref(false);
const createFormRef = ref(null);
const createForm = ref({
  name: "",
  description: "",
});

// 表单验证规则
const createFormRules = {
  name: [
    { required: true, message: "请输入工作空间名称", trigger: "blur" },
    { min: 2, max: 50, message: "名称长度在 2 到 50 个字符", trigger: "blur" },
  ],
};

// 计算属性
const selectedWorkspaceId = computed({
  get() {
    return workspaceStore.currentWorkspace?.id || null;
  },
  set(value) {
    // 这里不直接设置，而是通过方法处理
  },
});

// 方法
const handleWorkspaceChange = async (workspaceId) => {
  try {
    const workspace = await workspaceStore.switchWorkspace(workspaceId);
    emit("update:modelValue", workspace);
    emit("change", workspace);
  } catch (error) {
    console.error("切换工作空间失败:", error);
  }
};

const handleCreateWorkspace = async () => {
  try {
    // 验证表单
    const valid = await createFormRef.value.validate();
    if (!valid) return;

    // 创建工作空间
    const workspace = await workspaceStore.createWorkspace({
      name: createForm.value.name,
      description: createForm.value.description,
    });

    // 关闭对话框
    showCreateDialog.value = false;

    // 重置表单
    createForm.value = {
      name: "",
      description: "",
    };

    // 切换到新创建的工作空间
    await workspaceStore.switchWorkspace(workspace.id);
    emit("update:modelValue", workspace);
    emit("change", workspace);
  } catch (error) {
    console.error("创建工作空间失败:", error);
  }
};

const handleCreateDialogClose = () => {
  showCreateDialog.value = false;
  // 重置表单
  createForm.value = {
    name: "",
    description: "",
  };
  // 清除验证状态
  if (createFormRef.value) {
    createFormRef.value.clearValidate();
  }
};

// 生命周期
onMounted(async () => {
  // 初始化工作空间数据
  await workspaceStore.initialize();

  // 如果有当前工作空间，触发change事件
  if (workspaceStore.currentWorkspace) {
    emit("update:modelValue", workspaceStore.currentWorkspace);
    emit("change", workspaceStore.currentWorkspace);
  }
});
</script>

<style scoped>
.workspace-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.workspace-select {
  min-width: 200px;
}

.workspace-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.workspace-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.workspace-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.2;
}

.create-workspace-btn {
  flex-shrink: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
