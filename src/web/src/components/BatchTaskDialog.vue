<template>
  <div class="batch-task-content">
    <!-- 执行配置区域 -->
    <div class="config-section">
      <TaskConfig v-model="taskConfig" />
    </div>

    <!-- 查询内容输入区域 -->
    <div class="queries-section">
      <div class="queries-header">
        <span class="query-count">
          共
          {{ form.queries.split("\n").filter((q) => q.trim()).length }} 个查询
        </span>
        <el-dropdown @command="handleTemplateCommand" trigger="click">
          <el-button size="small" type="default">
            <el-icon><Files /></el-icon>
            模板
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <!-- 动态生成菜单项 -->
              <template v-for="(item, index) in templateMenuItems" :key="index">
                <!-- 模板项 -->
                <el-dropdown-item
                  v-if="item.type === 'template'"
                  :command="item.command"
                >
                  <el-icon>
                    <component :is="getIconComponent(item.icon)" />
                  </el-icon>
                  {{ item.name }}
                  <span class="query-count-badge">{{ item.queryCount }}</span>
                </el-dropdown-item>

                <!-- 分割线 -->
                <el-dropdown-item
                  v-else-if="item.type === 'divider'"
                  divided
                  disabled
                  style="height: 1px; padding: 0; margin: 4px 0;"
                />

                <!-- 动作项 -->
                <el-dropdown-item
                  v-else-if="item.type === 'action'"
                  :command="item.command"
                  :divided="index > 0 && templateMenuItems[index - 1]?.type !== 'divider'"
                >
                  {{ item.name }}
                </el-dropdown-item>
              </template>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="queries-input-container">
        <el-input
          v-model="form.queries"
          type="textarea"
          :rows="5"
          placeholder="每行一个查询内容，支持多个查询批量执行&#10;&#10;例如：&#10;分析1850816294推流成功率&#10;分析1850816294拉流成功率&#10;分析1850816294推流错误码分布"
          maxlength="5000"
          show-word-limit
          class="queries-textarea"
          :autosize="{ minRows: 2, maxRows: 5 }"
        />
      </div>

      <!-- 提交按钮区域 -->
      <div class="submit-section">
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="!canCreateBatch"
          size="large"
          style="width: 100%"
        >
          <el-icon><VideoPlay /></el-icon>
          批量启动任务
          <span
            v-if="form.queries.split('\n').filter((q) => q.trim()).length > 0"
          >
            ({{ form.queries.split("\n").filter((q) => q.trim()).length }}个)
          </span>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import {
  ArrowDown,
  Files,
  VideoPlay,
  Document,
  Search
} from "@element-plus/icons-vue";
import TaskConfig from "./TaskConfig.vue";
import { issueApi } from "../api/index.js";

// 响应式数据
const loading = ref(false);
const progress = ref(0);
const templateMenuItems = ref([]);
const historyIssues = ref([]);
const issuesLoading = ref(false);
const issuesError = ref(null);

// Props
const props = defineProps({
  workspaceId: {
    type: [String, Number],
    default: null,
  },
});

// Emits
const emit = defineEmits(["task-created"]);

// 从API加载问题数据
const loadHistoryIssues = async () => {
  try {
    issuesLoading.value = true;
    issuesError.value = null;

    // 获取问题列表数据
    const response = await issueApi.getIssueList({ limit: 100 });

    if (!response || !response.data) {
      throw new Error('API返回数据格式错误');
    }

    // 按分类分组问题
    const categorizedIssues = {};
    const allQuestions = [];

    response.data.forEach(issue => {
      const categoryType = issue.category_type;
      if (!categorizedIssues[categoryType]) {
        categorizedIssues[categoryType] = {
          name: issue.category_name,
          queries: []
        };
      }

      // 添加"请"前缀以保持与原有格式一致
      const formattedQuestion = issue.question.startsWith('请')
        ? issue.question
        : `请${issue.question}`;

      categorizedIssues[categoryType].queries.push(formattedQuestion);
      allQuestions.push(formattedQuestion);
    });

    console.log('从API加载的问题数量:', allQuestions.length);
    console.log('分类信息:', Object.keys(categorizedIssues));

    historyIssues.value = allQuestions;

    // 生成模板菜单项
    const menuItems = [];

    // 为每个分类创建模板项
    Object.entries(categorizedIssues).forEach(([categoryType, categoryData]) => {
      const iconName = categoryType === 'test' ? 'Search' : 'Document';

      menuItems.push({
        type: 'template',
        command: categoryType,
        name: categoryData.name,
        icon: iconName,
        queryCount: categoryData.queries.length,
        queries: categoryData.queries
      });
    });

    // 添加分割线和重置操作
    menuItems.push(
      { type: 'divider' },
      {
        type: 'action',
        command: 'clear',
        name: '重置为默认查询'
      }
    );

    templateMenuItems.value = menuItems;

  } catch (error) {
    console.error('加载问题数据失败:', error);
    issuesError.value = error.message || '加载问题数据失败';

    // 发生错误时使用默认数据
    ElMessage.warning(`加载问题库失败: ${error.message}，使用默认数据`);
    loadDefaultIssues();
  } finally {
    issuesLoading.value = false;
  }
};

// 默认问题数据（作为回退）
const loadDefaultIssues = () => {
  const testQueries = [
    "请检查 1850816294 2025年8月28日前一个月内，推流是否正常"
  ];

  const historyQueries = [
    "请检查 3918244832 (会玩-密室杀) 2025年1月22日前一个月内，拉流是否正常",
    "请检查 1648494736 (Mango_Prod) 2025年2月28日前一个月内，登录是否正常",
    "请检查 1734496943 (H项目) 2025年3月5日前一个月内，服务是否正常"
  ];

  historyIssues.value = [...testQueries, ...historyQueries];

  templateMenuItems.value = [
    {
      type: 'template',
      command: 'test',
      name: '测试问题模板',
      icon: 'Search',
      queryCount: testQueries.length,
      queries: testQueries
    },
    {
      type: 'template',
      command: 'history',
      name: '历史问题模板',
      icon: 'Document',
      queryCount: historyQueries.length,
      queries: historyQueries
    },
    {
      type: 'divider'
    },
    {
      type: 'action',
      command: 'clear',
      name: '重置为默认查询'
    }
  ];
};

// 初始化
onMounted(() => {
  loadHistoryIssues();
});

// 图标映射
const iconMap = {
  Search,
  Document
};

// 计算属性
const canCreateBatch = computed(() => {
  const queries = form.value.queries
    .split("\n")
    .map((q) => q.trim())
    .filter((q) => q.length > 0);
  return queries.length > 0;
});

// 获取图标组件
const getIconComponent = (iconName) => {
  return iconMap[iconName] || Document;
};

// 默认查询
const DEFAULT_QUERIES = [
  "分析1850816294推流成功率",
  "分析1850816294推流rtc子事件成功率", 
  "分析1850816294拉流成功率",
  "分析1850816294端到端丢包率"
];

// 配置对象
const taskConfig = ref({
  maxParallelWorkers: 5,
  recursionLimit: 30,
  enableMock: false,
  mockDelay: 2,
});

// 表单数据
const form = ref({
  queries: DEFAULT_QUERIES.join('\n'),
});

// 模板处理方法
const handleTemplateCommand = (command) => {
  if (command === 'clear') {
    // 处理重置操作
    form.value.queries = DEFAULT_QUERIES.join('\n');

    // 重置配置
    taskConfig.value.maxParallelWorkers = 5;
    taskConfig.value.recursionLimit = 30;
    taskConfig.value.enableMock = false;
    taskConfig.value.mockDelay = 2;

    ElMessage.success("已重置为默认查询");
    return;
  }

  // 查找对应的模板
  const template = templateMenuItems.value.find(item => 
    item.type === 'template' && item.command === command
  );

  if (!template) {
    ElMessage.error("模板不存在或已失效");
    return;
  }

  // 应用模板查询
  form.value.queries = template.queries.join('\n');
  ElMessage.success(
    `已加载 ${template.queryCount} 个查询 (${template.name})`
  );
};

// 提交批量创建任务
const handleSubmit = async () => {
  const queries = form.value.queries
    .split("\n")
    .map((q) => q.trim())
    .filter((q) => q.length > 0);

  if (queries.length === 0) {
    ElMessage.warning("请至少输入一个查询内容");
    return;
  }

  const MAX_BATCH_SIZE = 100;
  if (queries.length > MAX_BATCH_SIZE) {
    ElMessage.warning(
      `批量任务数量不能超过${MAX_BATCH_SIZE}个，当前有${queries.length}个查询`,
    );
    return;
  }

  try {
    loading.value = true;
    progress.value = 0;


    const tasksWithConfig = {
      user_queries: queries,
      max_parallel_workers: taskConfig.value.maxParallelWorkers,
      recursion_limit: taskConfig.value.recursionLimit,
      workspace_id: props.workspaceId || 2,
      ...(taskConfig.value.enableMock && {
        configurable: {
          mock_mode: 1,
          mock_delay: taskConfig.value.mockDelay,
        },
      }),
    };

    const response = await fetch("/api/tasks/batch-create", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(tasksWithConfig),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    const totalTasks = queries.length;
    for (let i = 0; i <= totalTasks; i++) {
      progress.value = Math.round((i / totalTasks) * 100);
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    const successCount = result.created_tasks.length;
    const failedCount = result.failed_tasks.length;

    if (successCount > 0) {
      ElMessage.success(
        `成功启动 ${successCount} 个任务${failedCount > 0 ? `，${failedCount} 个任务启动失败` : ""}`,
      );
    }

    if (failedCount > 0) {
      console.error("Failed tasks:", result.failed_tasks);
      ElMessage.warning(`${failedCount} 个任务启动失败，请查看控制台了解详情`);
    }

    const firstTaskId = result.created_tasks[0]?.id|| "";
    const lastTaskId =
      result.created_tasks[result.created_tasks.length - 1]?.id || "";
    const batchInfo =
      successCount > 1
        ? `任务范围：${firstTaskId} ~ ${lastTaskId}`
        : `任务ID：${firstTaskId}`;

    ElNotification({
      title: "批量任务创建成功",
      message: `成功创建：${successCount} 个任务\n${batchInfo}`,
      type: "success",
      duration: 5000,
    });

    emit("task-created", result.created_tasks);

    // 重置表单
    form.value = {
      queries: DEFAULT_QUERIES.join('\n'),
    };
    taskConfig.value = {
      maxParallelWorkers: 5,
      recursionLimit: 30,
      enableMock: false,
      mockDelay: 2,
    };
  } catch (error) {
    console.error("Batch create error:", error);
    ElMessage.error("批量启动任务失败: " + error.message);
  } finally {
    loading.value = false;
    progress.value = 0;
  }
};

// 暴露组件状态
defineExpose({
  form,
  loading,
  progress,
});
</script>

<style scoped>
.batch-task-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 配置区域样式 */
.config-section {
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  padding: 20px;
  max-height: 120px;
}



/* 查询内容区域样式 */
.queries-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  overflow: hidden;
}

/* 提交按钮区域样式 */
.submit-section {
  margin-top: 20px;
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-lighter);
}

.queries-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.query-count {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.queries-input-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.queries-textarea {
  width: 100%;
  height: 100%;
}

.queries-textarea :deep(.el-textarea__inner) {
  width: 100%;
  height: 100%;
  min-height: 400px;
  resize: vertical;
  font-family: "JetBrains Mono", "Fira Code", "Consolas", monospace;
  font-size: 14px;
  line-height: 1.6;
}

/* 模板菜单样式 */
:deep(.el-dropdown-menu) {
  --el-dropdown-menuItem-hover-fill: var(--el-color-primary-light-9);
  --el-dropdown-menuItem-hover-color: var(--el-color-primary);
}

/* 查询数量徽章样式 */
:deep(.query-count-badge) {
  margin-left: auto;
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

/* 模板菜单图标样式 */
:deep(.el-dropdown-menu .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}


</style>
