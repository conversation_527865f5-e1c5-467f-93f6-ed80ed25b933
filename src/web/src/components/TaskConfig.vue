<template>
  <div class="task-config">
    <div class="config-row">
      <div class="config-item">
        <label class="config-label">并行数</label>
        <el-input-number
          v-model="configData.maxParallelWorkers"
          :min="1"
          :max="20"
          size="small"
          controls-position="right"
          :disabled="disabled"
        />
      </div>

      <div class="config-item">
        <label class="config-label">递归限制</label>
        <el-input-number
          v-model="configData.recursionLimit"
          :min="10"
          :max="500"
          size="small"
          controls-position="right"
          :disabled="disabled"
        />
      </div>

      <div class="config-item">
        <el-checkbox
          v-model="configData.enableMock"
          size="large"
          :disabled="disabled"
        >
          <span class="checkbox-label">Mock模式</span>
        </el-checkbox>
      </div>

      <div v-if="configData.enableMock" class="config-item">
        <label class="config-label">延迟时间</label>
        <el-input-number
          v-model="configData.mockDelay"
          :min="0.5"
          :max="10"
          :step="0.5"
          size="small"
          controls-position="right"
          :disabled="disabled"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      maxParallelWorkers: 5,
      recursionLimit: 30,
      enableMock: false,
      mockDelay: 2,
    }),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["update:modelValue"]);

// 计算属性 - 双向绑定
const configData = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});
</script>

<style scoped>
.task-config {
  width: 100%;
}

.config-row {
  display: flex;
  gap: 24px;
  align-items: flex-end;
  flex-wrap: wrap;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;
}

.config-label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.checkbox-label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.unit {
  margin-left: 8px;
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-row {
    flex-direction: column;
    align-items: stretch;
  }

  .config-item {
    min-width: auto;
  }
}
</style>
