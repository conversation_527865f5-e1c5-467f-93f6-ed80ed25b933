import { createRouter, createWebHistory } from "vue-router";
import TaskCenter from "@/views/TaskCenter.vue";
import TaskDetail from "@/views/TaskDetail.vue";

const routes = [
  {
    path: "/",
    name: "TaskCenter",
    component: TaskCenter,
    meta: { title: "任务中心" },
  },
  {
    path: "/task/:queryId",
    name: "TaskDetail",
    component: TaskDetail,
    meta: { title: "任务详情" },
  },
  // 重定向旧的历史记录路由到任务中心
  {
    path: "/history",
    redirect: "/",
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
