<template>
  <div id="app" class="app-container">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="logo">
            <el-icon><DataAnalysis /></el-icon>
            <span>QFlowAgent</span>
          </div>
          <div class="header-nav">
            <div class="nav-links">
              <router-link
                to="/"
                class="nav-link"
                :class="{ active: $route.path === '/' }"
              >
                <el-icon><House /></el-icon>
                <span>任务中心</span>
              </router-link>
              <a
                href="http://localhost:2026/docs"
                target="_blank"
                class="nav-link external"
              >
                <el-icon><Document /></el-icon>
                <span>API文档</span>
                <el-icon class="external-icon"><TopRight /></el-icon>
              </a>
            </div>
          </div>
          <div class="header-actions">
            <el-button
              :icon="isDark ? 'Sunny' : 'Moon'"
              circle
              @click="toggleDark"
              title="切换主题"
            />
          </div>
        </div>
      </el-header>

      <!-- 主体内容 -->
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import {
  DataAnalysis,
  House,
  Document,
  TopRight,
} from "@element-plus/icons-vue";

const isDark = ref(true);

const toggleDark = () => {
  isDark.value = !isDark.value;
  if (isDark.value) {
    document.documentElement.classList.add("dark");
  } else {
    document.documentElement.classList.remove("dark");
  }
};

onMounted(() => {
  // 确保默认是暗黑模式
  document.documentElement.classList.add("dark");
});
</script>

<style scoped>
.app-container {
  height: 100vh;
  background: var(--el-bg-color);
}

.app-header {
  border-bottom: 1px solid var(--el-border-color);
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.header-nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--el-color-primary);
  background: var(--el-fill-color-light);
}

.nav-link.active {
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.nav-link.external {
  color: var(--el-text-color-secondary);
}

.nav-link.external:hover {
  color: var(--el-color-primary);
}

.external-icon {
  font-size: 12px;
  opacity: 0.6;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: bold;
  color: var(--el-color-primary);
}

.app-main {
  padding: 20px;
  background: var(--el-bg-color);
}
</style>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

#app {
  height: 100vh;
}
</style>
