import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { workspaceApi } from "../api/workspace";

export const useWorkspaceStore = defineStore("workspace", () => {
  // 状态
  const currentWorkspace = ref(null);
  const workspaceList = ref([]);
  const loading = ref(false);

  // 计算属性
  const currentWorkspaceId = computed(() => currentWorkspace.value?.id || null);
  const currentWorkspaceName = computed(
    () => currentWorkspace.value?.name || "未选择工作空间",
  );

  // 获取工作空间列表
  const fetchWorkspaces = async () => {
    try {
      loading.value = true;
      const response = await workspaceApi.getWorkspaces();
      workspaceList.value = response.workspaces || [];

      // 如果没有当前工作空间，选择第一个
      if (!currentWorkspace.value && workspaceList.value.length > 0) {
        currentWorkspace.value = workspaceList.value[0];
      }

      return workspaceList.value;
    } catch (error) {
      console.error("获取工作空间列表失败:", error);
      ElMessage.error("获取工作空间列表失败");
      return [];
    } finally {
      loading.value = false;
    }
  };

  // 切换工作空间
  const switchWorkspace = async (workspaceId) => {
    try {
      const workspace = workspaceList.value.find((w) => w.id === workspaceId);
      if (!workspace) {
        throw new Error(`工作空间 ${workspaceId} 不存在`);
      }

      currentWorkspace.value = workspace;

      // 触发其他store的数据重新加载
      // 注意：这里不直接调用taskStore，避免循环依赖
      // 由组件层面处理任务列表的重新加载

      ElMessage.success(`已切换到工作空间：${workspace.name}`);
      return workspace;
    } catch (error) {
      console.error("切换工作空间失败:", error);
      ElMessage.error("切换工作空间失败");
      throw error;
    }
  };

  // 创建工作空间
  const createWorkspace = async (data) => {
    try {
      loading.value = true;
      const workspace = await workspaceApi.createWorkspace(data);

      // 添加到列表
      workspaceList.value.push(workspace);

      ElMessage.success(`工作空间 "${workspace.name}" 创建成功`);
      return workspace;
    } catch (error) {
      console.error("创建工作空间失败:", error);
      ElMessage.error("创建工作空间失败");
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 更新工作空间
  const updateWorkspace = async (workspaceId, data) => {
    try {
      loading.value = true;
      const workspace = await workspaceApi.updateWorkspace(workspaceId, data);

      // 更新列表中的数据
      const index = workspaceList.value.findIndex((w) => w.id === workspaceId);
      if (index !== -1) {
        workspaceList.value[index] = workspace;
      }

      // 如果是当前工作空间，也要更新
      if (currentWorkspace.value?.id === workspaceId) {
        currentWorkspace.value = workspace;
      }

      ElMessage.success(`工作空间 "${workspace.name}" 更新成功`);
      return workspace;
    } catch (error) {
      console.error("更新工作空间失败:", error);
      ElMessage.error("更新工作空间失败");
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 删除工作空间
  const deleteWorkspace = async (workspaceId) => {
    try {
      loading.value = true;
      await workspaceApi.deleteWorkspace(workspaceId);

      // 从列表中移除
      workspaceList.value = workspaceList.value.filter(
        (w) => w.id !== workspaceId,
      );

      // 如果删除的是当前工作空间，切换到第一个
      if (currentWorkspace.value?.id === workspaceId) {
        currentWorkspace.value =
          workspaceList.value.length > 0 ? workspaceList.value[0] : null;
      }

      ElMessage.success("工作空间删除成功");
    } catch (error) {
      console.error("删除工作空间失败:", error);
      ElMessage.error("删除工作空间失败");
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取工作空间统计
  const getWorkspaceStats = async (workspaceId) => {
    try {
      return await workspaceApi.getWorkspaceStats(workspaceId);
    } catch (error) {
      console.error("获取工作空间统计失败:", error);
      return null;
    }
  };

  // 初始化
  const initialize = async () => {
    await fetchWorkspaces();
  };

  return {
    // 状态
    currentWorkspace,
    workspaceList,
    loading,

    // 计算属性
    currentWorkspaceId,
    currentWorkspaceName,

    // 方法
    fetchWorkspaces,
    switchWorkspace,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    getWorkspaceStats,
    initialize,
  };
});
