import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { taskApi, TaskEventSource } from "@/api";

export const useTaskStore = defineStore("task", () => {
  // 状态
  const tasks = ref([]);
  const currentTask = ref(null);
  const loading = ref(false);
  const currentEventSource = ref(null);
  const streamMessages = ref([]);

  // 分页状态
  const totalCount = ref(0);
  const currentPage = ref(1);
  const pageSize = ref(10);

  // 计算属性
  const runningTasks = computed(() =>
    tasks.value.filter((task) => task.status === "running"),
  );

  const completedTasks = computed(() =>
    tasks.value.filter((task) => task.status === "completed"),
  );

  // 方法
  const createTask = async (userInput, options = {}) => {
    try {
      loading.value = true;

      // 构建请求参数
      const requestData = {
        user_query: userInput,
        workspace_id: options.workspaceId || 2, // 使用默认工作空间
        max_parallel_workers: options.maxParallelWorkers || 5,
        recursion_limit: options.recursionLimit || 30,
      };

      // 添加configurable参数（如Mock模式配置）
      if (options.configurable) {
        requestData.configurable = options.configurable;
        console.log("📤 发送configurable配置:", options.configurable);
      }

      const task = await taskApi.createTask(requestData);

      // 添加到任务列表
      tasks.value.unshift(task);
      return task;
    } catch (error) {
      console.error("Failed to create task:", error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  const loadTasks = async (params = {}) => {
    try {
      loading.value = true;
      const response = await taskApi.getTasks(params);

      // 正确处理后端返回的分页响应
      if (response && typeof response === 'object') {
        tasks.value = response.tasks || [];
        totalCount.value = response.total_count || 0;
        currentPage.value = response.page || 1;
        pageSize.value = response.page_size || 10;
      } else {
        // 向后兼容旧格式
        tasks.value = Array.isArray(response) ? response : [];
        totalCount.value = tasks.value.length;
      }

      return response;
    } catch (error) {
      console.error("Failed to load tasks:", error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  const loadTaskDetail = async (queryId) => {
    try {
      loading.value = true;
      const task = await taskApi.getTaskDetail(queryId);
      currentTask.value = task;
      return task;
    } catch (error) {
      console.error("Failed to load task detail:", error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  const startTaskStream = async (queryId) => {
    // 清理之前的连接
    if (currentEventSource.value) {
      currentEventSource.value.close();
    }

    // 清空流消息
    streamMessages.value = [];

    // 创建新的EventSource连接
    const eventSource = new TaskEventSource(queryId);
    currentEventSource.value = eventSource;

    // 监听不同类型的事件
    eventSource.on("values", (event) => {
      console.log("Task values:", event);
      streamMessages.value.push({
        type: "values",
        data: event.value,
        query_id: event.query_id,
        timestamp: new Date(event.timestamp),
      });
    });

    eventSource.on("messages", (event) => {
      console.log("Task messages:", event);
      streamMessages.value.push({
        type: "messages",
        data: event.value,
        query_id: event.query_id,
        timestamp: new Date(event.timestamp),
      });
    });

    eventSource.on("updates", (event) => {
      console.log("Task updates:", event);
      streamMessages.value.push({
        type: "updates",
        data: event.value,
        query_id: event.query_id,
        timestamp: new Date(event.timestamp),
      });
    });

    eventSource.on("custom", (event) => {
      console.log("Task custom:", event);
      streamMessages.value.push({
        type: "custom",
        data: event.value,
        query_id: event.query_id,
        timestamp: new Date(event.timestamp),
      });
    });

    eventSource.on("success", (event) => {
      console.log("Task completed:", event);
      streamMessages.value.push({
        type: "success",
        data: event.value,
        query_id: event.query_id,
        timestamp: new Date(event.timestamp),
      });

      // 更新任务状态
      const taskIndex = tasks.value.findIndex((t) => t.query_id === queryId);
      if (taskIndex > -1) {
        tasks.value[taskIndex].status = "completed";
      }
    });

    eventSource.on("error", (event) => {
      console.error("Task error:", event);
      streamMessages.value.push({
        type: "error",
        data: event.value,
        query_id: event.query_id,
        timestamp: new Date(event.timestamp),
      });
    });

    eventSource.on("close", () => {
      console.log("EventSource closed");
      // currentEventSource.value = null; 即使关闭也要展示结果给用户
    });

    // 启动任务流
    try {
      await eventSource.startTask();
      return eventSource;
    } catch (error) {
      console.error("Failed to start task stream:", error);
      throw error;
    }
  };

  const stopTaskStream = () => {
    if (currentEventSource.value) {
      currentEventSource.value.close();
      currentEventSource.value = null;
    }
  };

  const updateTaskStatus = (queryId, status) => {
    const taskIndex = tasks.value.findIndex((t) => t.query_id === queryId);
    if (taskIndex > -1) {
      tasks.value[taskIndex].status = status;
    }
  };

  const clearStreamMessages = () => {
    streamMessages.value = [];
  };

  return {
    // 状态
    tasks,
    currentTask,
    loading,
    currentEventSource,
    streamMessages,

    // 分页状态
    totalCount,
    currentPage,
    pageSize,

    // 计算属性
    runningTasks,
    completedTasks,

    // 方法
    createTask,
    loadTasks,
    loadTaskDetail,
    startTaskStream,
    stopTaskStream,
    updateTaskStatus,
    clearStreamMessages,
  };
});
