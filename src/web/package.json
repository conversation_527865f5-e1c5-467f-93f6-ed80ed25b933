{"name": "qflow-agent-web", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "js-yaml": "^4.1.0", "pinia": "^2.1.7", "uuid": "^11.1.0", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "vite": "^5.0.8"}}