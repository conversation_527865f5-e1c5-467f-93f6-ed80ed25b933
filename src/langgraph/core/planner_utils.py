from __future__ import annotations

from typing import Dict, List, Set, Tuple

from langchain_core.messages import AIMessage

from src.langgraph.core.common.types import DAGPlan, DAGPlanStep, WorkerResult


def summarize_steps(steps: List[DAGPlanStep]) -> str:
    """返回任务的简洁编号摘要，包含状态、依赖与查询标题。

    - 使用任务 id 表示依赖
    """
    if not steps:
        return "无任务"

    lines: List[str] = []
    for index, step in enumerate(steps, start=1):
        status_text = f" [{step.status}]" if getattr(step, "status", None) else ""

        display = step.query_params.query_title
        dep_ids = getattr(step, "depends_on", []) or []
        depends_text = "无" if not dep_ids else "、".join([str(d) for d in dep_ids])
        line = f"{index}. {display}{status_text}（依赖：{depends_text}，ID={getattr(step, 'id', None)}）"
        lines.append(line)

    return "\n".join(lines)


def compute_dependency_layers(steps: List[DAGPlanStep]) -> List[List[int]]:
    """按任务 id 计算分层顺序（Kahn 算法）。

    - 仅考虑给定任务集合内的依赖边
    - 若存在环或未解析的依赖，剩余节点置于最后一层
    """
    id_to_step: Dict[int, DAGPlanStep] = {int(t.id): t for t in steps}
    if not id_to_step:
        return []

    indegree: Dict[int, int] = {tid: 0 for tid in id_to_step.keys()}
    adjacency: Dict[int, List[int]] = {tid: [] for tid in id_to_step.keys()}

    for step in steps:
        tid = int(getattr(step, "id"))
        for dep in getattr(step, "depends_on", []) or []:
            if dep in id_to_step:
                indegree[tid] += 1
                adjacency[dep].append(tid)

    layers: List[List[int]] = []
    current_layer = sorted([t for t, d in indegree.items() if d == 0])
    visited: Set[int] = set()
    while current_layer:
        layers.append(current_layer)
        visited.update(current_layer)
        next_indegree = indegree.copy()
        for u in current_layer:
            for v in adjacency.get(u, []):
                next_indegree[v] = max(0, next_indegree[v] - 1)
        next_layer = sorted([t for t, d in next_indegree.items() if d == 0 and t not in visited])
        if not next_layer:
            remaining = sorted([t for t in indegree.keys() if t not in visited])
            if remaining:
                layers.append(remaining)
            break
        indegree = next_indegree
        current_layer = next_layer

    return layers


def format_layers(layers: List[List[int]]) -> str:
    if not layers:
        return "无依赖层级"
    return "\n".join([f"第{idx}层：" + ", ".join([str(x) for x in layer]) for idx, layer in enumerate(layers, start=1)])


def build_plan_message(plan: DAGPlan, extra_kwargs: dict | None = None) -> AIMessage:
    """Create a very concise plan message suitable for Feishu.

    Goals:
    - Keep it short; only convey what the model intends to do next
    - Show goal, first-phase steps, and a brief note about subsequent steps
    """
    extra_kwargs = extra_kwargs or {}

    def _truncate(text: str, max_len: int = 120) -> str:
        if not text:
            return ""
        return text if len(text) <= max_len else text[: max_len - 1] + "…"

    steps = getattr(plan, "steps", []) or []
    layers = compute_dependency_layers(steps)
    num_layers = len(layers)

    # 映射：id -> 可读名称
    id_to_display: Dict[int, str] = {int(t.id): t.query_params.query_title for t in steps}

    # 按层逐层展示任务标题，保证与派发轮次一致
    layer_lines: List[str] = []
    for idx, layer in enumerate(layers, start=1):
        names = [id_to_display.get(t, str(t)) for t in layer]
        titles = "，".join(names) if names else "无"
        layer_lines.append(f"第{idx}层：{titles}")
    layers_text = "\n".join(layer_lines) if layer_lines else "无"

    # Build content
    goal_text = _truncate(getattr(plan, "goal", "") or "")

    ai_message = AIMessage(name="planner", content="")
    ai_message.additional_kwargs = {
        "object_type": plan.__class__.__name__,
        "object": plan,
        **extra_kwargs,
    }
    ai_message.content = (
        f"🎯 目标：{goal_text} \n 📋 计划：{len(steps)} 个任务，{num_layers} 层 \n🧩 分层预览：\n{layers_text}"
    ).strip()
    return ai_message


# ---------- Planner helpers ----------


def validate_schema_ids(dag_plan_schema: DAGPlan) -> Tuple[bool, str]:
    """校验 LLM 输出的任务 ID：
    - 必须为正整数
    - 在一次规划内唯一

    返回 (是否有效, 警告/错误文本)。
    """
    steps = getattr(dag_plan_schema, "steps", []) or []
    seen: Set[int] = set()
    duplicates: List[int] = []
    invalid: List[str] = []
    for t in steps:
        try:
            tid = int(getattr(t, "id"))
        except Exception:
            invalid.append(str(getattr(t, "id", None)))
            continue
        if tid <= 0:
            invalid.append(str(tid))
            continue
        if tid in seen:
            duplicates.append(tid)
        else:
            seen.add(tid)

    messages: List[str] = []
    if invalid:
        messages.append(f"- 非法ID: {sorted(set(invalid))}")
    if duplicates:
        messages.append(f"- 重复ID: {sorted(set(duplicates))}")

    if messages:
        return False, "\n".join(messages)
    return True, ""


def get_completed_ids(steps: List[DAGPlanStep]) -> Set[int]:
    return {int(getattr(t, "id")) for t in steps if getattr(t, "status", None) in ("DONE", "FAILED")}


def get_ready_steps_by_id(steps: List[DAGPlanStep]) -> List[DAGPlanStep]:
    steps = steps or []
    completed_ids = get_completed_ids(steps)
    ready: List[DAGPlanStep] = []
    for step in steps:
        st = getattr(step, "status", None) or "PENDING"
        if st in ("DONE", "FAILED", "RUNNING"):
            continue
        deps = [int(d) for d in (getattr(step, "depends_on", []) or [])]
        # 依赖已满足的任务标记 READY
        if all(dep in completed_ids for dep in deps):
            step.status = "READY"
            ready.append(step)
        else:
            # 依赖未满足则保持/置为 PENDING
            if st not in ("READY", "RUNNING", "DONE", "FAILED"):
                step.status = "PENDING"
    return ready


def get_results_by_steps(steps: List[DAGPlanStep], worker_results) -> List[WorkerResult]:
    results: List[WorkerResult] = []
    for t in steps:
        qp = getattr(t, "query_params", None)
        if qp is None:
            continue
        try:
            key = qp.to_key()
            info = worker_results.get(key)
            if info is not None:
                results.append(info)
        except Exception:
            continue
    return results
