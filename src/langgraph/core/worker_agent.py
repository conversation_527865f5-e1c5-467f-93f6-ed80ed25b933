from __future__ import annotations

import logging

from typing import Dict

from langgraph.config import get_config

from langgraph.func import task

from src.langgraph.common.utils.config_utils import get_mock_delay, is_mock_mode
from src.langgraph.common.utils.context_builder import ContextBuilder

from src.langgraph.common.utils.llm_request_utils import create_token_update, llm_structured_request
from src.langgraph.core.common.data_center import DataCenter
from src.langgraph.core.common.types import DAGPlanStep, RetryParams, WorkerResult

# Mock模式支持
from src.langgraph.mock import MockDataGenerator

from src.langgraph.state import State

from src.langgraph.zego_tools.ocean_executor import SqlExecutorQueryResult
from src.storage.services.storage_service import db_update_step_status

logger = logging.getLogger(__name__)


async def _mock_worker_step(state: State, current_step: DAGPlanStep) -> Dict:
    """Mock模式的任务执行"""
    query_params = current_step.query_params
    if query_params:
        analysis_name = getattr(query_params, "query_title", None) or getattr(query_params, "metric_name", "unknown")
    else:
        analysis_name = "unknown"

    # 获取mock延迟时间
    delay = get_mock_delay(get_config())
    await MockDataGenerator.mock_delay(delay)

    # 获取任务信息
    step_title = getattr(query_params, "query_title", "Mock任务") if query_params else "Mock任务"
    step_id = current_step.id

    # 生成mock结果
    mock_result_data = MockDataGenerator.generate_mock_worker_result(
        step_title=step_title, current_round=current_step.round, step_id=step_id
    )

    # 创建WorkerResult对象
    worker_result = WorkerResult(
        objective_conclusion=mock_result_data["objective_conclusion"],
        objective_evidence=mock_result_data["objective_evidence"],
        subjective_thinking=mock_result_data["subjective_thinking"],
    )

    # 设置任务结果
    current_step.worker_result = worker_result
    current_step.status = "DONE"

    logger.info(f"[worker] 🎭 Mock模式任务完成: {analysis_name}")

    # 构建返回状态
    state_update = {"messages": [worker_result.to_message(extra_kwargs={"input_tokens": 50, "output_tokens": 100})]}

    # 同步到数据库
    await db_update_step_status(current_step)
    return state_update


# 什么乱七八糟的 先编译过 然后再优化下
async def _ensure_data_available(current_step: DAGPlanStep):
    if current_step.query_result and current_step.query_result.is_successful:
        return current_step.query_result
    elif current_step.query_result and not current_step.query_result.is_successful:
        df, error_info = await DataCenter.get_dataframe(query_params=current_step.query_result)
        if df is not None and not df.empty:
            current_step.query_result = SqlExecutorQueryResult.success(df, additional_info=error_info)
        else:
            current_step.query_result = await DataCenter.query_and_store_data(current_step.query_params)


async def _request_llm_to_adjust_params(current_step: DAGPlanStep, state: State) -> dict:
    error_context = current_step.get_error_context()
    ctx = ContextBuilder(state=state)
    ctx.add_system_text("你是一个数据查询专家。现在有一个查询失败了，请分析错误原因并决定是否调整参数重试。")
    ctx.add_system_text(error_context)
    ctx.add_system_text(
        "请分析以上错误，并决定：\n"
        "1. 如果错误可以通过调整查询参数解决（如修改维度名称、WHERE条件等），请提供调整后的参数\n"
        "2. 如果错误无法解决（如数据库连接问题、权限问题等），请选择放弃\n\n"
        "常见的可调整错误：\n"
        "- 列名不存在：可能需要修改drilldown_dimension\n"
        "- WHERE条件语法错误：可能需要调整where字段\n"
        "- 时间范围问题：可能需要调整时间参数"
    )
    ctx.add_ocean_dimension_tips()
    ctx.add_rtc_special_issue()
    ctx.add_json_schema(RetryParams)
    ctx.add_plan_goal().add_plan_thinking().add_relate_dimension_summary(current_step=current_step)
    ctx.set_human_step("请分析以上错误并提供调整建议。")
    input_messages = ctx.build_messages()
    response_raw, response_parsed, response_error, input_tokens, output_tokens = await llm_structured_request(
        node_name=f"{current_step.query_params.to_key()}_retry",
        input_messages=input_messages,
        schema_type=RetryParams,
    )
    return {
        "raw": response_raw,
        "parsed": response_parsed,
        "error": response_error,
        "input_tokens": input_tokens,
        "output_tokens": output_tokens,
    }


async def _handle_query_failure(current_step: DAGPlanStep, state: State, state_update: dict, analysis_name: str):
    err = getattr(getattr(current_step, "query_result", None), "error_message", None)
    logger.warning(f"查询失败，尝试让大模型调整参数: {err}")
    retry_params = await _request_llm_to_adjust_params(current_step, state)
    state_update.update(create_token_update(retry_params.get("input_tokens", 0), retry_params.get("output_tokens", 0)))
    if not retry_params["parsed"].give_up and retry_params["parsed"].adjusted_query_params:
        logger.info(
            f"大模型决定重试，使用调整后的参数执行查询: {retry_params['parsed'].adjusted_query_params.model_dump()}"
        )
        current_step.query_result = await DataCenter.query_and_store_data(retry_params["parsed"].adjusted_query_params)
        if current_step.has_data():
            logger.info("重试查询成功")
            return current_step.query_result
        else:
            logger.error("重试查询仍然失败，放弃")
            return current_step.query_result
    else:
        logger.info(f"大模型决定放弃查询: {retry_params['parsed'].give_up_reason}")


async def _perform_analysis(current_step: DAGPlanStep, state_update: dict, state: State) -> dict:
    query_params = current_step.query_params
    data_prompt = current_step.get_data_prompt()
    ctx = ContextBuilder(state=state)
    ctx.add_base_analysis_role()
    ctx.add_ocean_dimension_tips()
    ctx.add_rtc_special_issue()
    ctx.add_system_text(data_prompt)
    ctx.add_json_schema(WorkerResult)
    ctx.add_plan_goal().add_plan_thinking().add_relate_dimension_summary(current_step)
    ctx.set_human_step("请根据以上数据进行分析。")
    input_messages = ctx.build_messages()
    _, response_parsed, _, input_tokens, output_tokens = await llm_structured_request(
        node_name=query_params.to_key(), input_messages=input_messages, schema_type=WorkerResult
    )
    state_update.update(create_token_update(input_tokens, output_tokens))
    current_step.worker_result = response_parsed
    logger.info(
        f"[{query_params.to_key()}] ✅ worker_result = \n{current_step.worker_result.model_dump_json(indent=4, exclude_none=False)}\n"
    )

    worker_message = current_step.create_combined_message(
        extra_kwargs={"input_tokens": input_tokens, "output_tokens": output_tokens}
    )
    state_update.setdefault("messages", []).append(worker_message)
    current_step.status = "RUNNING"


@task
async def worker_agent(state: State, current_step: DAGPlanStep) -> Dict:
    """
    Functional 风格的通用分析任务（纯函数式）。
    统一返回 dict 状态增量。
    """
    current_step.status = "RUNNING"
    query_params = current_step.query_params
    if query_params:
        analysis_name = getattr(query_params, "query_title", None) or getattr(query_params, "metric_name", "unknown")
    else:
        analysis_name = "unknown"
    logger.info(f"🤖{analysis_name} ...")

    # 🔧 检查是否为mock模式
    if is_mock_mode(get_config()):
        logger.info(f"[worker] 🎭 使用Mock模式执行任务: {analysis_name}")
        return await _mock_worker_step(state, current_step)

    state_update: dict = {"messages": []}

    # 确保数据可用
    await _ensure_data_available(current_step)

    # 查询失败时尝试一次参数调整重试
    if current_step.has_error():
        await _handle_query_failure(current_step, state, state_update, analysis_name)

    # 执行分析（成功或失败都会执行）
    await _perform_analysis(current_step, state_update, state)

    if current_step.has_error():
        current_step.status = "FAILED"
    else:
        current_step.status = "DONE"

    assert current_step.worker_result, "worker_result is missing"

    # 直接同步任务状态到数据库
    await db_update_step_status(current_step)
    return state_update
