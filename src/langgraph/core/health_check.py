"""健康检查任务

在任务执行前进行系统健康检查，确保数据库连接等基础设施正常。
"""

import logging
from typing import Any, Dict

from langgraph.func import task

from src.langgraph.common.utils.time_utils import get_ns_timestamp
from src.langgraph.state import State

logger = logging.getLogger(__name__)


@task
async def db_health_check(state: State) -> Dict[str, Any]:
    """
    系统健康检查任务

    在任务执行前检查：
    1. 数据库连接状态
    2. 其他关键基础设施状态

    如果健康检查失败，抛出异常终止任务执行
    """
    logger.info("🏥 开始系统健康检查")

    # 数据库连接检查 - 使用简化版本
    from src.langgraph.zego_tools.ocean_executor import ocean_connection

    try:
        is_connected = await ocean_connection.test_connection()
        if not is_connected:
            error_msg = "数据库连接失败，任务终止"
            logger.error(f"[db_health_check] ❌ {error_msg}")
            raise RuntimeError(error_msg)
        logger.info("[db_health_check] ✅ 数据库连接正常")
    except Exception as e:
        error_msg = f"数据库连接测试异常，任务终止: {str(e)}"
        logger.error(f"[db_health_check] ❌ {error_msg}")
        raise RuntimeError(error_msg)

    # 可以在这里添加其他健康检查项目
    # 例如：Redis连接、外部API可用性等

    logger.info("🏥 ✅ 系统健康检查通过")

    return {"db_health_check_passed": True, "db_health_check_timestamp": get_ns_timestamp()}
