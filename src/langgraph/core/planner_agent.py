import logging

from langchain_core.messages import SystemMessage
from langgraph.config import get_config
from langgraph.func import task

from src.langgraph.common.prompts.fragments import (
    PLANNER_REQUIREMENTS,
    PLANNER_ROLE,
    REPLANNER_PRINCIPLES,
    REPLANNER_PROMPTS,
    REPLANNER_ROLE,
    SPECIAL_REGION_ISSUE_NOTES,
)

from src.langgraph.common.utils.config_utils import get_mock_delay, is_mock_mode
from src.langgraph.common.utils.context_builder import ContextBuilder

from src.langgraph.common.utils.llm_request_utils import create_token_update, llm_structured_request
from src.langgraph.core.common.data_center import DataCenter
from src.langgraph.core.common.types import DAGPlan

from src.langgraph.core.planner_utils import validate_schema_ids

# Mock模式支持
from src.langgraph.mock import MockDataGenerator
from src.langgraph.state import State
from src.langgraph.zego_tools.ocean_executor.types import SqlExecutorQueryResult
from src.storage.services.storage_service import db_save_plan

logger = logging.getLogger(__name__)


async def _mock_planner_agent(state: State):
    """Mock模式的计划生成"""
    plans: list[DAGPlan] = state.get("plans", []) or []

    # 获取mock延迟时间
    delay = get_mock_delay(get_config())
    await MockDataGenerator.mock_delay(delay)

    # 获取当前轮次
    current_round = len(plans) + 1

    # 生成mock计划
    query = state.get("query", "1850816294")
    mock_plan_data = MockDataGenerator.generate_mock_plan(current_round, query)

    # 转换为DAGPlan对象
    plan = DAGPlan(**mock_plan_data)

    # 设置正确的round值
    plan.round = current_round
    for step in plan.steps:
        step.round = current_round
    logger.info(f"[planner] 🎭 Mock模式生成计划: round={current_round} (已有计划数: {len(plans)})")

    # Mock数据查询结果
    for plan_step in plan.steps:
        # Mock查询结果
        plan_step.query_result = SqlExecutorQueryResult(is_successful=True, sql="select xxx")

    # 构建返回状态
    state_update = {
        "plans": [plan],
        "messages": [plan.to_message(extra_kwargs={"input_tokens": 100, "output_tokens": 200})],
    }

    await db_save_plan(plan)

    return state_update


ID_FIX_INSTRUCTION = (
    "请修正上次返回的 steps：\n"
    "- 使用从1开始连续且唯一的整数 id；\n"
    "- depends_on 仅引用这些 id；\n"
    "- 不要输出字符串依赖或标题作为键。"
)


async def _structured_schema_with_id_retry(node_name: str, base_messages: list, schema_cls):
    """请求 LLM 返回 DAGPlan ，并在 ID 异常时进行一次纠正重试。

    返回: (schema, total_input_tokens, total_output_tokens)
    """
    # TODO 这里检查DAG是否有环
    _, schema, _, input_tokens, output_tokens = await llm_structured_request(
        node_name=node_name,
        input_messages=base_messages,
        schema_type=schema_cls,
    )
    is_valid_ids, id_warn = validate_schema_ids(schema)
    if not is_valid_ids:
        logger.warning(f"[planner] ⚠️ 规划返回ID异常，尝试一次纠正重试:\n{id_warn}")
        fix_msg = SystemMessage(content=ID_FIX_INSTRUCTION)
        try:
            _, schema_retry, _, input_tokens2, output_tokens2 = await llm_structured_request(
                node_name=f"{node_name}_retry",
                input_messages=[*base_messages, fix_msg],
                schema_type=schema_cls,
            )
            return schema_retry, input_tokens + input_tokens2, output_tokens + output_tokens2
        except Exception:
            logger.exception("[planner] 纠正重试失败，继续使用首次返回结果")
    return schema, input_tokens, output_tokens


@task
async def planner_agent(state: State):
    """
    规划与调度（含重规划与聚合）：
    - 初次进入：生成 DAG 计划并预取数据
    - 后续进入（worker→planner 回环）：当无可执行任务时，聚合判定 → 可能生成新任务或仅更新 plan.thinking；最终汇报由 reporter 兜底生成 final_report
    """
    logger.info("🤖planner node is working.")
    node_name = "planner"
    plans: list[DAGPlan] = state.get("plans", []) or []
    current_round = len(plans) + 1

    # 🔧 检查是否为mock模式
    if is_mock_mode(get_config()):
        logger.info("[planner] 🎭 使用Mock模式生成计划")
        return await _mock_planner_agent(state)

    # 正常模式继续执行

    # 使用新的 ContextBuilder 构建完整上下文
    ctx = ContextBuilder(state=state)

    # 添加系统角色和规则
    ctx.add_base_analysis_role()
    ctx.add_ocean_dimension_tips()
    ctx.add_rtc_special_issue()

    if plans:
        # 重新规划
        ctx.add_system_text(REPLANNER_ROLE)
        ctx.add_system_text(REPLANNER_PROMPTS)
        ctx.add_system_text(REPLANNER_PRINCIPLES)
        ctx.add_plan_goal().add_plan_thinking().add_relate_dimension_summary()
    else:
        # 首次规划
        ctx.add_system_text(PLANNER_ROLE)
        ctx.add_system_text(PLANNER_REQUIREMENTS)

    ctx.add_system_text(SPECIAL_REGION_ISSUE_NOTES)
    ctx.add_json_schema(
        DAGPlan,
        extra_notes=(
            "- 严格遵循 schema；\n"
            "- steps 中的 id 为从1开始连续且唯一的整数；\n"
            "- depends_on 仅包含这些整数 id；\n"
            "- 不要输出字符串依赖或以标题作为依赖键；"
        ),
    )

    # 设置人类任务
    ctx.set_human_step("请根据以上信息生成分析计划。")

    input_messages = ctx.build_messages()

    dag_plan_schema, input_tokens, output_tokens = await _structured_schema_with_id_retry(
        node_name, input_messages, DAGPlan
    )
    state_update: dict = create_token_update(input_tokens, output_tokens)
    logger.info(f"[planner] ✅ dag_plan = \n{dag_plan_schema.model_dump_json(indent=4, exclude_none=False)}\n")
    plan: DAGPlan = dag_plan_schema
    plan.round = current_round
    for step in plan.steps:
        step.round = current_round

    # 🔧 修复：根据已有计划数量设置正确的round值
    current_round = len(plans) + 1
    plan.round = current_round
    logger.info(f"[planner] 🔧 设置计划轮次: round={current_round} (已有计划数: {len(plans)})")

    if not plan.steps:
        logger.info("[planner] 无新任务，返回空")
        return {}

    logger.info("[planner] 开始批量查询数据")
    total_count = len(plan.steps)
    success_count = 0

    for plan_step in plan.steps:
        query_params = plan_step.query_params
        plan_step.query_result = await DataCenter.query_and_store_data(query_params)
        if plan_step.query_result.is_successful:
            success_count += 1
    if success_count == total_count:
        logger.info(f"[planner] ✅ 批量查询完成: 成功 {success_count}/{total_count}")
    elif success_count > 0:
        logger.warning(f"[planner] ⚠️ 批量查询部分成功: {success_count}/{total_count}")
    else:
        logger.error(f"[planner] ❌ 批量查询全部失败: {success_count}/{total_count}")

    state_update.update({"plans": [plan]})
    state_update.setdefault("messages", []).append(
        plan.to_message(extra_kwargs={"input_tokens": input_tokens, "output_tokens": output_tokens})
    )

    # 直接同步计划到数据库
    await db_save_plan(plan)

    return state_update
