from typing import List, Optional

from langchain_core.messages import AIMessage
from pydantic import BaseModel, ConfigDict, Field
from pydantic.json_schema import SkipJsonSchema

from src.langgraph.zego_tools.ocean_executor import SqlExecutorQueryResult
from src.langgraph.zego_tools.sql_generator import DataQueryParams


class RetryParams(BaseModel):
    """
    重试参数类：用于大模型调整查询参数的响应
    """

    thinking: str = Field(description="对错误的分析和调整思路")
    adjusted_query_params: Optional[DataQueryParams] = Field(
        default=None, description="调整后的查询参数，如果决定重试的话"
    )
    give_up: bool = Field(description="是否放弃这个查询步骤")
    give_up_reason: Optional[str] = Field(default=None, description="放弃的原因说明")


class WorkerResult(BaseModel):
    objective_evidence: str = Field(description="客观数据证据（纯事实描述）")
    subjective_thinking: str = Field(description="逻辑清晰的分析过程")
    objective_conclusion: str = Field(description="简洁明了的分析结论")

    def to_content(self, title: str = "分析结果") -> str:
        content_lines = [
            "🧾 客观数据证据（纯事实描述）",
            str(self.objective_evidence or ""),
            "",
            "🧠 分析过程",
            str(self.subjective_thinking or ""),
            "",
            "✅ 分析结论",
            str(self.objective_conclusion or ""),
        ]
        content = "\n".join(content_lines)
        return content

    def to_message(self, title: str = "分析结果", extra_kwargs: dict = {}) -> AIMessage:
        """生成分析结果消息（纯分析内容，不包含查询上下文）。

        Args:
            title: 消息显示名称
            extra_kwargs: 额外的消息属性
        """
        ai_message = AIMessage(name=str(title), content="")
        ai_message.additional_kwargs = {
            "object_type": self.__class__.__name__,
            "object": self,
            **extra_kwargs,
        }

        ai_message.content = self.to_content(title)

        return ai_message


class TaskResult(BaseModel):
    """
    Reporter 节点最终结构化汇报。

    要求：结论先行，逻辑清晰，有证据、有思考、言简意赅。
    """

    thinking: str = Field(description="综合分析的思考与取舍理由（简洁版）")
    executive_summary: str = Field(description="结论先行")
    key_findings: list[str] = Field(description="关键发现（要点列举，含维度/时间/地区/客户等信息）")
    supporting_evidence: list[str] = Field(description="关键证据与数据要点摘录（引用上游维度分析）")
    analysis_reasoning: str = Field(description="推理链路与因果说明（简洁版）")
    critical_issues: str = Field(description="关键问题详情")

    def to_message(self, extra_kwargs: dict = {}) -> AIMessage:
        ai_message = AIMessage(name="最终汇报", content="")
        ai_message.additional_kwargs = {
            "object_type": self.__class__.__name__,
            "object": self,
            **extra_kwargs,
        }

        def format_list(lst):
            if not lst:
                return ""
            return "\n".join(f"- {item}" for item in lst)

        content_lines = [
            "📌 结论先行",
            self.executive_summary,
            "",
            "🔎 关键发现",
            format_list(self.key_findings),
            "",
            "🧪 证据要点" if self.supporting_evidence else None,
            format_list(self.supporting_evidence) if self.supporting_evidence else None,
            "",
            "🧠 推理链路",
            self.analysis_reasoning,
            "",
            "⚠️ 关键问题详情",
            self.critical_issues,
            "",
        ]
        ai_message.content = "\n".join([c for c in content_lines if c is not None])
        return ai_message


class DAGPlanStep(BaseModel):
    """计划中的单个步骤。

    不区分 kind；计划属于整个步骤。
    状态：PENDING/READY/RUNNING/DONE/FAILED
    """

    round: SkipJsonSchema[int | None] = Field(default=None, description="属于第几轮plan")
    id: int = Field(description="步骤整数ID，从1开始连续编号且在一次规划内唯一")
    depends_on: list[int] = Field(default_factory=list, description="前置依赖步骤的ID列表")
    query_params: DataQueryParams = Field(description="数据查询参数")
    query_result: SkipJsonSchema[SqlExecutorQueryResult | None] = Field(
        default=None, exclude=True, description="数据查询结果"
    )
    status: SkipJsonSchema[str] = Field(
        default="PENDING",
        description="步骤状态： PENDING/READY/RUNNING/DONE/FAILED",
    )
    worker_result: SkipJsonSchema[WorkerResult | None] = Field(default=None, description="数据分析结果")

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def has_data(self) -> bool:
        """检查是否有有效数据"""
        if self.query_result is None:
            return False
        try:
            df = self.query_result.data
            return bool(self.query_result.is_successful and df is not None and not df.empty)
        except Exception:
            return False

    def has_error(self) -> bool:
        """检查是否有错误"""
        # 占位查询结果（无结果）不视为错误
        if self.query_result is None:
            return False
        return (not self.query_result.is_successful) or (self.query_result.error_message is not None)

    def get_data_prompt(self) -> str:
        """获取数据提示词格式"""
        if self.has_data():
            df = self.query_result.data  # type: ignore[union-attr]
            data_prompt = f"<数据说明>\n{self.query_params.to_prompt()}\n</数据说明>\n"
            # 显式传入列名，避免表头缺失
            data_prompt += f"<数据表>\n{df.to_markdown(index=False, headers=list(df.columns))}\n</数据表>\n"

            data_len = len(df)
            if data_len > 3000:
                data_prompt += "\n<warning>\n数据量被限制limit3000\n</warning>\n"

            if (
                self.query_result
                and self.query_result.additional_info
                and "错误码说明" in self.query_result.additional_info
            ):
                data_prompt += f"\n<错误码说明>\n{self.query_result.additional_info}\n</错误码说明>\n"

            return data_prompt
        else:
            err = None
            if self.query_result is not None:
                err = self.query_result.error_message
            return f"**{self.query_params.query_title}**: 查询失败 - {err}"

    def get_step_query_info(self) -> str:
        """获取查询上下文信息，用于在 AIMessage 中展示"""
        context_parts = []

        # 基本信息
        context_parts.append(f"# 分析主题: {self.query_params.query_title}")
        context_parts.append(f"- 指标: {self.query_params.metric_name}")
        context_parts.append(f"- 时间范围: {self.query_params.time_start} ~ {self.query_params.time_end}")
        context_parts.append(f"- 查询参数: {self.query_params.model_dump_json()}")

        # 数据状态
        if self.has_data():
            df = self.query_result.data if self.query_result else None
            if df is not None:
                context_parts.append(f"数据状态: 查询成功，{len(df)} 条记录")
        elif self.has_error():
            error_msg = self.query_result.error_message if self.query_result else "未知错误"
            context_parts.append(f"数据状态: 查询失败 - {error_msg}")
        else:
            context_parts.append("数据状态: 无数据")

        return "\n".join(context_parts)

    def get_error_context(self) -> str:
        """获取错误上下文信息，用于大模型调整参数"""
        if not self.has_error():
            return ""

        error_context = "查询失败详情：\n"
        error_context += f"- 查询标题：{self.query_params.query_title}\n"
        error_context += f"- 指标名称：{self.query_params.metric_name}\n"

        if self.query_result.sql:
            error_context += f"- 生成的SQL：\n```sql\n{self.query_result.sql}\n```\n"

        if self.query_result and self.query_result.error_source:
            error_context += f"- 错误来源：{self.query_result.error_source}\n"

        if self.query_result and self.query_result.error_code:
            error_context += f"- 错误代码：{self.query_result.error_code}\n"

        if self.query_result and self.query_result.error_message:
            error_context += f"- 错误信息：{self.query_result.error_message}\n"

        if self.query_params.drilldown_dimension:
            error_context += f"- 下钻维度：{self.query_params.drilldown_dimension}\n"

        if self.query_params.where:
            error_context += f"- WHERE条件：{self.query_params.where}\n"

        return error_context

    def create_combined_message(self, extra_kwargs: dict = {}) -> AIMessage:
        """创建包含查询上下文和分析结果的完整消息。

        Args:
            worker_result: 分析结果
            worker_params: 查询参数和上下文（已弃用，使用query_params）
            extra_kwargs: 额外的消息属性
        """
        ai_message = AIMessage(name=self.query_params.query_title, content="")
        ai_message.additional_kwargs = {
            "object_type": self.__class__.__name__,
            "object": self,
            **extra_kwargs,
        }

        # 获取查询上下文信息
        contents = [self.get_step_query_info()]

        # 创建基础消息
        if self.worker_result:
            worker_result_content = self.worker_result.to_content(title=self.query_params.query_title)
            contents.append(worker_result_content)

        # 在内容前添加查询上下文
        ai_message.content = "\n".join(contents)

        return ai_message


class DAGPlan(BaseModel):
    """Planner 输出的DAG任务规划，包含依赖与查询参数。"""

    goal: str = Field(description="本次任务的总体目标/用户问题")
    thinking: str = Field(default=None, description="计划思路")
    steps: list[DAGPlanStep] = Field(default_factory=list, description="任务列表")
    round: SkipJsonSchema[int] = Field(default=1, description="规划轮次")

    def refresh_ready_steps(self) -> list[DAGPlanStep]:
        todo_steps = [st for st in self.steps if st.status in ("PENDING", "READY")]
        ready_steps = []
        for step in todo_steps:
            parents = [rt for rt in self.steps if rt.id in step.depends_on]
            if all(parent.status in ("DONE", "FAILED") for parent in parents):
                step.status = "READY"
                ready_steps.append(step)
        return ready_steps

    def all_done(self) -> bool:
        return all(step.status in ("DONE", "FAILED") for step in self.steps)

    def to_message(self, extra_kwargs: dict = {}) -> AIMessage:
        from src.langgraph.core.planner_utils import build_plan_message

        return build_plan_message(self, extra_kwargs)

    def to_params_list(self) -> List[DataQueryParams]:
        return [t.query_params for t in (getattr(self, "steps", []) or []) if t.query_params]

    # ---- Runtime helpers ----
    def get_completed_ids(self) -> set[int]:
        from src.langgraph.core.planner_utils import get_completed_ids

        return get_completed_ids(getattr(self, "steps", []) or [])

    def get_ready_steps(self) -> list[DAGPlanStep]:
        from src.langgraph.core.planner_utils import get_ready_steps_by_id

        return get_ready_steps_by_id(getattr(self, "steps", []) or [])

    def find_step_by_id(self, step_id: int) -> DAGPlanStep | None:
        for t in self.steps:
            if t.id == step_id:
                return t
        return None

    def with_steps(self, steps: list[DAGPlanStep]) -> "DAGPlan":
        return DAGPlan(goal=self.goal, steps=list(steps or []), thinking=self.thinking, round=self.round)


if __name__ == "__main__":
    print("-" * 20)
    print(DAGPlan.model_json_schema())
    print("-" * 20)
