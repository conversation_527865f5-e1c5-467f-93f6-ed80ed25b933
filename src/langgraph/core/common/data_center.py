"""
数据中心模块：统一管理所有节点的数据存取逻辑
"""

import hashlib
import logging

import pandas as pd

from src.langgraph.common.utils.df_store_utils import trans_df_to_store_data

from src.langgraph.zego_tools.ocean_executor import execute_sql_async, SqlExecutorQueryResult
from src.langgraph.zego_tools.sql_generator import DataQueryParams
from src.langgraph.zego_tools.sql_generator.generate_sql import generate_sql


logger = logging.getLogger(__name__)

DATA_NAMESPACE = ("data_center", "queried_data")
SQL_KEY_PREFIX = "sql:"
GLOBAL_SQL_NAMESPACE = ("data_center", "global_sql_data")


class DataCenter:
    """数据中心：统一管理queried_data的存取操作，使用LangGraph Store"""

    # --------------- Key helpers ---------------
    @staticmethod
    def _sql_hash(sql: str) -> str:
        """使用 SQL 文本的 SHA256 前16位作为内容寻址 key。"""
        return hashlib.sha256(sql.encode("utf-8")).hexdigest()[:16]

    @staticmethod
    def _sql_key(sql_hash: str) -> str:
        return f"{SQL_KEY_PREFIX}{sql_hash}"

    @staticmethod
    async def store_data(
        query_params: DataQueryParams,
        data: pd.DataFrame,
        error_info: str = "",
    ) -> None:
        """
        存储数据到 LangGraph Store 中，主路径统一为基于 SQL 哈希的内容寻址。

        注意：不再写入 legacy 的参数 key 路径。

        Args:
            query_params: 查询参数对象
            data: 查询到的 DataFrame 数据
            error_info: 错误信息文本
        """
        # 为了统一按 SQL 内容寻址进行存储，这里生成 SQL 并计算哈希
        try:

            sql = generate_sql(query_params)
            sql_hash = DataCenter._sql_hash(sql)

            # 按 SQL 哈希写入数据（全局命名空间）
            await DataCenter._store_sql_data(
                sql_hash=sql_hash,
                query_params=query_params,
                data=data,
                error_info=error_info,
            )

            logger.info(
                f"[DataCenter] ✅ 存储数据到Store(sql-hash): {DataCenter._sql_key(sql_hash)}, 行数: {len(data)} 数据标题: {query_params.query_title}"
            )
        except Exception as e:
            # 严禁继续写入 legacy param-key，这里仅记录错误
            logger.error(
                f"[DataCenter] ❌ store_data 失败，已禁止 legacy 写入；原因: {e} | 标题: {query_params.query_title}"
            )

    @staticmethod
    async def _store_sql_data(
        sql_hash: str,
        query_params: DataQueryParams,
        data: pd.DataFrame,
        error_info: str = "",
    ) -> None:
        """按 SQL 哈希内容寻址存储查询结果（全局命名空间）。"""
        # namespace = GLOBAL_SQL_NAMESPACE  # 暂时未使用
        records, columns = trans_df_to_store_data(data)
        # data_entry = {  # 暂时未使用
        #     "data": records,
        #     "data_columns": columns,
        #     "error_info": error_info,
        #     "query_params": query_params.model_dump_json(),
        # }
        sql_key = DataCenter._sql_key(sql_hash)

        # Redis已移除，暂时禁用缓存功能
        # redis_manager = await get_redis_manager()
        # await redis_manager.store_put(namespace=namespace, key=sql_key, value=data_entry)
        logger.info(f"[DataCenter] ✅ 跳过缓存存储: {sql_key}, 行数: {len(data)} 数据标题: {query_params.query_title}")

    @staticmethod
    async def get_dataframe(query_params: DataQueryParams) -> tuple[pd.DataFrame, str]:
        """从全局 SQL 缓存获取 DataFrame 及其 error_info。

        Returns:
            (df, error_info)
            - 若未命中或数据异常，df 为空 DataFrame，error_info 为空串
        """
        try:
            # 每次基于参数重新生成 SQL，再按哈希定位全局缓存
            sql = generate_sql(query_params)
            sql_hash = DataCenter._sql_hash(sql)
            sql_key = DataCenter._sql_key(sql_hash)
            # Redis已移除，暂时禁用缓存功能
            # redis_manager = await get_redis_manager()
            # stored_item = await redis_manager.store_get(namespace=GLOBAL_SQL_NAMESPACE, key=sql_key)

            # 缓存已禁用，直接返回空结果
            logger.info(f"[DataCenter] 缓存已禁用，跳过缓存查找: {sql_key}")
            return pd.DataFrame(), ""
        except Exception as e:
            logger.error(f"[DataCenter] 获取全局SQL缓存数据时出错: {e}")
            return pd.DataFrame(), ""

    @staticmethod
    async def query_and_store_data(query_params: DataQueryParams) -> SqlExecutorQueryResult:

        try:
            sql = generate_sql(query_params)
            sql_hash = DataCenter._sql_hash(sql)
            sql_key = DataCenter._sql_key(sql_hash)

            # Redis已移除，暂时禁用缓存功能
            # redis_manager = await get_redis_manager()
            # cached_item = await redis_manager.store_get(namespace=GLOBAL_SQL_NAMESPACE, key=sql_key)
            logger.info(f"[DataCenter] 缓存已禁用，直接执行SQL查询: {sql_key}")

            # 使用带重试机制的查询执行，获取SqlExecutorResult
            query_result = await execute_sql_async("ocean", sql, max_retries=5, base_delay=0.5)

            # 处理查询结果
            if query_result.is_successful:
                # 存储成功的数据（内容寻址，写入全局SQL命名空间）
                await DataCenter._store_sql_data(
                    sql_hash=sql_hash,
                    query_params=query_params,
                    data=query_result.data,
                    error_info=query_result.additional_info or "",
                )

                if query_result.additional_info and "错误码说明" in query_result.additional_info:
                    logger.info(
                        f"✅ {query_params.query_title} 查询完成: {len(query_result.data)} 条记录, 包含错误码信息"
                    )
                else:
                    logger.info(f"✅ {query_params.query_title} 查询完成: {len(query_result.data)} 条记录")

            else:

                err_msg = query_result.error_message
                logger.warning(
                    f"⚠️ {query_params.query_title} 查询失败: [{query_result.error_code}] " f"{err_msg}\nSQL如下:\n{sql}"
                )

            return query_result

        except Exception as e:
            # SQL生成或其他异常
            sql = locals().get("sql", None)
            logger.error(
                f"❌ {query_params.query_title} 查询失败: {e}"
                + (f"\nSQL如下:\n{sql}" if "sql" in locals() and sql else "")
            )
            return SqlExecutorQueryResult(
                sql=sql,
                error_message=f"查询处理异常: {str(e)}",
                error_code="EXECUTION_ERROR",
                error_source="DataCenter",
                is_successful=False,
            )
