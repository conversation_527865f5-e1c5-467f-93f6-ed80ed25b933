import logging

from langgraph.config import get_config

from langgraph.func import task

from src.langgraph.common.utils.config_utils import get_mock_delay, is_mock_mode
from src.langgraph.common.utils.context_builder import ContextBuilder
from src.langgraph.common.utils.llm_request_utils import create_token_update, llm_structured_request
from src.langgraph.core.common.types import TaskResult

# Mock模式支持
from src.langgraph.mock import MockDataGenerator
from src.langgraph.state import State
from src.storage.services.storage_service import db_insert_task_result

logger = logging.getLogger(__name__)


async def _mock_reporter_step(state: State) -> dict:
    """Mock模式的报告生成"""
    # 获取mock延迟时间
    delay = get_mock_delay(get_config())
    await MockDataGenerator.mock_delay(delay)

    # 生成mock报告
    query = state.get("query", "1850816294")
    mock_report_data = MockDataGenerator.generate_mock_reporter_result(query)

    # 创建TaskResult对象
    reporter_result = TaskResult(
        thinking=mock_report_data["thinking"],
        executive_summary=mock_report_data["executive_summary"],
        key_findings=mock_report_data["key_findings"],
        supporting_evidence=mock_report_data["supporting_evidence"],
        analysis_reasoning=mock_report_data["analysis_reasoning"],
        critical_issues=mock_report_data["critical_issues"],
    )

    logger.info("[reporter] 🎭 Mock模式报告生成完成")

    # 同步到数据库
    await db_insert_task_result(reporter_result)

    # 构建返回状态
    state_update = {
        "messages": [reporter_result.to_message(extra_kwargs={"input_tokens": 200, "output_tokens": 500})],
        "reporter_result": reporter_result,
    }

    return state_update


@task
async def reporter_agent(state: State) -> dict:
    """
    Reporter 任务：请求大模型生成最终结构化汇报，并写入 reporter_result。
    返回：dict 状态增量
    """
    logger.info("🤖reporter step is working.")

    # 🔧 检查是否为mock模式
    from langgraph.config import get_config

    if is_mock_mode(get_config()):
        logger.info("[reporter] 🎭 使用Mock模式生成报告")
        return await _mock_reporter_step(state)

    state_update: dict = {"messages": []}

    # 如已有 reporter_result 则直接返回空更新（幂等）
    existing = state.get("reporter_result")
    if existing is not None:
        return {}

    # 使用新的 ContextBuilder 构建完整上下文
    plans = state.get("plans", []) or []
    latest_plan = plans[-1] if plans else None

    ctx = ContextBuilder(state=state)

    # 添加系统角色和规则
    ctx.add_reporter_base()
    ctx.add_ocean_dimension_tips()
    ctx.add_rtc_special_issue()
    ctx.add_json_schema(TaskResult)

    # 添加上下文信息 TODO 这里很多东西都在 systemMessage 中
    ctx.add_plan_goal(latest_plan).add_plan_thinking(latest_plan).add_relate_dimension_summary()

    # 设置人类任务
    ctx.set_human_step("请根据以上信息生成分析报告。")

    input_messages = ctx.build_messages()
    _, result_parsed, _, input_tokens, output_tokens = await llm_structured_request(
        node_name="reporter",
        input_messages=input_messages,
        schema_type=TaskResult,
    )

    state_update.update(create_token_update(input_tokens, output_tokens))
    reporter_result: TaskResult = result_parsed

    # 生成消息并落盘到 state
    msg = reporter_result.to_message(extra_kwargs={"input_tokens": input_tokens, "output_tokens": output_tokens})
    state_update["messages"].append(msg)
    state_update["reporter_result"] = reporter_result

    logger.info(f"[reporter] ✅ reporter_result = \n{reporter_result.model_dump_json(indent=4, exclude_none=False)}\n")

    # 直接同步报告结果到数据库
    await db_insert_task_result(reporter_result)
    return state_update
