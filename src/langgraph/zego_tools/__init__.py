"""ZEGO 工具包（顶层）

轻量惰性转发，避免循环依赖，同时保留常用入口：
- 参数与SQL生成：DataQueryParams, generate_sql
- 执行与错误处理：execute_sql_async, SqlExecutorQueryResult, format_error_code_info_for_llm
"""

from typing import TYPE_CHECKING

__all__ = [
    "DataQueryParams",
    "generate_sql",
    "execute_sql_async",
    "SqlExecutorQueryResult",
    "format_error_code_info_for_llm",
]


def __getattr__(name: str):  # pragma: no cover - 简单转发
    if name == "DataQueryParams":
        from .sql_generator.params import DataQueryParams as _DataQueryParams

        return _DataQueryParams
    if name == "generate_sql":
        from .sql_generator.generate_sql import generate_sql as _generate_sql

        return _generate_sql
    if name == "execute_sql_async":
        from .ocean_executor.executor import execute_sql_async as _execute_sql_async

        return _execute_sql_async
    if name == "SqlExecutorQueryResult":
        from .ocean_executor.types import SqlExecutorQueryResult as _SqlExecutorResult

        return _SqlExecutorResult
    if name == "format_error_code_info_for_llm":
        from .ocean_executor.error_handler import format_error_code_info_for_llm as _format_error_code_info_for_llm

        return _format_error_code_info_for_llm
    raise AttributeError(f"module {__name__!r} has no attribute {name!r}")


if TYPE_CHECKING:  # 仅用于类型检查与IDE提示
    from .ocean_executor.error_handler import format_error_code_info_for_llm as format_error_code_info_for_llm
    from .ocean_executor.executor import execute_sql_async as execute_sql_async
    from .ocean_executor.types import SqlExecutorQueryResult as SqlExecutorQueryResult
    from .sql_generator.generate_sql import generate_sql as generate_sql
    from .sql_generator.params import DataQueryParams as DataQueryParams
