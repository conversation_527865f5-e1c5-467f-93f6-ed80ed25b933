"""测试简化后的数据库连接器"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

from src.langgraph.zego_tools.ocean_executor.simple_executor import execute_sql_async


async def test_simple_connection():
    """测试简化后的连接"""
    print("=== 测试简化后的数据库连接器 ===")

    # 测试基本连接
    print("\n1. 测试基本查询...")
    try:
        result = await execute_sql_async("ocean", "SELECT 1 as test")
        if result.is_successful:
            print(f"✅ 基本查询成功: {result.data}")
        else:
            print(f"❌ 基本查询失败: {result.error_message}")
    except Exception as e:
        print(f"❌ 基本查询异常: {e}")

    # 测试错误处理
    print("\n2. 测试错误处理...")
    try:
        result = await execute_sql_async("ocean", "SELECT * FROM non_existent_table")
        if not result.is_successful:
            print(f"✅ 错误处理正常: {result.error_code} - {result.error_message}")
        else:
            print("❌ 应该返回错误但却成功了")
    except Exception as e:
        print(f"❌ 错误处理异常: {e}")

    # 测试并发执行（应该排队）
    print("\n3. 测试并发执行（排队机制）...")
    try:
        tasks = []
        for i in range(3):
            task = execute_sql_async("ocean", f"SELECT {i} as test_id, SLEEP(1) as delay")
            tasks.append(task)

        import time

        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()

        print(f"✅ 并发查询完成，总耗时: {end_time - start_time:.2f}秒")
        print("   (应该约为3秒，说明查询是排队执行的)")

        for i, result in enumerate(results):
            if result.is_successful:
                print(f"   查询{i}: 成功")
            else:
                print(f"   查询{i}: 失败 - {result.error_message}")

    except Exception as e:
        print(f"❌ 并发测试异常: {e}")

    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(test_simple_connection())
