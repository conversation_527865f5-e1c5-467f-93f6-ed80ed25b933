"""SQL执行器模块 - 简化版本

完全使用简化实现，移除复杂的连接池、熔断器等逻辑：
- 排队顺序执行，更安全
- 只使用aiomysql，移除fallback
- 保留SOCKS支持
"""

from .error_handler import format_error_code_info_for_llm
from .simple_connector import ocean_connection, themis_connection

# 简化版本 (唯一实现)
from .simple_executor import execute_sql_async
from .types import SqlExecutorQueryResult

__all__ = [
    "execute_sql_async",
    "format_error_code_info_for_llm",
    "SqlExecutorQueryResult",
    "ocean_connection",
    "themis_connection",
]
