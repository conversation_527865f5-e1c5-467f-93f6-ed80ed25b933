"""错误处理模块

负责处理SQL执行过程中的错误码解析和格式化。
"""

import json
import logging
from pathlib import Path
from typing import Optional

import pandas as pd

from src.langgraph.zego_tools.sql_generator.const import error_code_map

logger = logging.getLogger(__name__)


def save_unknown_error_codes(error_codes: set[int]) -> None:
    """
    将未知错误码保存到本地JSON文件中，支持merge更新

    Args:
        error_codes: 未知错误码集合
    """
    if not error_codes:
        return

    # 使用现代化的Path库
    error_map_file = Path(__file__).parent.parent / "error_map_td.json"

    # 读取现有的错误码数据
    existing_error_codes = {}
    if error_map_file.exists():
        try:
            with error_map_file.open("r", encoding="utf-8") as f:
                existing_error_codes = json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            logger.warning(f"读取错误码文件失败: {e}，将创建新文件")

    # 合并新的错误码
    updated_error_codes = existing_error_codes.copy()
    for error_code in error_codes:
        if str(error_code) not in updated_error_codes:
            updated_error_codes[str(error_code)] = {
                "description": "未知错误码",
                "first_discovered": None,  # 可以后续扩展时间戳
                "count": 1,
            }
        else:
            # 如果已存在，增加计数
            updated_error_codes[str(error_code)]["count"] += 1

    # 写入文件
    try:
        error_map_file.parent.mkdir(parents=True, exist_ok=True)
        with error_map_file.open("w", encoding="utf-8") as f:
            json.dump(updated_error_codes, f, ensure_ascii=False, indent=2)
        logger.info(f"已更新错误码文件，共保存 {len(updated_error_codes)} 个错误码，其中新发现 {len(error_codes)} 个")
    except IOError as e:
        logger.error(f"写入错误码文件失败: {e}")


def extract_error_code_info(df: pd.DataFrame) -> Optional[pd.DataFrame]:
    """
    从DataFrame中提取错误码信息并返回错误码说明

    Args:
        df: 查询结果DataFrame

    Returns:
        包含错误码说明的DataFrame，如果没有错误码列则返回None
    """
    if df is None or df.empty:
        return None

    k_error_code_columns = ["error_code", "error"]
    error_code_columns = [col for col in df.columns if col.lower() in k_error_code_columns]
    if not error_code_columns:
        return None

    # 提取所有唯一的错误码
    unique_error_codes = set()
    for col in error_code_columns:
        if df[col].dtype in ["int64", "int32", "float64", "float32"]:
            # 数值类型的错误码
            codes = df[col].dropna().unique()
            unique_error_codes.update([int(code) for code in codes if pd.notna(code)])
        else:
            # 字符串类型的错误码，尝试转换为数字
            codes = df[col].dropna().unique()
            for code in codes:
                try:
                    unique_error_codes.add(int(code))
                except (ValueError, TypeError):
                    continue

    if not unique_error_codes:
        return None

    # 收集未知错误码，用于保存到本地文件
    unknown_error_codes = set()

    # 构建错误码说明DataFrame
    error_info_data = []

    for error_code in sorted(unique_error_codes):
        if error_code in error_code_map:
            description = error_code_map[error_code]
        else:
            description = "未知错误码"
            unknown_error_codes.add(error_code)
            logger.warning(f"未找到错误码 {error_code} 的说明，请补充到 error_code_map 中。")
        error_info_data.append({"error_code": error_code, "description": description})

    # 保存未知错误码到本地文件
    if unknown_error_codes:
        save_unknown_error_codes(unknown_error_codes)

    assert len(unique_error_codes) < 100, "错误码数量超过100，请检查错误码是否正确"

    return pd.DataFrame(error_info_data)


def format_error_code_info_for_llm(error_info_df: Optional[pd.DataFrame]) -> str:
    """
    将错误码信息格式化为适合传递给大模型的字符串

    Args:
        error_info_df: 错误码信息DataFrame

    Returns:
        格式化后的错误码信息字符串
    """
    if error_info_df is None or error_info_df.empty:
        return "本次查询结果中未包含错误码信息。"

    info_lines = ["## 错误码说明"]
    info_lines.append("以下是查询结果中出现的错误码及其详细说明：")
    info_lines.append("")

    for _, row in error_info_df.iterrows():
        error_code = row["error_code"]
        description = row["description"]
        info_lines.append(f"**错误码 {error_code}**: {description}")
        info_lines.append("")

    return "\n".join(info_lines)
