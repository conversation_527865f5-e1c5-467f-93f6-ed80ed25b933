"""大模型工具接口层

专门负责：
- 作为大模型工具的统一入口
- 连接大模型返回的 data_query_params 和内部 SQL 生成系统
- 提供简洁的接口，隐藏内部实现复杂性
"""

# 兼容包内导入与脚本直接运行
try:  # 优先包内相对导入
    from . import sql_template  # type: ignore
    from .params import DataQueryParams  # type: ignore
except Exception:  # 回退：脚本模式（直接运行本文件）
    # 在脚本模式下需要把当前目录加入 sys.path，确保相邻模块可被找到
    import os
    import sys

    _CUR_DIR = os.path.dirname(os.path.abspath(__file__))
    if _CUR_DIR not in sys.path:
        sys.path.insert(0, _CUR_DIR)
    import sql_template  # type: ignore
    from params import DataQueryParams  # type: ignore


def generate_sql(data_query_params: DataQueryParams) -> str:
    """
    使用查询参数生成对应的SQL语句

    Args:
        data_query_params: 数据查询参数对象

    Returns:
        生成的SQL查询语句

    Raises:
        ValueError: 当参数无效或指标不支持某些操作时
    """

    # 参数检查
    metric = data_query_params.get_metric()
    if not metric:
        raise ValueError(f"未找到指标配置: {data_query_params.metric_name}")

    if data_query_params.drilldown_dimension:
        if not metric.supports_dimension(data_query_params.drilldown_dimension):
            available_dims = metric.get_available_dimensions()
            raise ValueError(
                f"指标 {data_query_params.metric_name} 不支持维度 {data_query_params.drilldown_dimension}。"
                f"可用维度: {', '.join(available_dims)}"
            )

    ret_sql = sql_template.sqlgen_route(data_query_params)
    assert len(ret_sql) > 10
    return ret_sql
