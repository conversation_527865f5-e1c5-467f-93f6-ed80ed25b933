from datetime import datetime, timedelta


# 简化的表粒度管理
class TableGranularityManager:
    """表粒度管理器 - 简化版本"""

    # 表后缀映射
    GRANULARITY_SUFFIXES = {
        "1min": "",  # 1分钟粒度，无后缀
        "10min": "_10m",  # 10分钟粒度
        "1hour": "_1h",  # 1小时粒度
        "1day": "_1d",  # 1天粒度
    }

    @classmethod
    def get_table_name(cls, base_table: str, granularity: str = "1day") -> str:
        """
        根据粒度获取表名

        Args:
            base_table: 基础表名（默认应该是天粒度的表名，以_1d结尾）
            granularity: 粒度类型 ("1min", "10min", "1hour", "1day")

        Returns:
            对应粒度的表名
        """
        if granularity not in cls.GRANULARITY_SUFFIXES:
            raise ValueError(f"不支持的粒度: {granularity}")

        # 如果表名以_1d结尾，则替换为对应的后缀
        if base_table.endswith("_1d"):
            base_name = base_table[:-3]  # 移除_1d
            return f"{base_name}{cls.GRANULARITY_SUFFIXES[granularity]}"

        # 否则直接添加后缀
        return f"{base_table}{cls.GRANULARITY_SUFFIXES[granularity]}"

    @classmethod
    def auto_select_granularity(cls, start_time: datetime, end_time: datetime) -> str:
        """
        根据时间范围自动选择表粒度

        Args:
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            粒度类型字符串
        """
        time_diff = end_time - start_time

        if time_diff > timedelta(days=3):
            return "1day"
        elif time_diff > timedelta(days=1):
            return "1hour"
        elif time_diff > timedelta(hours=2):
            return "10min"
        else:
            return "1min"
