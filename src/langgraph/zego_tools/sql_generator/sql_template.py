from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:  # 仅用于类型检查与IDE提示，避免运行期导入导致循环
    try:
        from .params import DataQueryParams  # type: ignore
    except Exception:  # 兼容脚本直接运行
        from params import DataQueryParams  # type: ignore


def sqlgen_route(data_query_params: DataQueryParams) -> str:
    metric = data_query_params.get_metric()
    if data_query_params.drilldown_dimension:
        if metric.name.endswith("错误码分布"):
            return sqlgen_trend_error_dist_drilldown(data_query_params)
        else:
            return sqlgen_trend_drilldown(data_query_params)
    else:
        # 普通趋势分析（无下钻维度）
        if metric.name.endswith("错误码分布"):
            return sqlgen_trend_error_dist(data_query_params)
        else:
            return sqlgen_trend(data_query_params)


def sqlgen_trend(data_query_params: DataQueryParams) -> str:
    metric = data_query_params.get_metric()

    table_str = data_query_params.build_table_str(metric)
    where_str = data_query_params.build_where_condition_str(metric)
    sql = f"""
SELECT
    {metric.time_field},
    {metric.value_sql} as metric_value,
    SUM({metric.sample_ct_field}) as total_count
FROM {table_str}
WHERE {where_str}
GROUP BY {metric.time_field}
ORDER BY {metric.time_field}
LIMIT 3000
    """
    return sql


def sqlgen_trend_drilldown(data_query_params: DataQueryParams) -> str:
    metric = data_query_params.get_metric()

    # 使用CTE优化查询并限制维度数量
    table_str = data_query_params.build_table_str(metric)
    where_str = data_query_params.build_where_condition_str(metric)
    dimension = data_query_params.drilldown_dimension
    sql = f"""
WITH daily_stats AS (
    SELECT
        {metric.time_field},
        {dimension},
        {metric.value_sql} as metric_value,
        SUM({metric.sample_ct_field}) as total_count,
        ROUND(100.0 * SUM({metric.sample_ct_field}) / SUM(SUM({metric.sample_ct_field})) OVER (PARTITION BY {metric.time_field}),2) as dimension_percentage
    FROM {table_str}
    WHERE {where_str}
    GROUP BY {metric.time_field}, {dimension}
)
SELECT
    {metric.time_field},
    {dimension},
    metric_value,
    total_count,
    dimension_percentage
FROM daily_stats
where dimension_percentage > 1
ORDER BY {metric.time_field}, {dimension}, dimension_percentage DESC
LIMIT 3000
"""
    return sql


def sqlgen_trend_error_dist(data_query_params: DataQueryParams) -> str:
    metric = data_query_params.get_metric()
    """
    生成错误码分布分析的SQL

    Args:
        metric: 指标对象
        table_name: 表名
        where_conditions: WHERE条件列表
        granularity: 时间粒度

    Returns:
        错误码分布分析SQL
    """
    assert metric.name.endswith("错误码分布")

    table_str = data_query_params.build_table_str(metric)
    where_str = data_query_params.build_where_condition_str(metric)
    sql = f"""
WITH error_stats AS (
    SELECT
        {metric.time_field},
        {metric.error_field} as error_code,
        SUM({metric.sample_ct_field}) as error_code_count,
        round(100.0 * SUM({metric.sample_ct_field}) / SUM(SUM({metric.sample_ct_field})) OVER (PARTITION BY {metric.time_field}), 2) as error_percentage,
        SUM(SUM({metric.sample_ct_field})) OVER (PARTITION BY {metric.time_field}) as total_error_count
    FROM {table_str}
    WHERE {where_str}
    GROUP BY {metric.time_field}, {metric.error_field}
)
SELECT
    {metric.time_field},
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc
LIMIT 3000
"""
    return sql


def sqlgen_trend_error_dist_drilldown(data_query_params: DataQueryParams) -> str:
    metric = data_query_params.get_metric()
    assert metric.name.endswith("错误码分布")

    table_str = data_query_params.build_table_str(metric)
    where_str = data_query_params.build_where_condition_str(metric)
    dimension = data_query_params.drilldown_dimension
    sql = f"""
WITH error_stats AS (
    SELECT
        {metric.time_field}, {dimension},
        {metric.error_field} as error_code,
        SUM({metric.sample_ct_field}) as error_code_count,
        round(100.0 * SUM({metric.sample_ct_field}) / SUM(SUM({metric.sample_ct_field})) OVER (PARTITION BY {metric.time_field}, {dimension}), 2) as error_percentage,
        SUM(SUM({metric.sample_ct_field})) OVER (PARTITION BY {metric.time_field}, {dimension}) as total_error_count
    FROM {table_str}
    WHERE {where_str}
    GROUP BY {metric.time_field}, {metric.error_field}, {dimension}
)
SELECT
    {metric.time_field}, {dimension},
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc
LIMIT 3000
"""
    return sql
