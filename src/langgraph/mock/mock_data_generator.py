"""
Mock数据生成器
用于在测试模式下生成模拟的分析数据，避免频繁调用大模型API
"""

import asyncio
from typing import Any, Dict


class MockDataGenerator:
    """Mock数据生成器"""

    @staticmethod
    def generate_mock_plan(current_round: int, query: str) -> Dict[str, Any]:
        """生成mock计划数据"""
        if current_round == 1:
            return {
                "goal": f"分析{query}",
                "thinking": f"需要分析指定AppID {query}的推流成功率情况。首先分析整体趋势和错误码分布，然后检查各维度的聚集性特征。重点关注是否有国家、SDK版本或src维度的异常表现。",
                "steps": [
                    {
                        "id": 1,
                        "depends_on": [],
                        "query_params": {
                            "metric_name": "推流成功率",
                            "drilldown_dimension": None,
                            "where": None,
                            "time_start": "2025-07-28 11:35:57",
                            "time_end": "2025-08-27 11:35:57",
                            "appid_filter": query,
                            "country_filter": None,
                            "query_title": f"分析{query}整体推流成功率时间趋势",
                        },
                    },
                    {
                        "id": 2,
                        "depends_on": [],
                        "query_params": {
                            "metric_name": "推流错误码分布",
                            "drilldown_dimension": None,
                            "where": None,
                            "time_start": "2025-07-28 11:35:57",
                            "time_end": "2025-08-27 11:35:57",
                            "appid_filter": query,
                            "country_filter": None,
                            "query_title": f"分析{query} App推流错误码分布情况",
                        },
                    },
                    {
                        "id": 3,
                        "depends_on": [1, 2],
                        "query_params": {
                            "metric_name": "推流成功率",
                            "drilldown_dimension": "country",
                            "where": None,
                            "time_start": "2025-07-28 11:35:57",
                            "time_end": "2025-08-27 11:35:57",
                            "appid_filter": query,
                            "country_filter": None,
                            "query_title": f"按国家维度下钻分析{query}推流成功率",
                        },
                    },
                    {
                        "id": 4,
                        "depends_on": [1, 2],
                        "query_params": {
                            "metric_name": "推流成功率",
                            "drilldown_dimension": "sdk_version",
                            "where": None,
                            "time_start": "2025-07-28 11:35:57",
                            "time_end": "2025-08-27 11:35:57",
                            "appid_filter": query,
                            "country_filter": None,
                            "query_title": f"按SDK版本维度下钻分析{query}推流成功率",
                        },
                    },
                    {
                        "id": 5,
                        "depends_on": [3, 4],
                        "query_params": {
                            "metric_name": "推流成功率",
                            "drilldown_dimension": "src",
                            "where": None,
                            "time_start": "2025-07-28 11:35:57",
                            "time_end": "2025-08-27 11:35:57",
                            "appid_filter": query,
                            "country_filter": None,
                            "query_title": f"按src维度下钻分析{query}推流成功率",
                        },
                    },
                ],
                "round": 1,
            }
        elif current_round == 2:
            return {
                "goal": f"综合分析{query}推流成功率问题，提供最终结论和建议",
                "thinking": f"基于已有的多维度分析结果，已经确认了{query} App推流成功率存在严重问题。主要发现：1. 时间趋势：8月15日开始从97%降至89%，下降8.71个百分点 2. 错误码分布：主要问题是12200101(RTP hello timeout)和10007010(用户取消) 3. 国家维度：多国同步异常，美国最严重 4. SDK版本：两个版本同步下降，排除版本问题 5. src维度：发现关键聚集性，空值维度从89.96%降至70.3%，降幅19.71个百分点。现在需要进一步分析问题期间的错误码分布，特别是针对src维度的聚集性，以确定根本原因。",
                "steps": [
                    {
                        "id": 1,
                        "depends_on": [],
                        "query_params": {
                            "metric_name": "推流错误码分布",
                            "drilldown_dimension": "src",
                            "where": "time_start >= '2025-08-15 00:00:00' AND time_end <= '2025-08-26 23:59:59'",
                            "time_start": "2025-08-15 00:00:00",
                            "time_end": "2025-08-26 23:59:59",
                            "appid_filter": query,
                            "country_filter": None,
                            "query_title": f"分析问题期间{query}按src维度的推流错误码分布",
                        },
                    },
                    {
                        "id": 2,
                        "depends_on": [1],
                        "query_params": {
                            "metric_name": "推流错误码分布",
                            "drilldown_dimension": "country",
                            "where": "time_start >= '2025-08-15 00:00:00' AND time_end <= '2025-08-26 23:59:59'",
                            "time_start": "2025-08-15 00:00:00",
                            "time_end": "2025-08-26 23:59:59",
                            "appid_filter": query,
                            "country_filter": None,
                            "query_title": f"分析问题期间{query}按国家维度的推流错误码分布",
                        },
                    },
                ],
                "round": 2,
            }
        else:
            return {
                "goal": f"第{current_round}轮分析{query}",
                "thinking": f"第{current_round}轮分析思路...",
                "steps": [
                    {
                        "id": 1,
                        "depends_on": [],
                        "query_params": {"metric_name": "推流成功率", "query_title": f"第{current_round}轮分析任务"},
                    }
                ],
                "round": current_round,
            }

    @staticmethod
    def generate_mock_worker_result(step_title: str, current_round: int, step_id: int) -> Dict[str, Any]:
        """生成mock工作结果"""

        # 第一轮任务的mock结果
        if current_round == 1:
            if step_id == 1:  # 整体趋势分析
                return {
                    "objective_conclusion": f"确认{step_title.split('分析')[1].split(' App')[0]}在2025-08-15至2025-08-26期间存在严重的推流成功率下降问题，从正常97%以上水平降至89%左右，降幅约8.71个百分点。置信度高（高）。建议立即进行错误码分布分析和维度下钻，检查是否存在国家、SDK版本或src维度的聚集性问题。",
                    "objective_evidence": "2025-07-29至2025-08-14期间，推流成功率稳定在97%左右（97.57-97.83），波动很小。但从2025-08-15开始，推流成功率出现显著下降：2025-08-15降至94.99%，2025-08-16降至91.78%，2025-08-17降至90.49%，随后持续在89%左右波动（88.96%-90.49%）。总体从97.67%下降约8.71个百分点。每日样本量在6万到8万之间，数据充足。",
                    "subjective_thinking": "基于数据特征分析：1) 时间趋势显示明显的转折点在2025-08-15，之前稳定在97%左右，之后持续下降到89%左右；2) 问题持续时间长，从2025-08-15持续到至少2025-08-26；3) 样本量充足，每日6-8万，数据可靠性高；4) 下降幅度较大（8.71个百分点），属于严重问题。根据分析要求，需要进一步检查错误码分布和维度聚集性，特别是country、sdk_version、src维度是否存在聚集性问题。",
                }
            elif step_id == 2:  # 错误码分布分析
                return {
                    "objective_conclusion": "分析结论（置信度：高）：推流成功率整体表现良好，维持在85%-94%的正常范围内。主要问题为网络连接超时(12200101错误码)，占比2.22%-5.29%，是影响成功率的主要因素。未发现特定错误码聚集性问题，12200101错误码分布相对均匀，无特定国家或运营商聚集特征。",
                    "objective_evidence": "基于2025-07-28至2025-08-27期间的推流错误码分布数据，观察到以下客观数据特征：1. 整体推流成功率稳定在85%-94%之间，整体表现良好 2. 成功(error_code=0)占比在85.26%-93.92%之间波动 3. 主要错误码分布：12200101 (rtp hello timeout): 占比最高，2.22%-5.29%；10007010 (用户取消推拉流): 占比1.35%-4.17%；10009002 (网络类型改变): 占比0.63%-3.09%",
                    "subjective_thinking": "分析过程：1. 首先确认问题基本特征：整体推流成功率在85%-94%之间，属于正常范围，没有出现严重下滑 2. 识别主要错误码：12200101(rtp hello timeout)占比最高，是主要问题点 3. 分析错误码类型分布：网络相关错误码占比约2.7%-6.7%，用户操作相关错误码占比1.35%-4.17% 4. 评估趋势变化：8月14-16日表现最佳，8月17日后略有下降但保持稳定",
                }
            else:  # 其他任务
                dimensions = ["国家", "SDK版本", "src", "运营商"]
                dim = dimensions[(step_id - 3) % len(dimensions)]
                return {
                    "objective_conclusion": f"确认{dim}维度存在显著的推流成功率聚集性问题，置信度高。发现关键异常维度表现严重下降，需要重点关注。",
                    "objective_evidence": f"基于{dim}维度的推流成功率数据，观察到明显的聚集性特征。正常期间各{dim}表现稳定，异常期间出现同步下降，降幅在6-16个百分点之间。",
                    "subjective_thinking": f"通过{dim}维度分析发现：1) 问题具有全局性特征，多个{dim}同步出现异常；2) 降幅显著，属于严重问题；3) 时间点一致，符合系统级问题特征；4) 需要进一步分析具体的错误码分布。",
                }

        # 第二轮任务的mock结果
        elif current_round == 2:
            if step_id == 1:  # src维度错误码分布
                return {
                    "objective_conclusion": '分析置信度：高。推流成功率问题在src维度存在明显聚集性：1) src="rtc"主要问题是RTP连接超时(12200101)，占比3.98%-5.42%；2) src=""维度存在严重异常，用户取消错误码(10007010)占比高达70.25%-80.37%，远超正常水平，可能代表系统异常终止而非真实用户行为。建议重点调查空值src的来源、触发条件和系统配置，这可能是导致推流成功率下降的关键因素。',
                    "objective_evidence": '数据时间范围：2025-08-15 00:00:00 至 2025-08-26 23:59:59，按src维度下钻分析的推流错误码分布。数据包含两个主要src值："rtc"和空值""。对于src="rtc"，成功占比87.24%-92.77%，主要错误码为12200101(3.98%-5.42%)、10009002(2.31%-3.15%)、10007010(2.33%-2.51%)、12200106(0.71%-1.00%)。对于src=""，10007010占比异常高达70.25%-80.37%，35500013占比10.78%-16.7%，35500004占比5.23%-9.13%。',
                    "subjective_thinking": '分析src维度的推流错误码分布，发现两个关键问题：1) src="rtc"维度主要存在网络连接问题，12200101(RTP hello timeout)占比最高，表明RTP连接超时是主要瓶颈；2) src=""维度存在严重聚集性，10007010(用户取消)占比异常高达70%以上，远超正常水平。结合错误码说明，10007010表示用户取消推拉流，但在空值src中占比如此之高，可能暗示这些并非真正的用户主动取消，而是系统层面的异常终止。',
                }
            else:  # 其他第二轮任务
                return {
                    "objective_conclusion": "第二轮深度分析发现关键问题聚集性，置信度高。问题主要集中在特定维度，需要系统级解决方案。",
                    "objective_evidence": "基于问题期间的深度分析数据，发现多个维度存在异常聚集性特征，错误码分布呈现明显的模式化特征。",
                    "subjective_thinking": "通过深度分析发现问题的根本原因可能与系统配置或网络环境变化相关，需要从技术架构层面寻找解决方案。",
                }

        # 默认mock结果
        return {
            "objective_conclusion": f"Mock分析结论：{step_title}的分析结果显示存在显著问题，置信度高。",
            "objective_evidence": f"Mock客观证据：基于数据分析发现{step_title}在指定时间段内表现异常。",
            "subjective_thinking": f"Mock主观分析：通过分析{step_title}的数据特征，发现问题主要集中在特定维度。",
        }

    @staticmethod
    def generate_mock_reporter_result(query: str) -> Dict[str, Any]:
        """生成mock报告结果"""
        return {
            "thinking": f"综合分析显示，{query} App推流成功率问题具有明显的维度聚集性，特别是src维度的空值维度表现异常。问题从8月15日开始持续至今，影响范围广泛。通过多维度分析排除了SDK版本问题，聚焦到src维度的异常表现，特别是空值src中用户取消错误码占比异常高，这可能是系统异常终止而非真实用户行为。",
            "executive_summary": f"{query} App推流成功率严重下降问题已确认，从8月15日开始从97%降至89%，下降8.71个百分点。问题根源在于src维度聚集性，空值维度成功率暴跌19.71个百分点，用户取消错误码占比异常高达70-80%，表明存在系统异常终止而非真实用户行为。问题影响全球多个国家，每日影响6-7万次推流尝试，属于严重级别问题。",
            "key_findings": [
                "时间维度：问题从2025-08-15开始持续至今，已持续12天",
                "src维度：空值维度成功率从89.96%降至70.3%，降幅19.71个百分点；rtc维度从95.25%降至89.8%，降幅5.45个百分点",
                "错误码聚集：空值维度中用户取消错误码(10007010)占比高达70.25%-80.37%，远超正常水平",
                "全球影响：美国、沙特阿拉伯、尼日利亚、尼泊尔等多国同步异常，美国受影响最严重",
                "排除因素：两个SDK版本同步下降，排除版本问题",
            ],
            "supporting_evidence": [
                "整体趋势数据：推流成功率从97%左右降至89%左右，下降幅度约8.71个百分点，每日样本量6-8万",
                "src维度数据：空值维度8月15日从89.96%骤降至80.37%，8月16日进一步降至74.52%",
                '错误码分布：src=""维度中10007010占比70.25%-80.37%，35500013占比10.78%-16.7%',
                "国家影响：美国成功率从94.48%降至86.39%，沙特阿拉伯从97.38%降至92.65%",
            ],
            "analysis_reasoning": '问题定位路径：整体趋势确认→多维度下钻→发现src维度聚集性→错误码分析→确认空值维度系统异常。问题特征为突发性下降、多国同步、多版本同步，符合技术故障特征。空值维度中用户取消错误码占比异常高，表明这些"用户取消"可能实际上是系统异常终止，需要调查空值src的来源和触发条件。',
            "critical_issues": '关键问题为src=""维度的系统异常终止问题，用户取消错误码占比高达70-80%，远超正常水平。这表明存在严重的系统逻辑错误，导致推流过程中异常终止而非正常结束。问题影响范围广，每日影响6-7万次推流尝试，已持续12天，属于严重级别技术故障，需要立即调查空值src的来源、触发条件和系统配置。',
        }

    @staticmethod
    async def mock_delay(delay_seconds: float = 2.0):
        """模拟执行延迟，增加0~0.5秒的随机扰动"""
        import random

        await asyncio.sleep(delay_seconds + random.uniform(0, 0.5))
