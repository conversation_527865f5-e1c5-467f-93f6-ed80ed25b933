"""
DataFrame 存取 Store 的成对工具函数。

提供两个对称的工具函数：
- trans_df_to_store_data: 将 pandas DataFrame 转为 (records, columns) 元组（均可直接写入 Store）
- get_df_from_store_data: 由 (records, columns) 恢复为 DataFrame

注意：Store 适合存放 JSON 可序列化的轻量数据。大体量数据建议外部持久化（如 Parquet/S3），Store 仅保存 URI/元信息。
"""

from __future__ import annotations

import math

from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Tuple

import pandas as pd


def _normalize_json_value(value: Any) -> Any:
    """将值转换为可被 JSON 序列化的原生类型。

    - Decimal -> float
    - numpy 标量 -> Python 标量（.item()）
    - datetime / pandas.Timestamp -> ISO 字符串
    - NaN/Inf -> None
    - 递归处理 list/tuple/dict
    """
    try:  # 可选依赖
        import numpy as np  # type: ignore
    except Exception:  # pragma: no cover
        np = None  # type: ignore

    if isinstance(value, dict):
        return {k: _normalize_json_value(v) for k, v in value.items()}
    if isinstance(value, (list, tuple)):
        return [_normalize_json_value(v) for v in value]

    if isinstance(value, Decimal):
        return float(value)
    if isinstance(value, (datetime,)):
        return value.isoformat()
    # pandas.Timestamp: duck-typing 处理
    if hasattr(value, "isoformat") and not isinstance(value, str):
        try:
            return value.isoformat()
        except Exception:
            pass

    if np is not None and isinstance(value, (np.generic,)):
        try:
            return _normalize_json_value(value.item())
        except Exception:
            pass

    if isinstance(value, float):
        if math.isnan(value) or math.isinf(value):
            return None
        return value

    return value


def trans_df_to_store_data(df: pd.DataFrame) -> Tuple[List[Dict[str, Any]], List[str]]:
    """将 DataFrame 转换为 (records, columns) 元组，便于直接写入 Store。

    - records: list[dict]，每行记录（已做 JSON 安全归一化）
    - columns: list[str]，列名
    """
    if df is None or df.empty:
        return [], []

    records = df.to_dict(orient="records")
    serializable_records: List[Dict[str, Any]] = _normalize_json_value(records)
    data_columns: List[str] = list(df.columns)
    return serializable_records, data_columns


def get_df_from_store_data(records: List[Dict[str, Any]] | List[List[Any]], columns: List[str]) -> pd.DataFrame:
    """从 (records, columns) 恢复为 DataFrame。"""
    if not records or not columns:
        return pd.DataFrame()
    return pd.DataFrame(records, columns=columns)
