# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import re
from typing import Any, Optional

import json_repair

logger = logging.getLogger(__name__)


def _strip_code_fence(text: str) -> str:
    """去除围栏代码块，优先选择```json/```ts，其次选择第一个```...```。

    保留内部内容；若无围栏，原样返回。
    """
    text = text.strip()
    if not text:
        return text

    # 优先：匹配```json ... ``` 或 ```ts ... ```
    pattern_labeled = re.compile(r"```(?:json|ts)\s*\n([\s\S]*?)\n```", re.IGNORECASE)
    m = pattern_labeled.search(text)
    if m:
        return m.group(1).strip()

    # 其次：匹配任意``` ... ```
    pattern_any = re.compile(r"```\s*\n([\s\S]*?)\n```", re.IGNORECASE)
    m2 = pattern_any.search(text)
    if m2:
        return m2.group(1).strip()

    return text


def _extract_first_json_candidate(text: str) -> Optional[str]:
    """从文本中提取第一个看起来像 JSON 的片段（对象或数组）。

    策略：
    1) 如以 { 或 [ 开头，直接返回全文作为候选；
    2) 否则，在全文中寻找第一个 { 或 [，从该位置做括号配对提取；
    3) 若配对失败，返回从该位置到文本结尾的子串，交给 json_repair 兜底；
    4) 若未找到任何起始符，返回 None。
    """
    s = text.strip()
    if not s:
        return None
    if s[0] in "[{":
        return s

    start_index_obj = s.find("{")
    start_index_arr = s.find("[")
    starts = [i for i in [start_index_obj, start_index_arr] if i != -1]
    if not starts:
        return None
    start = min(starts)

    # 括号配对（忽略字符串与转义影响的简化实现）
    opening = s[start]
    closing = "}" if opening == "{" else "]"
    depth = 0
    in_string = False
    escape = False
    for i in range(start, len(s)):
        ch = s[i]
        if in_string:
            if escape:
                escape = False
            elif ch == "\\":
                escape = True
            elif ch == '"':
                in_string = False
            continue
        else:
            if ch == '"':
                in_string = True
                continue
            if ch == opening:
                depth += 1
            elif ch == closing:
                depth -= 1
                if depth == 0:
                    return s[start : i + 1].strip()

    # 未能成功闭合，返回从起始到结尾的部分，交给 json_repair 尝试
    return s[start:].strip()


def repair_json_output(content: str) -> str:
    """
    修复并规范化包含在任意文本中的 JSON 输出：
    - 识别任意位置的围栏代码块(```json/```ts/```...```)
    - 支持正文中嵌入的 JSON 片段（对象或数组）
    - 使用 json_repair 进行容错解析，最终返回紧凑的标准 JSON 字符串

    返回：
        成功时返回规范化 JSON 字符串；若无法识别 JSON，则返回原始内容（去首尾空白）。
    """
    content = (content or "").strip()
    if not content:
        return content

    # 优先从围栏代码提取
    candidate = _strip_code_fence(content)
    # 再从正文中提取 JSON 片段
    candidate = _extract_first_json_candidate(candidate) or candidate

    try:
        repaired_obj = json_repair.loads(candidate)
        return json.dumps(repaired_obj, ensure_ascii=False)
    except Exception as e:
        logger.warning(f"JSON repair failed: {e}")
        return content


def _looks_like_schema_dict(obj: Any) -> bool:
    """判断一个 dict 是否像 JSON Schema，而不是业务数据对象。"""
    if not isinstance(obj, dict):
        return False
    if "$defs" in obj:
        return True
    if isinstance(obj.get("properties"), dict):
        return True
    if obj.get("type") == "object" and "title" in obj and "properties" in obj:
        return True
    return False


def _looks_like_schema_text(text: str) -> bool:
    """从原始文本维度粗略判断是否为 schema 回显（弱信号）。"""
    if not text:
        return False
    t = text.strip()
    if "```" in t:  # 明显的代码块
        return True
    lowered = t.lower()
    # 典型 schema 关键词，仅在无法成功解析 JSON 时作为弱信号
    return ('"$defs"' in lowered or '"properties"' in lowered) and '"type"' in lowered and '"object"' in lowered
