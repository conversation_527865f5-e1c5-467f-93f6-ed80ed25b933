import typing
from typing import Optional, Union

from langchain_core.messages import AIMessage


class MessageUtils:
    @staticmethod
    def parse_include_raw_response(
        response: dict,
    ) -> tuple[
        AIMessage,
        Optional[Union[typing.Dict, type]],
        Optional[BaseException],
    ]:
        response_raw: AIMessage = response.get("raw") or response.get("message") or response.get("output")
        response_parsed: Optional[Union[typing.Dict, type]] = response.get("parsed", None)
        # 兼容不同键名：error / parsing_error / parsing_errors
        response_error: Optional[BaseException] = (
            response.get("error") or response.get("parsing_error") or response.get("parsing_errors")
        )
        return response_raw, response_parsed, response_error
