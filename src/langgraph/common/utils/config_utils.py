from __future__ import annotations

from langchain_core.runnables import RunnableConfig


def is_mock_mode(config: RunnableConfig) -> bool:
    configurable = config["configurable"]
    return configurable.get("mock_mode", 0) == 1


def get_mock_delay(config: RunnableConfig) -> float:
    configurable = config["configurable"]
    return float(configurable.get("mock_delay", 2.0))


def get_max_plans(config: RunnableConfig) -> int:
    configurable = config["configurable"]
    return int(configurable.get("max_plans", 2))


def get_max_parallel_workers(config: RunnableConfig) -> int:
    configurable = config["configurable"]
    return int(configurable.get("max_parallel_workers", 5))


def get_max_step_cnt(config: RunnableConfig) -> int:
    configurable = config["configurable"]
    return int(configurable.get("max_step_cnt", 30))


def get_query_id(config: RunnableConfig) -> str:
    configurable = config["configurable"]
    return configurable.get("query_id")


def get_workspace_id(config: RunnableConfig) -> int:
    configurable = config["configurable"]
    return int(configurable.get("workspace_id"))


def get_thread_id(config: RunnableConfig) -> str:
    configurable = config["configurable"]
    return configurable.get("thread_id")
