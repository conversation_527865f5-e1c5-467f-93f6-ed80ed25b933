import logging
import typing
from collections.abc import AsyncIterator
from contextlib import asynccontextmanager
from functools import wraps
from typing import Any, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage

from langchain_core.runnables.utils import Output

from src.langgraph.common.models.llm_config import (
    get_llm_instance,
    get_max_retries,
    RequestMode,
    should_immediately_switch_model,
)
from src.langgraph.common.utils.json_utils import _looks_like_schema_dict, _looks_like_schema_text, repair_json_output
from src.langgraph.common.utils.message_utils import MessageUtils
from src.langgraph.common.utils.socket_router import ensure_socket_patched, plain_socket_context
from src.langgraph.common.utils.token_utils import TokenTools

logger = logging.getLogger(__name__)

# 直接使用统一的模型管理系统（已集成智能重试功能）


def _anti_schema_messages() -> list[BaseMessage]:
    """当模型出现 schema 回显倾向时，建议追加到提示里的系统/人类约束消息。"""
    return [
        SystemMessage(
            content=(
                "严格只输出一个JSON数据对象；不要返回或复制schema文本；"
                "不要使用任何Markdown代码块（如```或```json）；不要输出解释性文字。"
            )
        ),
        HumanMessage(content="请给我内容，而不是schema"),
    ]


# Ensure socket is patched once so per-context routing is active
ensure_socket_patched()


@asynccontextmanager
async def llm_request_context():
    """LLM 请求上下文：强制使用直连 socket，杜绝 SOCKS 影响。"""
    async with plain_socket_context():
        yield


def llm_safe_request(func):
    """
    装饰器：确保LLM请求在安全的网络环境中执行，不受SOCKS代理影响
    """

    @wraps(func)
    async def wrapper(*args, **kwargs):
        async with llm_request_context():
            return await func(*args, **kwargs)

    return wrapper


def create_token_update(input_tokens: int, output_tokens: int) -> dict:
    """
    创建token更新字典，用于更新状态中的token统计

    Args:
        input_tokens: 输入token数量
        output_tokens: 输出token数量

    Returns:
        包含token统计的字典
    """
    return {
        "total_input_tokens": input_tokens,
        "total_output_tokens": output_tokens,
    }


async def get_response_from_astream(chunks: AsyncIterator[Output]) -> Output:
    first = True
    async for chunk in chunks:
        if first:
            response = chunk
            first = False
        else:
            response = response + chunk

    if isinstance(response, dict):
        return response

    # 给glm4.5适配
    ori_content = response.content
    if "</think>" in ori_content:
        # 找到最后一个</think>，前后分为 part1 和 part2
        last_think_index = ori_content.rfind("</think>")
        part1 = ori_content[:last_think_index]
        part2 = ori_content[last_think_index + len("</think>") :]

        response.content = part2
        response.additional_kwargs["reasoning_content"] = part1.replace("<think>", "").replace("</think>", "")

    return response


def get_io_tokens_from_response(response_raw) -> tuple[int, int]:
    try:
        if response_raw.usage_metadata is not None:  # deepseek-chat、deepseek-reasoner
            input_tokens = response_raw.usage_metadata["input_tokens"]
            output_tokens = response_raw.usage_metadata["output_tokens"]
            return input_tokens, output_tokens
    except Exception:
        pass
    try:
        if response_raw.response_metadata is not None:  # glm-4.5-air
            input_tokens = response_raw.response_metadata["token_usage"]["prompt_tokens"]
            output_tokens = response_raw.response_metadata["token_usage"]["completion_tokens"]
            return input_tokens, output_tokens
    except Exception:
        pass
    return 0, 0
    # qwen-flash jsonmode 拿不到
    # qwen3-30b jsonmode 拿不到


@llm_safe_request
async def llm_structured_request(
    node_name: str,
    input_messages: List[BaseMessage],
    schema_type: Union[typing.Dict, type],
    llm_params: dict[str, Any] = {},
    retry_index=0,
    max_retries: Optional[int] = None,
) -> tuple[
    AIMessage,
    Optional[Union[typing.Dict, type]],
    Optional[BaseException],
]:
    # 确定最大重试次数
    if max_retries is None:
        max_retries = get_max_retries(RequestMode.JSON_MODE)

    last_error = None
    # 当检测到模型回显schema时，动态追加的系统约束消息
    extra_system_messages: list[BaseMessage] = []
    anti_schema_hint_added = False

    for current_retry in range(max_retries + 1):  # +1 因为第一次不算重试
        try:
            # 智能重试逻辑：检查是否应该立即切换模型
            retry_index = current_retry
            if current_retry > 0 and last_error and should_immediately_switch_model(last_error):
                # 立即切换到下一个模型
                retry_index = ((current_retry - 1) // 2 + 1) * 2
                logger.info(f"[{node_name}] Immediately switching model due to error: {type(last_error).__name__}")

            # 获取LLM实例
            llm = get_llm_instance(RequestMode.JSON_MODE, retry_index, llm_params)
            if llm is None:
                logger.error(f"[{node_name}] No model available for retry #{current_retry}")
                break

            if current_retry > 0:
                logger.info(f"[{node_name}] Retry #{current_retry} using retry_index={retry_index}")

            # 合并额外的系统消息
            messages_to_use: List[BaseMessage] = list(input_messages) + list(extra_system_messages)
            approximately_input_tokens = TokenTools.count_messages_approximately_tokens(messages_to_use)
            logger.info(f"[{node_name}] {llm_params=}, {approximately_input_tokens=} - retry_index={retry_index}")

            # 执行请求
            try:
                response = await llm.with_structured_output(
                    schema_type,
                    include_raw=True,
                    # method="json_mode",
                ).ainvoke(messages_to_use)

            except Exception as e:
                # 记录更详细的异常信息（含堆栈与错误类型）
                try:
                    model_name = getattr(llm, "model", None) or getattr(llm, "model_name", None) or str(llm)
                except Exception:
                    model_name = "unknown"
                logger.exception(
                    f"[{node_name}] ❌ 请求失败ainvoke, error_type={type(e).__name__}, error={e!r}, model={model_name}"
                )
                try:
                    response = await get_response_from_astream(
                        # method="json_mode",
                        llm.with_structured_output(
                            schema_type,
                            include_raw=True,
                            # method="json_mode",
                        ).astream(messages_to_use)
                    )
                except Exception as e:
                    logger.exception(
                        f"[{node_name}] ❌ 请求失败astream, error_type={type(e).__name__}, error={e!r}, model={model_name}"
                    )
                    last_error = e
                    continue

            logger.info(f"[{node_name}] ✅ 请求成功，type(response)={type(response)}")

            # 解析输出
            response_raw, response_parsed, response_error = MessageUtils.parse_include_raw_response(response)

            # Token统计 - 初始化默认值以避免未定义变量错误
            input_tokens, output_tokens = get_io_tokens_from_response(response_raw)
            TokenTools.fit_to_coefficient(messages_to_use, input_tokens)
            logger.info(f"[{node_name}] ✅ usage_metadata={response_raw.usage_metadata}")

            # 检查解析失败并尝试从原始文本修复/识别schema回显
            if response_parsed is None:
                raw_text = getattr(response_raw, "content", "")
                # 1) 优先尝试修复并解析为JSON，然后用目标schema强校验
                try:
                    import json

                    repaired = repair_json_output(raw_text)
                    parsed_json = None
                    try:
                        parsed_json = json.loads(repaired)
                    except Exception:
                        parsed_json = None
                    schema_cls = schema_type if isinstance(schema_type, type) else None
                    if parsed_json is not None and schema_cls is not None and hasattr(schema_cls, "model_validate"):
                        try:
                            parsed_obj = schema_cls.model_validate(parsed_json)
                            response_parsed = parsed_obj
                            response_error = None
                        except Exception as inner_validate_error:
                            # 2) 若能解析为JSON但不符合目标schema，判断是否为“schema回显”
                            if _looks_like_schema_dict(parsed_json):
                                logger.warning(
                                    f"[{node_name}] ❌检测到模型回显schema（解析为JSON但不匹配目标数据schema），追加系统约束并重试"
                                )
                                if not anti_schema_hint_added:
                                    extra_system_messages.extend(_anti_schema_messages())
                                    anti_schema_hint_added = True
                                last_error = inner_validate_error
                                continue
                            else:
                                # 非schema回显，保留校验错误继续走重试/升级
                                response_error = inner_validate_error
                    elif parsed_json is None:
                        # 3) JSON解析失败：在文本层面粗略识别schema回显（避免把正确数据错杀）
                        if _looks_like_schema_text(raw_text or ""):
                            if not anti_schema_hint_added:
                                logger.warning(f"[{node_name}] 文本疑似schema回显，追加系统约束并重试")
                                extra_system_messages.extend(_anti_schema_messages())
                                anti_schema_hint_added = True
                            last_error = ValueError("模型回显了schema（文本层面）")
                            continue
                except Exception as e:
                    response_error = response_error or e

            if response_parsed is None:
                error_msg = f"响应解析失败: {response_error}"
                logger.error(f"[{node_name}] ❌ {error_msg}")
                last_error = response_error or ValueError(error_msg)
                continue

            return response_raw, response_parsed, response_error, input_tokens, output_tokens

        except Exception as e:
            logger.error(f"[{node_name}] ❌请求异常: {e}")
            last_error = e
            continue

    # 所有重试都失败了
    logger.error(f"[{node_name}] ❌所有重试都失败，最后错误: {last_error}")
    raise last_error or RuntimeError("All retries failed")


@llm_safe_request
async def llm_str_request(
    node_name: str,
    input_messages: List[BaseMessage],
    llm_params: dict[str, Any] = {},
    stage_name: Optional[str] = None,
    max_retries: Optional[int] = None,
) -> tuple[str, str]:
    # 提前定义，避免在异常早于赋值时引用未绑定变量
    node_name = f"{node_name}:{stage_name}" if stage_name else node_name

    # 确定最大重试次数
    if max_retries is None:
        max_retries = get_max_retries(RequestMode.TEXT_MODE)

    last_error = None

    for current_retry in range(max_retries + 1):  # +1 因为第一次不算重试
        try:
            # 智能重试逻辑：检查是否应该立即切换模型
            retry_index = current_retry
            if current_retry > 0 and last_error and should_immediately_switch_model(last_error):
                # 立即切换到下一个模型
                retry_index = ((current_retry - 1) // 2 + 1) * 2
                logger.info(f"[{node_name}] Immediately switching model due to error: {type(last_error).__name__}")

            # 获取LLM实例
            llm = get_llm_instance(RequestMode.TEXT_MODE, retry_index, llm_params)
            if llm is None:
                logger.error(f"[{node_name}] No model available for retry #{current_retry}")
                break

            if current_retry > 0:
                logger.info(f"[{node_name}] Retry #{current_retry} using retry_index={retry_index}")

            approximately_input_tokens = TokenTools.count_messages_approximately_tokens(input_messages)
            logger.info(f"[{node_name}] {llm_params=}, {approximately_input_tokens=} - retry_index={retry_index}")

            # 执行请求
            response = await get_response_from_astream(llm.astream(input_messages))

            response_content = response.content
            response_cot = response.additional_kwargs.get("reasoning_content", "")

            logger.info(
                f"[{node_name}] response.content.len={len(response_content)}, cot.len={len(response_cot)}, token:{response.usage_metadata}"
            )
            input_tokens, output_tokens = get_io_tokens_from_response(response)
            TokenTools.fit_to_coefficient(input_messages, input_tokens)
            logger.info(f"[{node_name}] ✅ usage_metadata={response.usage_metadata}")

            return response_content, response_cot, input_tokens, output_tokens

        except Exception as e:
            logger.error(f"[{node_name}] ❌请求异常: {e}")
            last_error = e
            continue

    # 所有重试都失败了
    logger.error(f"[{node_name}] ❌所有重试都失败，最后错误: {last_error}")
    return "", ""  # 文本模式返回空字符串而不是抛异常
