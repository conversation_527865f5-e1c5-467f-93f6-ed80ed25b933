import logging
import os
from typing import Any

from dotenv import load_dotenv

from langchain.chat_models import init_chat_model
from langchain_community.embeddings import DashScopeEmbeddings
from langchain_core.language_models import BaseChatModel

# values
logger = logging.getLogger(__name__)

# url key
load_dotenv(os.path.join(os.path.dirname(__file__), ".env"))
providers_data = {
    "deepseek": {"base_url": os.getenv("DEEPSEEK_BASE_URL"), "api_key": os.getenv("DEEPSEEK_API_KEY")},
    "ppio": {"base_url": os.getenv("PPIO_BASE_URL"), "api_key": os.getenv("PPIO_API_KEY")},
    "ali": {"base_url": os.getenv("ALI_BASE_URL"), "api_key": os.getenv("ALI_API_KEY")},
    "shangtang": {"base_url": os.getenv("SHANGTANG_BASE_URL"), "api_key": os.getenv("SHANGTANG_API_KEY")},
    "openrouter": {"base_url": os.getenv("OPENROUTER_BASE_URL"), "api_key": os.getenv("OPENROUTER_API_KEY")},
    "glm": {"base_url": os.getenv("GLM_BASE_URL"), "api_key": os.getenv("GLM_API_KEY")},
    "kimi": {"base_url": os.getenv("KIMI_BASE_URL"), "api_key": os.getenv("KIMI_API_KEY")},
}


models = [
    # ds
    # https://api-docs.deepseek.com/zh-cn/quick_start/pricing
    ("deepseek", "deepseek-chat"),  # 输入128k, 输出8k;输入4,输出12
    ("deepseek", "deepseek-reasoner"),  # 输入128k, 输出64k;输入4,输出12
    # ali
    # https://bailian.console.aliyun.com/?tab=model#/model-market
    ("ali", "qwen3-30b-a3b-instruct-2507"),  # 输入128k,输出32k;输入0.75,输出7.5
    ("ali", "qwen3-30b-a3b-thinking-2507"),  # 输入128k,输出32k;输入0.75,输出3
    ("ali", "qwen-flash"),  # 输入1M,输出32k;输入0.15~1.2, 输出1.5~12;阶梯 0~128k~256k
    ("ali", "qwen-plus-2025-07-28"),  # 输入1M,输出32k;输入0.08~4.8, 输出2~4.8, 思考输出8~64;阶梯 0~128k~256k
    ("ali", "qwen3-235b-a22b-instruct-2507"),  # 输入128k,输出32k;输入2,输出8
    ("ali", "qwen3-235b-a22b-thinking-2507"),  # 输入128k,输出32k;输入2,输出20
    # kimi
    # https://platform.moonshot.cn/docs/pricing/chat#%E4%BA%A7%E5%93%81%E5%AE%9A%E4%BB%B7
    ("kimi", "kimi-k2-0711-preview"),  # 128k,4,16,
    ("kimi", "kimi-latest"),  # 128k, 阶梯 2~10,10~30;
    ("ppio", "moonshotai/kimi-k2-instruct"),  # 128k,4,16
    # glm
    # https://open.bigmodel.cn/pricing
    # https://docs.bigmodel.cn/cn/guide/start/model-overview
    ("glm", "glm-4.5"),  # 128k,96k;输入2~4,输出8~16; 阶梯计费、限时一折
    ("glm", "glm-4.5-air"),  # 128k,96k; 输入0.8~1.2,输出2~8; 阶梯计费、限时一折
    ("glm", "glm-4.5v"),  # 64k,16k; 输入2~4,输出6~12;视觉模型, 阶梯计费、限时一折
    # ppio中转
    # https://ppio.com/model-api/console
    ("ppio", "qwen/qwen3-235b-a22b-instruct-2507"),  # 有时候乱码
    ("ppio", "qwen/qwen3-235b-a22b-thinking-2507"),  # 有时候乱码
]


class _llm_cfg:
    qwen_enable_thinking = {"extra_body": {"enable_thinking": True}}
    glm_disable_thinking = {"extra_body": {"thinking": {"type": "disabled"}}}
    glm_enable_thinking = {"extra_body": {"thinking": {"type": "enabled"}}}


active_json_models = [
    {"provider": "glm", "model": "glm-4.5-air", "params": _llm_cfg.glm_disable_thinking},
    {"provider": "glm", "model": "glm-4.5-air", "params": _llm_cfg.glm_enable_thinking},
    {"provider": "ali", "model": "qwen-flash", "params": {}},
    {"provider": "ali", "model": "qwen3-30b-a3b-instruct-2507", "params": {}},
    # {"provider": "ali", "model": "qwen-plus-2025-07-28", "params": {}},
    # {"provider": "deepseek", "model": "deepseek-chat", "params": {}},
]

active_text_models = [
    {"provider": "glm", "model": "glm-4.5-air", "params": _llm_cfg.glm_disable_thinking},
    {"provider": "glm", "model": "glm-4.5-air", "params": _llm_cfg.glm_enable_thinking},
    # {"provider": "ali", "model": "qwen-flash", "params": {}},
    # {"provider": "ali", "model": "qwen-flash", "params": _llm_cfg.qwen_enable_thinking},
    # {"provider": "ali", "model": "qwen-plus-2025-07-28", "params": {}},
    # {"provider": "ali", "model": "qwen-plus-2025-07-28", "params": _llm_cfg.qwen_enable_thinking},
]


def get_llm_by_code(provider_name: str, model_code: str, extra_params: dict[str, Any] = {}) -> BaseChatModel:
    default_extra_params = {"max_tokens": 1024 * 256, "temperature": 0.5}
    llm_params = {"model": model_code, "model_provider": "openai", **providers_data[provider_name]}
    merge_params = {**llm_params, **default_extra_params, **extra_params}  # 如果有相同的key，后者覆盖前者

    assert merge_params["base_url"] is not None, merge_params
    assert merge_params["api_key"] is not None, merge_params

    # deepseek
    if provider_name == "glm" and "glm" in model_code:
        from langchain_community.chat_models import ChatZhipuAI

        merge_params.pop("model_provider")
        merge_params.pop("base_url")
        merge_params.pop("max_tokens")
        zhipu_model = ChatZhipuAI(**merge_params)
        return zhipu_model

    if provider_name == "deepseek" and "deepseek" in model_code:
        from langchain_deepseek import ChatDeepSeek

        merge_params.pop("model_provider")
        merge_params["api_base"] = merge_params["base_url"]
        if model_code == "deepseek-chat":
            merge_params["max_tokens"] = 1024 * 4  # 输出 4～8k
        elif model_code == "deepseek-reasoner":
            merge_params["max_tokens"] = 1024 * 32  # 输出 32~64k
        else:
            logger.warning(f"[{model_code}] deepseek模型，未设置max_tokens")
        deepseek_model = ChatDeepSeek(**merge_params)
        return deepseek_model

    # 其他通用
    chat_model = init_chat_model(**merge_params)

    return chat_model


def get_default_embedding_model():
    ali_embedding_model = DashScopeEmbeddings(
        model="text-embedding-v4",
        dashscope_api_key=os.getenv("ALI_API_KEY"),
    )
    return ali_embedding_model


def get_default_llm():
    return get_llm_by_code("ali", "qwen3-30b-a3b-instruct-2507")


if __name__ == "__main__":
    query = "你是谁，是否开启了思考模式"
    print("--------------------------------")
    print(query)
    print("--------------------------------")
    print(get_llm_by_code("glm", "glm-4.5-air", _llm_cfg.glm_enable_thinking).invoke(query))
    print("--------------------------------")
    print(get_llm_by_code("glm", "glm-4.5-air", _llm_cfg.glm_disable_thinking).invoke(query))
    print("--------------------------------")
