"""QFlowAgent Functional API 入口

将原有 StateGraph(Graph API) 迁移为 Functional API：
- 通过 @entrypoint orchestration 循环调度 planner → worker(并发) → reporter
- 复用原有节点实现（planner_node/worker_node/reporter_node），在任务层收敛为 state update
- 仍通过 AsyncPostgresStore / AsyncPostgresSaver 提供 store 与 checkpoint 持久化
"""

from __future__ import annotations

import logging

from typing import Any, Dict

from langgraph.config import get_config, get_stream_writer

from langgraph.func import entrypoint

from src.langgraph.common.utils.config_utils import (
    get_max_parallel_workers,
    get_max_plans,
    get_max_step_cnt,
    is_mock_mode,
)
from src.langgraph.core import db_health_check, planner_agent, reporter_agent, worker_agent

from src.langgraph.state import append_plans, ConfigSchema

logger = logging.getLogger(__name__)


def _apply_update(state: Dict, update: Dict) -> Dict:
    """按 State 聚合语义合并增量更新。"""
    if not update:
        return state
    # messages: 追加
    if "messages" in update and update["messages"]:
        state["messages"] = list(state.get("messages", [])) + list(update["messages"])  # 保序追加
    # tokens: 求和
    for k in ("total_input_tokens", "total_output_tokens"):
        if k in update and update[k] is not None:
            state[k] = int(state.get(k, 0)) + int(update[k])
    # plans: 采用 append_plans 语义
    if "plans" in update and update["plans"]:
        state["plans"] = append_plans(state.get("plans"), update["plans"])  # type: ignore
    # current_step: 覆盖（通常仅用于派发，不保存在最终状态）
    if "current_step" in update:
        state["current_step"] = update["current_step"]

    # reporter 结果：覆盖
    if "reporter_result" in update and update["reporter_result"] is not None:
        state["reporter_result"] = update["reporter_result"]
    return state


# 移除LangGraph store，使用统一的redis_manager
@entrypoint(context_schema=ConfigSchema, name="zego_team_v2")
def workflow_v2(inputs: Dict[str, Any], *, previous: Dict[str, Any] | None = None):
    state: Dict[str, Any] = dict(previous or {})
    config = get_config()
    state["step_cnt"] = 0
    state["user_input"] = inputs["messages"][0].content
    assert state["user_input"]
    # 初始化输入
    state = _apply_update(state, inputs or {})
    writer = get_stream_writer()

    if is_mock_mode(config):
        logger.info("[planner] 🎭 使用Mock模式，跳过健康检查")
    else:
        try:
            health_upd = db_health_check(state).result()
            state = _apply_update(state, health_upd)
            writer(state)
        except Exception as e:
            error_state = {
                "health_check_passed": False,
                "health_check_error": str(e),
                "reporter_result": {"final_report": f"❌ 系统健康检查失败: {str(e)}", "error": True},
            }
            state = _apply_update(state, error_state)
            writer(state)
            return entrypoint.final(value=state, save=state)

    # plan - worker - replan
    while True:
        # 限制只进行2轮次
        plans = state.get("plans", []) or []
        if len(plans) >= get_max_plans(config):
            break
        upd = planner_agent(state).result()
        if not upd:
            break
        state = _apply_update(state, upd)
        writer(state)
        # 规划后刷新 plan 引用
        plans = state.get("plans", []) or []
        plan = plans[-1] if plans else None

        # 并发派发，直到没有 READY 任务为止（一次执行内自旋但不使用 continue/外层 while）
        # add do_plan step 以便处理 replan的情况
        while True:
            steps = list(getattr(plan, "steps", []) or []) if plan else []

            assert all(step.status != "RUNNING" for step in steps)
            # get next ready_steps
            ready_steps = plan.refresh_ready_steps()

            for step in steps:
                logger.info(f"[all]{step.id}. {step.query_params.query_title}")
                logger.info(f"[all]  - status: {step.status:<10} depends_on: {step.depends_on}")

            if not ready_steps:
                break
            limit = get_max_parallel_workers(config)
            dispatch_list = ready_steps[: max(1, limit)]

            for step in dispatch_list:
                logger.info(f"[run]{step.id}. {step.query_params.query_title}")
                logger.info(f"[run]  - status: {step.status:<10} depends_on: {step.depends_on}")

            futs = []
            for t in dispatch_list:
                futs.append(worker_agent(state, t))
                state["step_cnt"] += 1
                if state["step_cnt"] > get_max_step_cnt(config):
                    logger.warning("[run] 达到分析任务执行上限，结束派发，将已派发的执行完")
                    break

            # 把派发的执行完
            for fut in futs:
                state_update = fut.result()
                state = _apply_update(state, state_update)
                writer(state)

            if state["step_cnt"] > get_max_step_cnt(config):
                logger.warning("[run] 达到分析任务执行上限，结束分析，进行汇报")
                break

    upd = reporter_agent(state).result()
    state = _apply_update(state, upd)
    writer(state)
    return entrypoint.final(value=state, save=state)
