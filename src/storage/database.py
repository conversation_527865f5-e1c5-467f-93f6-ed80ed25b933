"""
数据库配置和连接管理
基于SQLModel的异步数据库操作
"""

import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from dotenv import load_dotenv
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool
from sqlmodel import SQLModel

from .models import *  # 导入所有模型以确保表结构被注册

load_dotenv()


class DatabaseConfig:
    """数据库配置类"""

    def __init__(self):
        # 从环境变量获取数据库配置
        self.database_url = os.getenv("STORAGE_DATABASE_URL")

        # 连接池配置
        self.pool_size = int(os.getenv("DB_POOL_SIZE", "10"))
        self.max_overflow = int(os.getenv("DB_MAX_OVERFLOW", "20"))
        self.pool_timeout = int(os.getenv("DB_POOL_TIMEOUT", "30"))
        self.pool_recycle = int(os.getenv("DB_POOL_RECYCLE", "3600"))

        # 连接配置
        self.echo = os.getenv("DB_ECHO", "false").lower() == "true"
        self.echo_pool = os.getenv("DB_ECHO_POOL", "false").lower() == "true"


# 全局引擎实例
_engine: Optional[AsyncEngine] = None
_config = DatabaseConfig()


def get_engine() -> AsyncEngine:
    """获取数据库引擎实例"""
    global _engine

    if _engine is None:
        # 根据数据库URL类型选择不同的配置
        if _config.database_url.startswith("sqlite"):
            # SQLite配置 (用于测试)
            _engine = create_async_engine(
                _config.database_url,
                echo=_config.echo,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
            )
        else:
            # PostgreSQL配置 (用于生产)
            _engine = create_async_engine(
                _config.database_url,
                echo=_config.echo,
                echo_pool=_config.echo_pool,
                pool_size=_config.pool_size,
                max_overflow=_config.max_overflow,
                pool_timeout=_config.pool_timeout,
                pool_recycle=_config.pool_recycle,
                # PostgreSQL特定配置
                connect_args={
                    "server_settings": {
                        "application_name": "qflowagent_storage",
                        "jit": "off",  # 关闭JIT以提高小查询性能
                    }
                },
            )

    return _engine


async def create_tables():
    """创建所有数据表"""
    engine = get_engine()

    async with engine.begin() as conn:
        # 创建所有SQLModel定义的表
        await conn.run_sync(SQLModel.metadata.create_all)


async def drop_tables():
    """删除所有数据表 (仅用于测试)"""
    engine = get_engine()

    async with engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.drop_all)


@asynccontextmanager
async def get_session() -> AsyncGenerator[AsyncSession]:
    """获取数据库会话的上下文管理器"""
    engine = get_engine()

    async with AsyncSession(engine, expire_on_commit=False) as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


class SessionManager:
    """会话管理器 - 用于依赖注入"""

    def __init__(self):
        self.engine = get_engine()

    async def get_session(self) -> AsyncSession:
        """获取新的数据库会话"""
        return AsyncSession(self.engine, expire_on_commit=False)

    @asynccontextmanager
    async def session_scope(self) -> AsyncGenerator[AsyncSession]:
        """会话作用域上下文管理器"""
        session = await self.get_session()
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


# 全局会话管理器实例
session_manager = SessionManager()


async def init_database():  # 失败则抛异常
    """初始化数据库"""
    print("正在初始化存储数据库...")

    try:
        # 测试数据库连接
        async with get_session() as session:
            from sqlalchemy import text

            await session.execute(text("SELECT 1"))
        print(f"✅ 数据库连接测试成功 db_url='{os.getenv("STORAGE_DATABASE_URL")}'")

        # 创建表结构
        await create_tables()
        print("✅ 数据表创建完成")

        # 创建默认工作空间
        await _create_default_workspace()
        print("✅ 默认工作空间创建完成")

        print("🎉 存储数据库初始化完成!")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise e


async def _create_default_workspace():
    """创建默认工作空间"""
    async with get_session() as session:
        from sqlmodel import select

        from .models import WorkspaceModel

        # 检查是否已存在默认工作空间
        result = await session.execute(select(WorkspaceModel).where(WorkspaceModel.name == "default"))
        existing = result.first()

        if not existing:
            # 创建默认工作空间
            default_workspace = WorkspaceModel(
                name="default", description="默认工作空间 - 用于向下兼容", config={"is_default": True}
            )
            session.add(default_workspace)
            await session.commit()


async def cleanup_database():
    """清理数据库连接"""
    global _engine

    if _engine:
        await _engine.dispose()
        _engine = None
        print("✅ 数据库连接已清理")


# 用于FastAPI依赖注入的函数
async def get_db_session() -> AsyncGenerator[AsyncSession]:
    """FastAPI依赖注入用的会话获取函数"""
    async with session_manager.session_scope() as session:
        yield session
