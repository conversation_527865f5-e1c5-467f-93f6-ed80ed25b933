"""
枚举类型定义
统一LLM和SQLModel使用的状态类型
"""

from enum import Enum


class TaskStatus(str, Enum):  # TODO 统一用一个
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StepStatus(str, Enum):
    """步骤状态枚举"""

    PENDING = "pending"
    READY = "ready"
    RUNNING = "running"
    DONE = "done"
    FAILED = "failed"


class ResultType(str, Enum):
    """结果类型枚举"""

    INTERMEDIATE = "intermediate"
    FINAL = "final"
    ERROR = "error"


class NodeType(str, Enum):
    """节点类型枚举"""

    PLANNER = "planner_agent"
    WORKER = "worker_agent"
    REPORTER = "reporter_agent"
