"""
任务相关数据模型
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from pydantic import field_serializer, TypeAdapter
from sqlalchemy import Column, func, TIMESTAMP
from sqlmodel import Field, JSON, Relationship, SQLModel

from src.storage.enums import StepStatus, TaskStatus

if TYPE_CHECKING:
    from src.storage.models.sqlmodel_workspace import WorkspaceModel


class TaskModel(SQLModel, table=True):
    """任务模型 - 存储任务基本信息和状态"""

    __tablename__ = "tasks"

    # 主键字段
    id: Optional[int] = Field(default=None, primary_key=True)

    # 唯一标识符
    query_id: str = Field(index=True, unique=True, description="调用方指定的查询ID")
    thread_id: str = Field(index=True, unique=True, description="LangGraph兼容的线程ID")

    # 工作空间关联
    workspace_id: int = Field(foreign_key="workspaces.id", index=True, description="所属工作空间ID")

    # 任务内容
    user_query: str = Field(description="用户原始查询")
    status: TaskStatus = Field(default=TaskStatus.PENDING, index=True, description="任务状态")

    # 配置信息
    config: Dict[str, Any] = Field(default_factory=dict, sa_type=JSON, description="任务配置")

    # 时间戳
    timestamp: datetime | None = Field(
        default=None,
        sa_column=Column(TIMESTAMP(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now()),
    )

    @field_serializer("timestamp")
    def serialize_datetime(self, ts: datetime | None) -> str | None:
        return ts.isoformat() if ts else None

    # 关系
    workspace: "WorkspaceModel" = Relationship(back_populates="tasks")
    plans: List["TaskPlanModel"] = Relationship(back_populates="task", cascade_delete=True)
    result: "TaskResultModel" = Relationship(back_populates="task", cascade_delete=True)

    def __repr__(self) -> str:
        return f"<TaskModel(id={self.id}, query_id='{self.query_id}', status='{self.status}')>"

    @property
    def is_terminal_state(self) -> bool:
        """检查是否为终止状态"""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]

    def to_full_dict(self) -> Dict[str, Any]:
        task_dict = self.model_dump()
        task_dict["plans"] = []
        for plan in self.plans:
            plan_dict = plan.model_dump()
            plan_dict["steps"] = [step.model_dump() for step in plan.steps]
            task_dict["plans"].append(plan_dict)
        task_dict["result"] = self.result.model_dump() if self.result else None

        return task_dict

    def to_full_json(self) -> str:
        full_dict = self.to_full_dict()
        adapter = TypeAdapter(Dict[str, Any])
        return adapter.dump_json(full_dict).decode()  # 不加decode的话就是编码的


class TaskPlanModel(SQLModel, table=True):
    """任务规划模型 - 存储DAG规划信息"""

    __tablename__ = "task_plans"

    # 主键字段
    id: Optional[int] = Field(default=None, primary_key=True)

    # 外键
    task_id: int = Field(foreign_key="tasks.id", index=True, description="关联任务ID")

    # 规划内容
    goal: str = Field(description="规划目标")
    thinking: Optional[str] = Field(default=None, description="规划思路")
    round: int = Field(default=1, description="规划轮次", index=True)

    # 元数据
    final_round: bool = Field(default=True, index=True, description="是否为最终轮次")
    step_count: int = Field(default=0, description="规划子步骤数")

    # 时间戳
    timestamp: Optional[datetime] = Field(
        default=None,
        sa_column=Column(TIMESTAMP(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now()),
    )

    @field_serializer("timestamp", when_used="json-unless-none")
    def serialize_datetime(self, ts: datetime) -> str:
        return ts.isoformat()

    # 关系
    task: TaskModel = Relationship(back_populates="plans")
    steps: List["TaskPlanStepModel"] = Relationship(back_populates="plan")

    def __repr__(self) -> str:
        return f"<TaskPlanModel(id={self.id}, task_id={self.task_id}, round={self.round})>"


class TaskPlanStepModel(SQLModel, table=True):
    """任务步骤模型 - 存储具体执行步骤"""

    __tablename__ = "task_steps"

    # 主键字段
    id: Optional[int] = Field(default=None, primary_key=True)

    # 关联字段
    plan_id: int = Field(foreign_key="task_plans.id", index=True, description="关联规划ID")

    # 步骤信息
    round: int = Field(description="属于第几轮plan", index=True)
    step_id: int = Field(description="DAGPlanTask中的步骤ID")
    step_name: str = Field(description="步骤名称")
    depends_on: List[int] = Field(default_factory=list, sa_column=Column(JSON), description="依赖的步骤ID列表")
    status: StepStatus = Field(default=StepStatus.PENDING, description="步骤状态")

    # 查询参数和结果
    query_params: Dict[str, Any] = Field(default_factory=dict, sa_type=JSON, description="查询参数")
    query_result: Optional[Dict[str, Any]] = Field(default=None, sa_type=JSON, description="查询结果")
    worker_result: Optional[Dict[str, Any]] = Field(default=None, sa_type=JSON, description="分析结果")

    # 时间戳
    timestamp: Optional[datetime] = Field(
        default=None,
        sa_column=Column(TIMESTAMP(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now()),
    )

    @field_serializer("timestamp", when_used="json-unless-none")
    def serialize_datetime(self, ts: datetime) -> str:
        return ts.isoformat()

    # 关系
    plan: TaskPlanModel = Relationship(back_populates="steps")

    def __repr__(self) -> str:
        return f"<TaskPlanStepModel(id={self.id}, step_id={self.step_id}, round={self.round}), status='{self.status}')>"

    @property
    def is_ready(self) -> bool:
        """检查步骤是否准备就绪(所有依赖已完成)"""
        return self.status == StepStatus.READY

    @property
    def is_terminal_state(self) -> bool:
        """检查是否为终止状态"""
        return self.status in [StepStatus.DONE, StepStatus.FAILED]


class TaskResultModel(SQLModel, table=True):
    """任务结果模型 - 存储分析结果和报告"""

    __tablename__ = "task_results"

    # 主键字段
    id: Optional[int] = Field(default=None, primary_key=True)

    # 关联字段
    task_id: int = Field(foreign_key="tasks.id", index=True, description="关联任务ID")

    # 结果内容
    structured_data: Optional[Dict[str, Any]] = Field(default=None, sa_type=JSON, description="结构化数据")

    # 时间戳
    timestamp: Optional[datetime] = Field(
        default=None,
        sa_column=Column(TIMESTAMP(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now()),
    )

    @field_serializer("timestamp", when_used="json-unless-none")
    def serialize_datetime(self, ts: datetime) -> str:
        return ts.isoformat()

    # 关系
    task: TaskModel = Relationship(back_populates="result")

    def __repr__(self) -> str:
        return f"<TaskResultModel(id={self.id}, task_id={self.task_id}, structured_data='{self.structured_data}')>"
