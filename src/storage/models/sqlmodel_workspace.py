"""
数据模型定义
基于SQLModel的自定义存储模型
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from sqlmodel import Field, JSON, Relationship, SQLModel


if TYPE_CHECKING:
    from src.storage.models.sqlmodel_task import TaskModel


class WorkspaceModel(SQLModel, table=True):
    """工作空间模型 - 支持多租户数据隔离"""

    __tablename__ = "workspaces"

    # 主键字段
    id: Optional[int] = Field(default=None, primary_key=True)

    # 基本信息
    name: str = Field(max_length=100, index=True, unique=True, description="工作空间名称")
    description: Optional[str] = Field(default=None, description="工作空间描述")

    # 配置信息
    config: Dict[str, Any] = Field(default_factory=dict, sa_type=JSON, description="工作空间配置")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    # 关系
    tasks: List["TaskModel"] = Relationship(back_populates="workspace")

    def __repr__(self) -> str:
        return f"<WorkspaceModel(id={self.id}, name='{self.name}')>"
