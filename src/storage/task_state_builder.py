"""
任务状态构建器 - 统一的任务状态数据构建逻辑
消除重复代码，提供一致的数据格式
"""

import logging
from typing import Any, Dict, List


logger = logging.getLogger(__name__)


class TaskStateBuilder:
    """任务状态构建器 - 提供统一的数据构建方法"""

    @staticmethod
    def calculate_plan_progress(plan_steps) -> tuple[int, str]:
        """计算计划进度和状态 - 单一职责，易于测试"""
        if not plan_steps:
            return 0, "PENDING"

        total = len(plan_steps)
        done_count = sum(1 for step in plan_steps if step.status.value == "done")

        # 状态优先级：FAILED > RUNNING > DONE > PENDING
        if any(step.status.value == "failed" for step in plan_steps):
            status = "FAILED"
        elif any(step.status.value == "running" for step in plan_steps):
            status = "RUNNING"
        elif done_count == total:
            status = "DONE"
        else:
            status = "PENDING"

        progress = int((done_count / total) * 100)
        return progress, status

    @staticmethod
    def calculate_step_progress(step_status: str) -> int:
        """计算单个步骤进度 - 消除魔法数字"""
        progress_map = {
            "done": 100,
            "running": 50,
            "failed": 0,
            "pending": 0,
            "ready": 0,
        }
        return progress_map.get(step_status, 0)

    @staticmethod
    def format_step_status(status) -> str:
        """格式化步骤状态 - 统一状态格式"""
        if hasattr(status, "value"):
            return status.value.upper()
        return str(status).upper()

    @staticmethod
    def build_step_data(step) -> Dict[str, Any]:
        """构建单个步骤数据 - 提取复杂逻辑"""
        return {
            "id": step.step_id,
            "name": step.step_name,
            "status": TaskStateBuilder.format_step_status(step.status),
            "progress": TaskStateBuilder.calculate_step_progress(step.status.value),
            "query_params": step.query_params,
            "result": step.worker_result,
            "timestamps": {
                "created": step.timestamp.isoformat(),
            },
        }

    @staticmethod
    def build_plan_data(plan, plan_steps: List) -> Dict[str, Any]:
        """构建计划数据 - 统一的计划数据构建"""
        progress, status = TaskStateBuilder.calculate_plan_progress(plan_steps)

        return {
            "id": plan.id,
            "goal": plan.goal,
            "thinking": plan.thinking,
            "status": status,
            "progress": progress,
            "round": plan.round,
            "timestamp": plan.timestamp.isoformat(),
            "steps": [TaskStateBuilder.build_step_data(step) for step in plan_steps],
        }

    @staticmethod
    def build_task_summary(task, plans_count: int = 0, steps_count: int = 0) -> Dict[str, Any]:
        """构建任务摘要数据 - 用于列表显示"""
        return {
            "query_id": task.query_id,
            "thread_id": task.thread_id,
            "workspace_id": task.workspace_id,
            "status": task.status.value,
            "user_query": task.user_query,
            "plans_count": plans_count,
            "steps_count": steps_count,
            "timestamp": task.timestamp.isoformat(),
        }


def get_task_state_builder() -> TaskStateBuilder:
    """获取全局任务状态构建器实例"""
    global _task_state_builder
    if _task_state_builder is None:
        _task_state_builder = TaskStateBuilder()
    return _task_state_builder
