"""
工作空间服务层
提供多租户工作空间管理功能
"""

from datetime import datetime

from logging import getLogger
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import and_, asc, desc, select

from src.storage.models.sqlmodel_task import TaskModel

from ..enums import TaskStatus

from ..models import TaskPlanStepModel, WorkspaceModel

logger = getLogger(__name__)


class WorkspaceService:
    """工作空间服务类 - 处理工作空间相关的所有业务逻辑"""

    def __init__(self, db: AsyncSession):
        self.db = db

    # === 工作空间基本操作 ===

    async def create_workspace(
        self, name: str, description: Optional[str] = None, config: Optional[Dict[str, Any]] = None
    ) -> WorkspaceModel:
        """创建新工作空间"""

        # 检查名称是否已存在
        existing = await self.get_workspace_by_name(name)
        if existing:
            raise ValueError(f"Workspace with name '{name}' already exists")

        workspace = WorkspaceModel(name=name, description=description, config=config or {})

        self.db.add(workspace)
        await self.db.commit()
        await self.db.refresh(workspace)

        return workspace

    async def get_workspace_by_id(self, workspace_id: int) -> Optional[WorkspaceModel]:
        """根据ID获取工作空间"""
        return await self.db.get(WorkspaceModel, workspace_id)

    async def get_workspace_by_name(self, name: str) -> Optional[WorkspaceModel]:
        """根据名称获取工作空间"""
        result = await self.db.execute(select(WorkspaceModel).where(WorkspaceModel.name == name))
        return result.scalar_one_or_none()

    async def list_workspaces(
        self, limit: int = 20, offset: int = 0, order_by: str = "created_at", order_desc: bool = True
    ) -> Tuple[List[WorkspaceModel], int]:
        """获取工作空间列表"""

        # 排序
        order_column = getattr(WorkspaceModel, order_by, WorkspaceModel.created_at)
        order_func = desc if order_desc else asc

        # 查询工作空间列表
        query = select(WorkspaceModel).order_by(order_func(order_column)).offset(offset).limit(limit)
        try:
            result = await self.db.execute(query)
            workspaces = result.scalars().all()
        except Exception as e:
            logger.error(f"Error listing workspaces: {e}")
            return [], 0

        # 查询总数
        count_result = await self.db.execute(select(WorkspaceModel))
        total_count = len(count_result.scalars().all())

        return list(workspaces), total_count

    async def update_workspace(
        self,
        workspace_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
    ) -> Optional[WorkspaceModel]:
        """更新工作空间"""
        workspace = await self.get_workspace_by_id(workspace_id)
        if not workspace:
            return None

        # 检查名称唯一性
        if name and name != workspace.name:
            existing = await self.get_workspace_by_name(name)
            if existing:
                raise ValueError(f"Workspace with name '{name}' already exists")
            workspace.name = name

        if description is not None:
            workspace.description = description

        if config is not None:
            workspace.config = config

        workspace.updated_at = datetime.now()

        await self.db.commit()
        await self.db.refresh(workspace)

        return workspace

    async def delete_workspace(self, workspace_id: int, force: bool = False) -> bool:
        """删除工作空间"""
        workspace = await self.get_workspace_by_id(workspace_id)
        if not workspace:
            return False

        # 检查是否有关联的任务
        if not force:
            task_count = await self.get_workspace_task_count(workspace_id)
            if task_count > 0:
                raise ValueError(
                    f"Cannot delete workspace '{workspace.name}' with {task_count} active tasks. "
                    "Use force=True to delete anyway."
                )

        await self.db.delete(workspace)
        await self.db.commit()

        return True

    # === 工作空间统计 ===

    async def get_workspace_stats(self, workspace_id: int) -> Dict[str, Any]:
        """获取工作空间统计信息"""
        try:
            workspace = await self.get_workspace_by_id(workspace_id)
            if not workspace:
                return {}

            # 查询任务统计
            task_query = select(TaskModel).where(TaskModel.workspace_id == workspace_id)
            task_result = await self.db.execute(task_query)
            tasks = task_result.scalars().all()

            # 统计各状态任务数量
            status_counts = {}
            for status in TaskStatus:
                status_counts[status.value] = sum(1 for task in tasks if task.status == status)

            # 最近活动
            recent_tasks = sorted(tasks, key=lambda x: x.timestamp, reverse=True)[:5]

            return {
                "workspace_id": workspace_id,
                "workspace_name": workspace.name,
                "total_tasks": len(tasks),
                "status_distribution": status_counts,
                "recent_tasks": [
                    {
                        "query_id": task.query_id,
                        "status": task.status,
                    }
                    for task in recent_tasks
                ],
                "created_at": workspace.created_at.isoformat(),
                "updated_at": workspace.updated_at.isoformat(),
            }
        except Exception as e:
            logger.error(f"Error getting workspace stats: {e}")
            raise e

    async def get_workspace_task_count(self, workspace_id: int) -> int:
        """获取工作空间任务数量"""
        result = await self.db.execute(select(TaskPlanStepModel).where(TaskPlanStepModel.workspace_id == workspace_id))
        tasks = result.scalars().all()
        return len(tasks)

    async def get_workspace_active_task_count(self, workspace_id: int) -> int:
        """获取工作空间活跃任务数量"""
        result = await self.db.execute(
            select(TaskPlanStepModel).where(
                and_(
                    TaskPlanStepModel.workspace_id == workspace_id,
                    TaskPlanStepModel.status.in_([TaskStatus.PENDING, TaskStatus.RUNNING]),
                )
            )
        )
        tasks = result.scalars().all()
        return len(tasks)

    # === 默认工作空间管理 ===

    async def get_default_workspace(self) -> Optional[WorkspaceModel]:
        """获取默认工作空间"""
        # 首先查找标记为默认的工作空间
        result = await self.db.execute(
            select(WorkspaceModel).where(WorkspaceModel.config.op("->>")('"is_default"') == "true")
        )
        workspace = result.scalar_one_or_none()

        if workspace:
            return workspace

        # 如果没有标记为默认的，返回名为"default"的工作空间
        return await self.get_workspace_by_name("default")

    async def ensure_default_workspace(self) -> WorkspaceModel:
        """确保默认工作空间存在"""
        default_workspace = await self.get_default_workspace()

        if not default_workspace:
            default_workspace = await self.create_workspace(
                name="default", description="默认工作空间 - 用于向下兼容", config={"is_default": True}
            )

        return default_workspace

    # === 数据隔离管理 ===

    async def get_workspace_schema_name(self, workspace_id: int) -> str:
        """获取工作空间对应的PostgreSQL Schema名称"""
        return f"workspace_{workspace_id}"

    async def get_workspace_redis_prefix(self, workspace_id: int) -> str:
        """获取工作空间对应的Redis键前缀"""
        return f"{workspace_id}:"

    async def create_workspace_schema(self, workspace_id: int) -> bool:
        """为工作空间创建专用PostgreSQL Schema"""
        try:
            from sqlalchemy import text

            schema_name = self.get_workspace_schema_name(workspace_id)

            # 创建Schema
            await self.db.execute(text(f"CREATE SCHEMA IF NOT EXISTS {schema_name}"))
            await self.db.commit()

            return True
        except Exception as e:
            await self.db.rollback()
            raise e

    async def drop_workspace_schema(self, workspace_id: int) -> bool:
        """删除工作空间的PostgreSQL Schema"""
        try:
            from sqlalchemy import text

            schema_name = self.get_workspace_schema_name(workspace_id)

            # 删除Schema及其所有内容
            await self.db.execute(text(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE"))
            await self.db.commit()

            return True
        except Exception as e:
            await self.db.rollback()
            raise e

    # === 权限验证 ===

    async def validate_workspace_access(self, workspace_id: int, user_id: Optional[str] = None) -> bool:
        """验证工作空间访问权限"""
        # 检查工作空间是否存在
        workspace = await self.get_workspace_by_id(workspace_id)
        if not workspace:
            return False

        # 这里可以添加更复杂的权限逻辑
        # 目前简单返回True，因为项目是个人项目不需要权限验证
        return True

    async def get_user_workspaces(self, user_id: str) -> List[WorkspaceModel]:
        """获取用户有权限访问的工作空间列表"""
        # 对于个人项目，返回所有工作空间
        workspaces, _ = await self.list_workspaces(limit=1000)
        return workspaces
