
import logging
from typing import List, Tuple

from langgraph.config import get_config

from sqlalchemy.orm import selectinload
from sqlmodel import desc, func, select

from src.langgraph.common.utils.config_utils import get_query_id

from src.langgraph.core.common.types import D<PERSON><PERSON><PERSON>, DAGPlanStep, TaskResult
from src.storage.database import get_session
from src.storage.models.sqlmodel_task import TaskModel, TaskPlanModel, TaskPlanStepModel, TaskResultModel
from src.storage.pg_notifier import get_pg_notifier

logger = logging.getLogger(__name__)


async def db_insert_task_result(task_result: TaskResult) -> TaskResultModel:
    async with get_session() as session:
        try:
            query_id = get_query_id(get_config())
            task_query = await session.execute(select(TaskModel).where(TaskModel.query_id == query_id))
            task_record = task_query.scalar_one()

            result_record = TaskResultModel(
                id=None,
                task_id=task_record.id,
                structured_data=task_result.model_dump(),
            )

            session.add(result_record)
            await session.commit()
            await session.refresh(result_record)

            pg_notifier = await get_pg_notifier()
            result_content = {
                "type": "result_record",
                "query_id": query_id,
                "body": {
                    "result_record": result_record.model_dump(),
                },
            }
            await pg_notifier.notify(f"channel_{query_id}", result_content)
            return result_record

        except Exception as e:
            logger.error(f"Failed to update step status: {e}")
            await session.rollback()
            raise e


async def db_update_step_status(step: DAGPlanStep) -> TaskPlanStepModel:
    async with get_session() as session:
        try:
            query_id = get_query_id(get_config())
            task_query = await session.execute(select(TaskModel).where(TaskModel.query_id == query_id))
            task_record = task_query.scalar_one()

            plan_record = (
                await session.execute(
                    select(TaskPlanModel).where(
                        TaskPlanModel.task_id == task_record.id,
                        TaskPlanModel.round == step.round,
                    )
                )
            ).scalar_one()

            step_record = (
                await session.execute(
                    select(TaskPlanStepModel).where(
                        TaskPlanStepModel.plan_id == plan_record.id,
                        TaskPlanStepModel.round == step.round,
                        TaskPlanStepModel.step_id == step.id,
                    )
                )
            ).scalar_one()

            step_record.status = step.status
            step_record.worker_result = step.worker_result.model_dump() if step.worker_result else None
            step_record.query_result = step.query_result.model_dump(exclude={"data"}) if step.query_result else None

            await session.commit()
            await session.refresh(step_record)

            pg_notifier = await get_pg_notifier()
            step_content = {
                "type": "step_record",
                "query_id": query_id,
                "body": {
                    "step_record": step_record.model_dump(),
                },
            }
            await pg_notifier.notify(f"channel_{query_id}", step_content)
            return step_record

        except Exception as e:
            logger.error(f"Failed to update step status: {e}")
            await session.rollback()
            raise e


async def db_save_plan(plan: DAGPlan) -> TaskPlanModel:
    assert all(plan.round == step.round for step in plan.steps), "plan.round != step.round"

    async with get_session() as session:
        try:
            query_id = get_query_id(get_config())

            task_record = (await session.execute(select(TaskModel).where(TaskModel.query_id == query_id))).scalar_one()

            # 前面的轮次标为旧
            prev_plan_records = (
                (
                    await session.execute(
                        select(TaskPlanModel).where(
                            TaskPlanModel.task_id == task_record.id,
                            TaskPlanModel.round < plan.round,
                        )
                    )
                )
                .scalars()
                .all()
            )

            for prev_plan_record in prev_plan_records:
                prev_plan_record.final_round = False

            plan_record = TaskPlanModel(
                # 主键字段
                id=None,
                # 外键
                task_id=task_record.id,
                # Langgraph数据规划内容
                goal=plan.goal,
                thinking=plan.thinking,
                round=plan.round,
                # 元数据
                final_round=True,
                step_count=len(plan.steps),
                # 关系
                # task=None,
                # steps=None,
            )
            # 保存主规划记录以获取ID
            session.add(plan_record)
            await session.commit()
            await session.refresh(plan_record)  # 获取生成的plan_id,以便能存step

            step_records = [
                TaskPlanStepModel(
                    # 主键字段
                    id=None,
                    # 关联字段
                    plan_id=plan_record.id,
                    # 步骤信息
                    round=step.round,
                    step_id=step.id,
                    step_name=step.query_params.query_title,
                    depends_on=step.depends_on,
                    status=step.status,
                    # 查询参数和结果
                    query_params=step.query_params.model_dump(),
                    query_result=step.query_result.model_dump(exclude={"data"}) if step.query_result else None,
                    worker_result=step.worker_result.model_dump() if step.worker_result else None,
                    # 关系
                    # task=None,
                    # plan=None,
                )
                for step in plan.steps
            ]

            # 保存步骤记录
            session.add_all(step_records)
            await session.commit()
            for step_record in step_records:
                await session.refresh(step_record)
            await session.refresh(plan_record)

            pg_notifier = await get_pg_notifier()
            plan_content = {
                "type": "plan_record",
                "query_id": query_id,
                "body": {
                    "plan_record": plan_record.model_dump(),
                },
            }
            await pg_notifier.notify(f"channel_{query_id}", plan_content)

            return plan_record
        except Exception as e:
            logger.error(f"Failed to save plan: {e}")
            await session.rollback()
            raise e


async def db_get_tasks_by_workspace_id(workspace_id: int, page: int, page_size: int) -> Tuple[List[TaskModel], int]:

    limit = page_size
    offset = (page - 1) * page_size
    async with get_session() as session:
        try:
            # 总个数
            total_count_query = await session.execute(
                select(func.count(TaskModel.id)).where(TaskModel.workspace_id == workspace_id)
            )
            total_count = total_count_query.scalar()  # 使用scalar()获取单个值
            if total_count == 0:
                return [], 0

            # 列表
            query = (
                select(TaskModel)
                .where(TaskModel.workspace_id == workspace_id)
                .order_by(desc(TaskModel.timestamp))
                .offset(offset)
                .limit(limit)
            )
            tasks_result = await session.execute(query)
            tasks = tasks_result.scalars().all()

            return tasks, total_count
        except Exception as e:
            logger.error(f"Failed to get tasks by workspace_id: {e}")
            raise e


async def db_get_task_with_relations(query_id: str) -> TaskModel | None:
    async with get_session() as session:
        task_query = await session.execute(
            select(TaskModel)
            .where(TaskModel.query_id == query_id)
            .options(
                # 预加载plans关系
                selectinload(TaskModel.plans),
                # 预加载每个plan的steps关系
                selectinload(TaskModel.plans).selectinload(TaskPlanModel.steps),
                # 预加载results关系
                selectinload(TaskModel.result),
            )
        )
        task_record = task_query.scalar_one_or_none()

        return task_record
