"""
任务服务层
提供任务管理的核心业务逻辑
"""

from datetime import datetime

from logging import getLogger
from typing import Any, Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlmodel import and_, asc, desc, select

from ..enums import ResultType, StepStatus, TaskStatus
from ..models import TaskModel, TaskPlanModel, TaskPlanStepModel, TaskResultModel, WorkspaceModel

logger = getLogger(__name__)


class TaskService:
    """任务服务类 - 处理任务相关的所有业务逻辑"""

    def __init__(self, session: AsyncSession):
        self.session = session

    # === 任务基本操作 ===

    async def create_task(
        self,
        query_id: str,
        user_query: str,
        workspace_id: int,
        thread_id: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
    ) -> TaskModel:
        """创建新任务"""

        # 检查query_id是否已存在
        existing = await self.get_task_by_query_id(query_id)
        if existing:
            raise ValueError(f"Task with query_id '{query_id}' already exists")

        # 验证工作空间是否存在
        workspace = await self.session.get(WorkspaceModel, workspace_id)
        if not workspace:
            raise ValueError(f"Workspace with id {workspace_id} not found")

        # 创建任务
        task = TaskModel(
            query_id=query_id,
            thread_id=thread_id or f"thread_{query_id}_{int(datetime.now().timestamp())}",
            workspace_id=workspace_id,
            user_query=user_query,
            config=config or {},
            status=TaskStatus.PENDING,
        )

        self.session.add(task)
        await self.session.commit()
        await self.session.refresh(task)

        return task

    # === Sequence-based缓存方法 ===

    # === 任务查询操作 ===

    async def get_task_by_query_id(self, query_id: str) -> Optional[TaskModel]:
        """根据query_id获取任务"""
        result = await self.session.execute(select(TaskModel).where(TaskModel.query_id == query_id))
        return result.scalar_one_or_none()

    async def get_task_with_relations(self, query_id: str) -> Optional[TaskModel]:
        """获取任务及其关联数据"""
        result = await self.session.execute(
            select(TaskModel)
            .options(
                selectinload(TaskModel.workspace),
                selectinload(TaskModel.plans),
                selectinload(TaskModel.steps),
                selectinload(TaskModel.result),
            )
            .where(TaskModel.query_id == query_id)
        )
        return result.scalar_one_or_none()

    async def update_task_status(self, query_id: str, status: TaskStatus, error_message: Optional[str] = None) -> bool:
        """更新任务状态"""
        task = await self.get_task_by_query_id(query_id)
        if not task:
            return False

        task.status = status

        if error_message:
            task.error_message = error_message

        await self.session.commit()

        return True

    async def delete_task(self, query_id: str) -> bool:
        """删除任务及其关联数据"""
        task = await self.get_task_by_query_id(query_id)
        if not task:
            return False

        await self.session.delete(task)
        await self.session.commit()

        return True

    # === 任务规划操作 ===

    async def save_plan(
        self,
        query_id: str,
        goal: str,
        plan_data: Dict[str, Any],
        thinking: str = None,
        round: int = None,
    ) -> TaskPlanModel:
        """保存任务规划"""
        task = await self.get_task_by_query_id(query_id)
        if not task:
            raise ValueError(f"Task with query_id '{query_id}' not found")

        # 将之前的规划标记为非最终轮次
        await self._mark_plans_as_old(task.id)

        # 创建新规划
        plan = TaskPlanModel(
            task_id=task.id,
            goal=goal,
            thinking=thinking,
            plan_data=plan_data,
            round=round,
            final_round=True,
        )

        self.session.add(plan)
        await self.session.commit()
        await self.session.refresh(plan)

        # 更新任务步骤统计
        await self.session.commit()

        return plan

    async def get_task_plans(self, query_id: str, round: Optional[int] = None) -> List[TaskPlanModel]:
        """获取任务规划列表"""
        task = await self.get_task_by_query_id(query_id)
        if not task:
            return []

        query = select(TaskPlanModel).where(TaskPlanModel.task_id == task.id)

        if round:
            query = query.where(TaskPlanModel.round == round)
        else:
            query = query.order_by(asc(TaskPlanModel.round))

        result = await self.session.execute(query)
        return list(result.scalars().all())

    async def get_current_plan(self, query_id: str) -> Optional[TaskPlanModel]:
        """获取当前规划"""
        task = await self.get_task_by_query_id(query_id)
        if not task:
            return None

        result = await self.session.execute(
            select(TaskPlanModel).where(and_(TaskPlanModel.task_id == task.id, TaskPlanModel.final_round))
        )
        return result.scalar_one_or_none()

    # === 任务步骤操作 ===

    async def save_step(
        self,
        query_id: str,
        step_id: int,
        step_name: str,
        depends_on: List[int],
        query_params: Dict[str, Any],
        round: int,
    ) -> TaskPlanStepModel:
        """保存任务步骤"""
        task = await self.get_task_by_query_id(query_id)
        if not task:
            raise ValueError(f"Task with query_id '{query_id}' not found")

        # 获取规划
        if round:
            plans = await self.get_task_plans(query_id, round)
            plan = plans[0] if plans else None
        else:
            plan = await self.get_current_plan(query_id)

        if not plan:
            raise ValueError(f"No plan found for task '{query_id}'")

        # 检查步骤是否已存在
        existing = await self._get_step_by_step_id(task.id, plan.id, step_id)
        if existing:
            return existing

        # 创建步骤
        step = TaskPlanStepModel(
            task_id=task.id,
            plan_id=plan.id,
            step_id=step_id,
            step_name=step_name,
            depends_on=depends_on,
            query_params=query_params,
            status=StepStatus.PENDING,
        )

        self.session.add(step)
        await self.session.commit()
        await self.session.refresh(step)

        return step

    async def save_step_result(
        self,
        query_id: str,
        step_id: int,
        sql_query: Optional[str] = None,
        query_result: Optional[Dict[str, Any]] = None,
        worker_result: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """保存步骤执行结果"""
        task = await self.get_task_by_query_id(query_id)
        if not task:
            return False

        # 在所有计划中查找步骤，而不只是当前计划
        all_plans = await self.get_task_plans(query_id)
        step = None

        for plan in all_plans:
            step = await self._get_step_by_step_id(task.id, plan.id, step_id)
            if step:
                break

        if not step:
            return False

        if query_result:
            step.query_result = query_result
        if worker_result:
            step.worker_result = worker_result

        await self.session.commit()

        return True

    async def get_task_steps(
        self, query_id: str, round: Optional[int] = None, status: Optional[StepStatus] = None
    ) -> List[TaskPlanStepModel]:
        """获取任务步骤列表 - 修复多轮计划步骤查询问题"""
        task = await self.get_task_by_query_id(query_id)
        if not task:
            return []

        # 获取规划 - 修复：直接按轮次查询，避免数组索引问题
        if round:
            # 直接按task_id和round查询特定轮次的计划
            result = await self.session.execute(
                select(TaskPlanModel).where(and_(TaskPlanModel.task_id == task.id, TaskPlanModel.round == round))
            )
            plan = result.scalar_one_or_none()
        else:
            plan = await self.get_current_plan(query_id)

        if not plan:
            return []

        query = select(TaskPlanStepModel).where(TaskPlanStepModel.plan_id == plan.id)

        if status:
            query = query.where(TaskPlanStepModel.status == status)

        query = query.order_by(asc(TaskPlanStepModel.step_id))

        result = await self.session.execute(query)
        return list(result.scalars().all())

    # === 任务结果操作 ===

    async def get_task_results(self, query_id: str, result_type: Optional[ResultType] = None) -> List[TaskResultModel]:
        """获取任务结果列表"""
        task = await self.get_task_by_query_id(query_id)
        if not task:
            return []

        query = select(TaskResultModel).where(TaskResultModel.task_id == task.id)

        if result_type:
            query = query.where(TaskResultModel.result_type == result_type)

        query = query.order_by(desc(TaskResultModel.created_at))

        result = await self.session.execute(query)
        return list(result.scalars().all())

    async def get_final_result(self, query_id: str) -> Optional[TaskResultModel]:
        """获取最终结果"""
        results = await self.get_task_results(query_id, ResultType.FINAL)
        return results[0] if results else None

    # === 私有辅助方法 ===

    async def _get_latest_plan(self, task_id: int) -> Optional[TaskPlanModel]:
        """获取最新规划"""
        result = await self.session.execute(
            select(TaskPlanModel).where(TaskPlanModel.task_id == task_id).order_by(desc(TaskPlanModel.round)).limit(1)
        )
        return result.scalar_one_or_none()

    async def _mark_plans_as_old(self, task_id: int):
        """将所有规划标记为非最终轮次"""
        result = await self.session.execute(select(TaskPlanModel).where(TaskPlanModel.task_id == task_id))
        plans = result.scalars().all()

        for plan in plans:
            plan.final_round = False

        await self.session.commit()

    async def _get_step_by_step_id(self, task_id, plan_id, step_id, round) -> Optional[TaskPlanStepModel]:
        """根据step_id获取步骤"""
        result = await self.session.execute(
            select(TaskPlanStepModel).where(
                and_(
                    TaskPlanStepModel.task_id == task_id,
                    TaskPlanStepModel.plan_id == plan_id,
                    TaskPlanStepModel.step_id == step_id,
                    TaskPlanStepModel.round == round,
                )
            )
        )
        return result.scalar_one_or_none()
