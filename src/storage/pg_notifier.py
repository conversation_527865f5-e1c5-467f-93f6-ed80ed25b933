"""PostgreSQL NOTIFY/LISTEN 通知管理器

替代Redis，使用PostgreSQL原生的NOTIFY/LISTEN机制实现实时通知
"""

import asyncio
import json
import logging
import os
from contextlib import asynccontextmanager
from typing import Any, Callable, Dict, List, Optional

import asyncpg
from pydantic import TypeAdapter
from sqlalchemy import text

from src.storage.database import get_session

logger = logging.getLogger(__name__)


class PostgreSQLNotifier:
    """PostgreSQL通知管理器"""

    def __init__(self):
        self._connection: Optional[asyncpg.Connection] = None
        self._listeners: Dict[str, List[Callable]] = {}  # 支持多个消费者
        self._initialized = False
        self._lock = asyncio.Lock()  # 保护并发访问
        self._active_listeners: Dict[str, int] = {}  # 跟踪活跃的监听器计数

    async def initialize(self):
        """初始化连接"""
        if self._initialized:
            return

        try:
            database_url = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5433/qagent")
            self._connection = await asyncpg.connect(database_url)
            self._initialized = True
            logger.info("PostgreSQL Notifier initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL Notifier: {e}")
            raise

    async def close(self):
        """关闭连接"""
        if self._connection:
            await self._connection.close()
        self._initialized = False

    async def notify(self, channel: str, payload_dict: Dict[str, Any]):
        """发送通知"""
        try:
            async with get_session() as session:
                payload_json = TypeAdapter(Dict[str, Any]).dump_json(payload_dict).decode()

                await session.execute(
                    text("SELECT pg_notify(:channel, :payload)"), {"channel": channel, "payload": payload_json}
                )
                await session.commit()
                logger.info(f"Sent notification to {channel}: len={len(payload_json)}")
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            raise

    async def listen(self, channel: str, callback: Callable[[Dict[str, Any]], None]):
        """监听通知"""
        async with self._lock:
            try:
                if not self._connection:
                    await self.initialize()

                # 检查是否已经在监听该channel
                if channel not in self._listeners:
                    await self._connection.add_listener(channel, self._handle_notification)
                    self._listeners[channel] = []
                    self._active_listeners[channel] = 0
                    logger.info(f"Started listening to channel: {channel}")

                # 检查是否已经添加过相同的callback（避免重复添加）
                if callback not in self._listeners[channel]:
                    self._listeners[channel].append(callback)
                    self._active_listeners[channel] += 1
                    logger.info(
                        f"Added callback to channel {channel}, total consumers: {len(self._listeners[channel])}"
                    )
                else:
                    logger.warning(f"Callback already exists for channel {channel}, skipping duplicate")

            except Exception as e:
                logger.error(f"Failed to listen to channel {channel}: {e}")
                raise

    async def unlisten(self, channel: str, callback: Optional[Callable[[Dict[str, Any]], None]] = None):
        """停止监听指定channel"""
        async with self._lock:
            try:
                if not self._connection:
                    return

                # 检查是否在监听该channel
                if channel in self._listeners:
                    callbacks = self._listeners[channel]

                    # 如果指定了callback，只移除特定的callback
                    if callback is not None:
                        if callback in callbacks:
                            callbacks.remove(callback)
                            self._active_listeners[channel] -= 1
                            logger.info(
                                f"Removed specific callback from channel {channel}, remaining consumers: {len(callbacks)}"
                            )

                            # 如果没有消费者了，清理整个channel
                            if len(callbacks) == 0:
                                await self._connection.remove_listener(channel, self._handle_notification)
                                del self._listeners[channel]
                                del self._active_listeners[channel]
                                logger.info(f"Stopped listening to channel: {channel}")
                        else:
                            logger.warning(f"Callback not found in channel {channel}")
                    else:
                        # 如果没有指定callback，移除所有消费者（向后兼容）
                        await self._connection.remove_listener(channel, self._handle_notification)
                        del self._listeners[channel]
                        del self._active_listeners[channel]
                        logger.info(f"Stopped listening to channel: {channel} (all consumers removed)")
            except Exception as e:
                logger.error(f"Failed to unlisten from channel {channel}: {e}")
                raise

    @asynccontextmanager
    async def listen_context(self, channel: str, callback: Callable[[Dict[str, Any]], None]):
        """上下文管理器版本的监听，确保资源正确释放"""
        try:
            await self.listen(channel, callback)
            yield
        finally:
            await self.unlisten(channel, callback)

    def _handle_notification(self, connection, pid, channel, payload):
        """处理通知"""
        try:
            data = json.loads(payload)
            if channel in self._listeners:
                callbacks = self._listeners[channel]
                # 遍历所有回调函数并异步调用
                for callback in callbacks:
                    # 在事件循环中异步调用回调
                    asyncio.create_task(self._async_callback(callback, data))
        except Exception as e:
            logger.error(f"Failed to handle notification: {e}")

    async def _async_callback(self, callback: Callable, data: Dict[str, Any]):
        """异步回调包装器"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(data)
            else:
                callback(data)
        except Exception as e:
            logger.error(f"Error in notification callback: {e}")


# 全局实例
_pg_notifier: Optional[PostgreSQLNotifier] = None


async def get_pg_notifier() -> PostgreSQLNotifier:
    """获取PostgreSQL通知管理器实例"""
    global _pg_notifier
    if _pg_notifier is None:
        _pg_notifier = PostgreSQLNotifier()
        await _pg_notifier.initialize()
    return _pg_notifier


async def init_pg_notifier():
    """初始化PostgreSQL通知管理器"""
    global _pg_notifier
    _pg_notifier = PostgreSQLNotifier()
    await _pg_notifier.initialize()


async def close_pg_notifier():
    """关闭PostgreSQL通知管理器"""
    global _pg_notifier
    if _pg_notifier:
        await _pg_notifier.close()
        _pg_notifier = None
