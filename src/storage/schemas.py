"""
API请求/响应模型
基于Pydantic的数据传输对象
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from .enums import ResultType, StepStatus, TaskStatus


# === 基础响应模型 ===


class BaseResponse(BaseModel):
    """基础响应模型"""

    model_config = ConfigDict(from_attributes=True)

    success: bool = True
    message: str = "操作成功"
    timestamp: datetime = Field(default_factory=datetime.now)


class PaginatedResponse(BaseResponse):
    """分页响应模型"""

    total_count: int
    page: int = 1
    page_size: int = 20
    total_pages: int

    def __init__(self, total_count: int, page: int = 1, page_size: int = 20, **kwargs):
        total_pages = (total_count + page_size - 1) // page_size
        super().__init__(total_count=total_count, page=page, page_size=page_size, total_pages=total_pages, **kwargs)


# === 工作空间相关模型 ===


class WorkspaceCreateRequest(BaseModel):
    """创建工作空间请求"""

    name: str = Field(..., min_length=1, max_length=100, description="工作空间名称")
    description: Optional[str] = Field(None, description="工作空间描述")
    config: Dict[str, Any] = Field(default_factory=dict, description="工作空间配置")


class WorkspaceUpdateRequest(BaseModel):
    """更新工作空间请求"""

    name: Optional[str] = Field(None, min_length=1, max_length=100, description="工作空间名称")
    description: Optional[str] = Field(None, description="工作空间描述")
    config: Optional[Dict[str, Any]] = Field(None, description="工作空间配置")


class WorkspaceResponse(BaseModel):
    """工作空间响应"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: Optional[str]
    config: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


class WorkspaceStatsResponse(BaseModel):
    """工作空间统计响应"""

    workspace_id: int
    workspace_name: str
    total_tasks: int
    status_distribution: Dict[str, int]
    average_execution_time_seconds: Optional[float]
    recent_tasks: List[Dict[str, Any]]
    created_at: str
    updated_at: str


class WorkspaceListResponse(PaginatedResponse):
    """工作空间列表响应"""

    data: List[WorkspaceResponse]


# === 任务相关模型 ===


class TaskCreateRequest(BaseModel):
    """创建任务请求"""

    query_id: str = Field(..., min_length=1, description="调用方指定的查询ID")
    user_query: str = Field(..., min_length=1, description="用户查询内容")
    workspace_id: int = Field(..., gt=0, description="工作空间ID")
    thread_id: Optional[str] = Field(None, description="LangGraph线程ID")
    max_parallel_workers: int = Field(5, ge=1, le=20, description="最大并行工作者数量")
    recursion_limit: int = Field(100, ge=10, le=1000, description="递归限制")
    config: Dict[str, Any] = Field(default_factory=dict, description="任务配置")


class TaskSummaryResponse(BaseModel):
    """任务摘要响应（用于列表显示）"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    query_id: str
    user_query: str
    status: TaskStatus
    created_at: datetime
    updated_at: datetime
    workspace_name: Optional[str] = None


class TaskListResponse(PaginatedResponse):
    """任务列表响应"""

    data: List[TaskSummaryResponse]


class TaskStatusUpdateRequest(BaseModel):
    """任务状态更新请求"""

    status: TaskStatus
    error_message: Optional[str] = None


# === 任务规划相关模型 ===


class TaskPlanResponse(BaseModel):
    """任务规划响应"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    task_id: int
    goal: str
    thinking: Optional[str]
    plan_data: Dict[str, Any]
    round: int
    final_round: bool
    estimated_duration: Optional[int]
    created_at: datetime


class TaskPlanListResponse(BaseResponse):
    """任务规划列表响应"""

    data: List[TaskPlanResponse]


class TaskPlanCreateRequest(BaseModel):
    """创建任务规划请求"""

    goal: str = Field(..., min_length=1, description="规划目标")
    plan_data: Dict[str, Any] = Field(..., description="DAGPlan序列化数据")
    thinking: Optional[str] = Field(None, description="规划思路")
    round: Optional[int] = Field(None, description="规划轮次，不指定则自动递增")


# === 任务步骤相关模型 ===


class TaskStepResponse(BaseModel):
    """任务步骤响应"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    task_id: int
    plan_id: int
    step_id: int
    step_name: str
    depends_on: List[int]
    status: StepStatus
    query_params: Dict[str, Any]
    sql_query: Optional[str]
    query_result: Optional[Dict[str, Any]]
    worker_result: Optional[Dict[str, Any]]
    created_at: datetime


class TaskStepListResponse(BaseResponse):
    """任务步骤列表响应"""

    data: List[TaskStepResponse]


class TaskStepCreateRequest(BaseModel):
    """创建任务步骤请求"""

    step_id: int = Field(..., description="步骤ID")
    step_name: str = Field(..., min_length=1, description="步骤名称")
    depends_on: List[int] = Field(default_factory=list, description="依赖的步骤ID列表")
    query_params: Dict[str, Any] = Field(default_factory=dict, description="查询参数")
    round: Optional[int] = Field(None, description="规划版本")


class TaskStepStatusUpdateRequest(BaseModel):
    """任务步骤状态更新请求"""

    status: StepStatus
    error_message: Optional[str] = None


class TaskStepResultUpdateRequest(BaseModel):
    """任务步骤结果更新请求"""

    sql_query: Optional[str] = None
    query_result: Optional[Dict[str, Any]] = None
    worker_result: Optional[Dict[str, Any]] = None


# === 任务结果相关模型 ===


class TaskResultResponse(BaseModel):
    """任务结果响应"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    task_id: int
    result_type: ResultType
    content: str
    structured_data: Optional[Dict[str, Any]]
    created_at: datetime


class TaskResultListResponse(BaseResponse):
    """任务结果列表响应"""

    data: List[TaskResultResponse]


class TaskResultCreateRequest(BaseModel):
    """创建任务结果请求"""

    content: str = Field(..., min_length=1, description="结果内容")
    result_type: ResultType = Field(ResultType.FINAL, description="结果类型")
    structured_data: Optional[Dict[str, Any]] = Field(None, description="结构化数据")


# === 组合响应模型 ===


# === 错误响应模型 ===


class ErrorResponse(BaseModel):
    """错误响应模型"""

    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.now)


# === 流式响应模型 ===


class StreamEventResponse(BaseModel):
    """流式事件响应"""

    event_type: str
    task_id: int
    query_id: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)


# === 查询参数模型 ===


class TaskListQueryParams(BaseModel):
    """任务列表查询参数"""

    workspace_id: Optional[int] = Field(None, description="工作空间ID过滤")
    status: Optional[TaskStatus] = Field(None, description="状态过滤")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页大小")
    order_by: str = Field("created_at", description="排序字段")
    order_desc: bool = Field(True, description="是否降序排列")


class WorkspaceListQueryParams(BaseModel):
    """工作空间列表查询参数"""

    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页大小")
    order_by: str = Field("created_at", description="排序字段")
    order_desc: bool = Field(True, description="是否降序排列")


class TaskStepQueryParams(BaseModel):
    """任务步骤查询参数"""

    round: Optional[int] = Field(None, description="规划版本")
    status: Optional[StepStatus] = Field(None, description="状态过滤")


class TaskResultQueryParams(BaseModel):
    """任务结果查询参数"""

    result_type: Optional[ResultType] = Field(None, description="结果类型过滤")
