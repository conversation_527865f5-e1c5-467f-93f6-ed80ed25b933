# 开发指南

## 📂 项目结构

```
QFlowAgent/
├── src/                          # 源代码
│   ├── api/                      # FastAPI后端服务
│   │   ├── main.py               # 应用入口
│   │   ├── routes/               # API路由
│   │   ├── services/             # 业务服务
│   │   └── models/               # 数据模型
│   ├── langgraph/                # LangGraph核心
│   │   ├── graph_v2.py           # 图谱编排入口（workflow_v2）
│   │   ├── state.py              # 状态结构
│   │   ├── tasks/                # 任务节点
│   │   ├── zego_tools/           # 工具集
│   │   ├── common/               # 通用组件
│   │   ├── nodes/                # 节点定义
│   │   └── memory/               # 存储工具
│   ├── lark/                     # 飞书集成
│   └── web/                      # Vue前端
├── apps/                         # 应用示例
│   ├── zego_cli.py               # CLI示例
│   ├── zego_lark.py              # 飞书集成
│   └── zego_db.py                # 数据库工具
├── tests/                        # 测试代码
├── scripts/                      # 脚本工具
└── docs/                         # 文档
```

## 🏗️ 核心架构

### LangGraph图谱设计

基于Functional API的智能体编排：

```mermaid
graph TB
    subgraph "LangGraph图谱"
        A[db_health_check] --> B[planner_agent]
        B --> C{Functional调度}
        C -->|并发执行| D[worker_agent]
        C -->|并发执行| E[worker_agent]
        C -->|并发执行| F[worker_agent]
        D --> B
        E --> B
        F --> B
        B -->|全部完成| G[reporter_agent]
    end
```

### 关键组件

1. **planner_agent**: 生成DAGPlan，预取数据，聚合/再规划
2. **worker_agent**: 执行具体分析任务，支持失败重试
3. **reporter_agent**: 生成最终结构化报告
4. **DataCenter**: 统一数据存储与复用
5. **ContextBuilder**: 统一上下文构建

## 🔧 开发环境搭建

### 1. 开发依赖安装

```bash
# 安装开发版本
uv pip install -e .[dev]

# 安装额外的开发工具
uv pip install black isort mypy pytest-asyncio
```

### 2. 代码规范配置

项目使用以下代码规范：

- **格式化**: Black
- **导入排序**: isort  
- **类型检查**: mypy
- **测试框架**: pytest + pytest-asyncio

```bash
# 格式化代码
black src/ tests/

# 排序导入
isort src/ tests/

# 类型检查
mypy src/
```

### 3. 预提交钩子（推荐）

```bash
# 安装pre-commit
pip install pre-commit

# 配置钩子
pre-commit install
```

## 🧪 测试指南

### 测试结构

```
tests/
├── conftest.py                   # 测试配置
├── test_connectivity.py         # 连接性测试
├── src/                          # 单元测试
│   ├── test_sqlgen_full.py      # SQL生成测试
│   └── test_sqlgen_daily.py     # 日级测试
├── data/                         # 测试数据
└── scripts/                      # 测试脚本
```

### 运行测试

```bash
# 运行所有测试
uv run bash tests/runall.sh

# 仅运行SQL生成测试
uv run bash tests/test_sqlgen.sh

# 运行连接性测试
uv run pytest tests/test_connectivity.py -v

# 运行特定测试
uv run pytest tests/test_connectivity.py::test_db_connectivity -v

# 生成覆盖率报告
uv run pytest --cov=src --cov-report=term-missing
```

### 测试环境配置

测试具备智能跳过策略：

- **数据库测试**: 未配置`ZEGO_MYSQL_URL`时自动跳过
- **LLM测试**: 未配置任一LLM Key时自动跳过
- **代理测试**: 自动使用`llm_request_context()`规避代理影响

## 🚀 功能扩展

### 1. 新增指标/维度

在 `src/langgraph/zego_tools/sql_generator/metrics.py` 中定义新指标：

```python
# 定义新指标
@dataclass
class NewMetric:
    name: str = "新指标名称"
    table_type: str = "1min"  # 表类型
    error_field: str = "error_code"  # 错误字段
    # ... 其他配置
```

在 `sql_template.py` 中添加SQL模板：

```python
def generate_new_metric_sql(params: DataQueryParams) -> str:
    """生成新指标的SQL"""
    return f"""
    SELECT 
        {params.time_field} as time,
        {params.dimensions},
        COUNT(*) as total,
        SUM(CASE WHEN {params.success_condition} THEN 1 ELSE 0 END) as success
    FROM {params.table_name}
    WHERE {params.time_condition}
    GROUP BY {params.time_field}, {params.dimensions}
    """
```

### 2. 新增LLM提供方

在 `src/langgraph/common/models/new_llms.py` 中添加配置：

```python
providers_data = {
    # ... 现有配置
    "new_provider": {
        "models": {
            "new-model": {
                "display_name": "新模型",
                "max_tokens": 32768,
                "supports_vision": False
            }
        },
        "base_url_env": "NEW_PROVIDER_BASE_URL",
        "api_key_env": "NEW_PROVIDER_API_KEY"
    }
}
```

### 3. 自定义分析节点

继承worker_task或创建新节点：

```python
async def custom_analysis_task(state: State, config: RunnableConfig) -> State:
    """自定义分析任务"""
    # 获取当前任务
    current_task = state.current_task
    
    # 执行自定义分析逻辑
    result = await perform_custom_analysis(current_task)
    
    # 更新状态
    state.worker_results.append(result)
    return state
```

### 4. 扩展数据源

在 `src/langgraph/zego_tools/ocean_executor/` 中添加新的执行器：

```python
async def execute_new_datasource_async(
    query: str,
    max_retries: int = 3
) -> SqlExecutorQueryResult:
    """新数据源执行器"""
    # 实现数据源连接和查询逻辑
    pass
```

## 🔌 API开发

### 添加新的API端点

1. 在 `src/api/models/` 中定义数据模型：

```python
from pydantic import BaseModel

class NewRequest(BaseModel):
    param1: str
    param2: int

class NewResponse(BaseModel):
    result: str
    status: str
```

2. 在 `src/api/routes/` 中添加路由：

```python
from fastapi import APIRouter

router = APIRouter()

@router.post("/new-endpoint", response_model=NewResponse)
async def new_endpoint(request: NewRequest):
    # 实现业务逻辑
    return NewResponse(result="success", status="ok")
```

3. 在 `src/api/main.py` 中注册路由：

```python
from .routes import new_routes

app.include_router(new_routes.router, prefix="/api/new")
```

### WebSocket支持

添加实时通信功能：

```python
from fastapi import WebSocket

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await websocket.accept()
    try:
        while True:
            data = await websocket.receive_text()
            # 处理数据
            await websocket.send_text(f"Echo: {data}")
    except WebSocketDisconnect:
        print(f"Client {client_id} disconnected")
```

## 🎨 前端开发

### 技术栈

- **框架**: Vue 3 + Composition API
- **UI库**: Element Plus（暗黑模式）
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite

### 开发服务

```bash
cd src/web

# 安装依赖
pnpm install

# 启动开发服务
pnpm run dev

# 构建生产版本
pnpm run build
```

### 添加新页面

1. 在 `src/web/src/views/` 中创建组件
2. 在 `src/web/src/router/` 中添加路由
3. 在 `src/web/src/stores/` 中添加状态管理

## 📊 监控与调试

### 日志配置

```python
import logging
from src.langgraph.common.init_log import setup_logging

# 初始化日志
setup_logging(level=logging.DEBUG)

# 使用日志
logger = logging.getLogger(__name__)
logger.info("调试信息")
```

### 性能监控

```python
import time
from functools import wraps

def timing_decorator(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start = time.time()
        result = await func(*args, **kwargs)
        duration = time.time() - start
        logger.info(f"{func.__name__} took {duration:.2f}s")
        return result
    return wrapper
```

### LangGraph可视化

```bash
# 启动LangGraph Dev进行可视化调试
uv run langgraph dev

# 访问 http://localhost:2024 查看图形界面
```

## 🔄 CI/CD建议

### GitHub Actions示例

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/setup-uv@v4
      - run: uv python install 3.12
      - run: uv pip install -e .[test]
      - run: uv run pytest --cov=src --cov-report=term-missing
      
  sql-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/setup-uv@v4
      - run: uv python install 3.12
      - run: uv pip install -e .[test]
      - run: uv run python -m unittest tests.src.test_sqlgen_full.TestFullSQLGeneration.test
```

### 代码质量检查

```yaml
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: black --check src/ tests/
      - run: isort --check src/ tests/
      - run: mypy src/
```

## 🛠️ 调试技巧

### 1. 断点调试

```python
import pdb; pdb.set_trace()  # 设置断点

# 或使用更现代的
import ipdb; ipdb.set_trace()
```

### 2. 异步调试

```python
import asyncio

async def debug_async_function():
    # 异步调试代码
    result = await some_async_operation()
    print(f"Debug: {result}")

# 在Jupyter中运行
await debug_async_function()
```

### 3. 状态检查

```python
def debug_state(state: State) -> None:
    """调试状态信息"""
    print(f"Messages: {len(state.messages)}")
    print(f"Plans: {len(state.plans)}")
    print(f"Worker Results: {len(state.worker_results)}")
    if state.current_task:
        print(f"Current Task: {state.current_task.id}")
```

## 📝 提交规范

### Commit Message格式

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

类型：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或工具变动

### Pull Request流程

1. Fork项目并创建功能分支
2. 进行开发并添加测试
3. 确保所有测试通过
4. 提交Pull Request
5. 等待代码评审

## 🎯 开发最佳实践

### 1. 代码组织

- 单一职责原则：每个函数/类只做一件事
- 依赖注入：避免硬编码依赖
- 错误处理：使用明确的异常类型
- 类型注解：为所有函数添加类型提示

### 2. 性能优化

- 使用异步编程：`async/await`
- 连接池：数据库和Redis连接复用
- 缓存策略：合理使用内存缓存
- 批量操作：避免N+1查询问题

### 3. 安全考虑

- 输入验证：使用Pydantic验证
- SQL注入防护：使用参数化查询
- 敏感信息：通过环境变量配置
- 访问控制：API端点权限验证

---

*更多架构细节请查看 [API架构文档](README_API.md)*