# QFlowAgent 类型映射报告

## 概述

本报告总结了QFlowAgent项目中LangGraph使用的类型与SQLModel数据模型之间的字段差异。通过对比分析，确保数据在不同层次间的正确传递和转换。

## 类型映射对比表

### 1. 任务规划相关类型

#### DAGPlan (LangGraph) ↔ TaskPlanModel (SQLModel)

| 字段名 | DAGPlan类型 | TaskPlanModel类型 | 映射状态 | 说明 |
|--------|-------------|-------------------|----------|------|
| goal | str | str | ✅ 完全匹配 | 规划目标 |
| thinking | str (可选) | Optional[str] | ✅ 完全匹配 | 规划思路 |
| tasks | list[DAGPlanStep] | - | ❌ 不直接映射 | 存储在plan_data中 |
| round | int | int | ✅ 完全匹配 | 规划轮次 |
| - | - | task_id | ➕ SQLModel独有 | 关联任务ID |
| - | - | plan_data | ➕ SQLModel独有 | DAGPlan序列化数据 |
| - | - | final_round | ➕ SQLModel独有 | 是否为最终轮次 |
| - | - | estimated_duration | ➕ SQLModel独有 | 预估执行时间 |
| - | - | created_at | ➕ SQLModel独有 | 创建时间 |

#### DAGPlanStep (LangGraph) ↔ TaskPlanStepModel (SQLModel)

| 字段名 | DAGPlanStep类型 | TaskPlanStepModel类型 | 映射状态 | 说明 |
|--------|-----------------|-------------------|----------|------|
| id | int | step_id | ✅ 语义匹配 | 任务ID |
| depends_on | list[int] | List[int] | ✅ 完全匹配 | 依赖任务列表 |
| query_params | DataQueryParams | Dict[str, Any] | 🔄 需要序列化 | 查询参数 |
| query_result | Optional[SqlExecutorQueryResult] | Optional[Dict[str, Any]] | 🔄 需要序列化 | 查询结果 |
| status | str | StepStatus | 🔄 枚举转换 | 任务状态 |
| worker_result | Optional[WorkerResult] | Optional[Dict[str, Any]] | 🔄 需要序列化 | 分析结果 |
| - | - | task_id | ➕ SQLModel独有 | 关联任务ID |
| - | - | plan_id | ➕ SQLModel独有 | 关联规划ID |
| - | - | step_name | ➕ SQLModel独有 | 步骤名称 |
| - | - | sql_query | ➕ SQLModel独有 | 生成的SQL |
| - | - | created_at | ➕ SQLModel独有 | 创建时间 |

### 2. 分析结果相关类型

#### WorkerResult (LangGraph) ↔ TaskResultModel (SQLModel)

| 字段名 | WorkerResult类型 | TaskResultModel类型 | 映射状态 | 说明 |
|--------|------------------|---------------------|----------|------|
| objective_evidence | str | - | ❌ 不直接映射 | 存储在content或structured_data中 |
| subjective_thinking | str | - | ❌ 不直接映射 | 存储在content或structured_data中 |
| objective_conclusion | str | - | ❌ 不直接映射 | 存储在content或structured_data中 |
| - | - | task_id | ➕ SQLModel独有 | 关联任务ID |
| - | - | result_type | ➕ SQLModel独有 | 结果类型 |
| - | - | content | ➕ SQLModel独有 | 结果内容文本 |
| - | - | structured_data | ➕ SQLModel独有 | 结构化数据 |
| - | - | created_at | ➕ SQLModel独有 | 创建时间 |

#### TaskResult (LangGraph) ↔ TaskResultModel (SQLModel)

| 字段名 | TaskResult类型 | TaskResultModel类型 | 映射状态 | 说明 |
|--------|-------------------|---------------------|----------|------|
| thinking | str | - | ❌ 不直接映射 | 存储在structured_data中 |
| executive_summary | str | - | ❌ 不直接映射 | 存储在content中 |
| key_findings | list[str] | - | ❌ 不直接映射 | 存储在structured_data中 |
| supporting_evidence | list[str] | - | ❌ 不直接映射 | 存储在structured_data中 |
| analysis_reasoning | str | - | ❌ 不直接映射 | 存储在structured_data中 |
| critical_issues | str | - | ❌ 不直接映射 | 存储在structured_data中 |

### 3. 查询参数相关类型

#### DataQueryParams (LangGraph) ↔ TaskPlanStepModel.query_params (SQLModel)

| 字段名 | DataQueryParams类型 | 存储方式 | 映射状态 | 说明 |
|--------|---------------------|----------|----------|------|
| metric_name | Literal[...] | JSON序列化 | 🔄 需要序列化 | 指标名称 |
| drilldown_dimension | Optional[Literal[...]] | JSON序列化 | 🔄 需要序列化 | 下钻维度 |
| where | Optional[str] | JSON序列化 | 🔄 需要序列化 | WHERE条件 |
| time_start | str | JSON序列化 | 🔄 需要序列化 | 开始时间 |
| time_end | str | JSON序列化 | 🔄 需要序列化 | 结束时间 |
| appid_filter | Optional[int] | JSON序列化 | 🔄 需要序列化 | AppID过滤 |
| country_filter | Optional[str] | JSON序列化 | 🔄 需要序列化 | 国家过滤 |
| query_title | str | JSON序列化 | 🔄 需要序列化 | 查询标题 |

### 4. 执行结果相关类型

#### SqlExecutorQueryResult (LangGraph) ↔ TaskPlanStepModel.query_result (SQLModel)

| 字段名 | SqlExecutorQueryResult类型 | 存储方式 | 映射状态 | 说明 |
|--------|----------------------------|----------|----------|------|
| sql | Optional[str] | JSON序列化 | 🔄 需要序列化 | SQL语句 |
| data | Optional[pd.DataFrame] | JSON序列化 | 🔄 需要序列化 | 查询数据 |
| error_message | Optional[str] | JSON序列化 | 🔄 需要序列化 | 错误信息 |
| error_code | Optional[str] | JSON序列化 | 🔄 需要序列化 | 错误代码 |
| additional_info | Optional[str] | JSON序列化 | 🔄 需要序列化 | 额外信息 |
| error_source | Optional[str] | JSON序列化 | 🔄 需要序列化 | 错误来源 |
| is_successful | bool | JSON序列化 | 🔄 需要序列化 | 是否成功 |

### 5. 任务管理相关类型

#### TaskPlanStepModel (SQLModel) - 无直接LangGraph对应类型

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Optional[int] | 主键 |
| query_id | str | 查询ID |
| thread_id | str | 线程ID |
| workspace_id | int | 工作空间ID |
| user_query | str | 用户查询 |
| status | TaskStatus | 任务状态 |
| max_parallel_workers | int | 最大并行数 |
| recursion_limit | int | 递归限制 |
| config | Dict[str, Any] | 配置信息 |
| error_message | Optional[str] | 错误信息 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### WorkspaceModel (SQLModel) - 无直接LangGraph对应类型

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Optional[int] | 主键 |
| name | str | 工作空间名称 |
| description | Optional[str] | 描述 |
| config | Dict[str, Any] | 配置信息 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

## 映射状态说明

- ✅ **完全匹配**: 字段名称和类型完全一致
- 🔄 **需要序列化**: 类型匹配但需要JSON序列化/反序列化
- ❌ **不直接映射**: 需要特殊处理或组合存储
- ➕ **独有字段**: 某一侧独有的字段

## 数据转换策略

### 1. LangGraph → SQLModel 转换

```python
# DAGPlan → TaskPlanModel
def dag_plan_to_model(plan: DAGPlan, task_id: int) -> TaskPlanModel:
    return TaskPlanModel(
        task_id=task_id,
        goal=plan.goal,
        thinking=plan.thinking,
        plan_data=plan.model_dump(),  # 序列化整个对象
        round=plan.round,
        final_round=True,  # 默认值
    )

# DAGPlanStep → TaskPlanStepModel  
def dag_task_to_step(task: DAGPlanStep, task_id: int, plan_id: int) -> TaskPlanStepModel:
    return TaskPlanStepModel(
        task_id=task_id,
        plan_id=plan_id,
        step_id=task.id,
        step_name=task.query_params.query_title,
        depends_on=task.depends_on,
        status=StepStatus(task.status.lower()),  # 枚举转换
        query_params=task.query_params.model_dump(),
        query_result=task.query_result.model_dump() if task.query_result else None,
        worker_result=task.worker_result.model_dump() if task.worker_result else None
    )
```

### 2. SQLModel → LangGraph 转换

```python
# TaskPlanModel → DAGPlan
def model_to_dag_plan(model: TaskPlanModel) -> DAGPlan:
    plan_data = model.plan_data
    return DAGPlan(
        goal=model.goal,
        thinking=model.thinking,
        tasks=[DAGPlanStep(**task_data) for task_data in plan_data.get('tasks', [])],
        round=model.round
    )

# TaskPlanStepModel → DAGPlanStep
def step_to_dag_task(step: TaskPlanStepModel) -> DAGPlanStep:
    return DAGPlanStep(
        id=step.step_id,
        depends_on=step.depends_on,
        query_params=DataQueryParams(**step.query_params),
        query_result=SqlExecutorQueryResult(**step.query_result) if step.query_result else None,
        status=step.status.value.upper(),  # 枚举转换
        worker_result=WorkerResult(**step.worker_result) if step.worker_result else None
    )
```

## 总结

1. **核心映射关系**：
   - DAGPlan ↔ TaskPlanModel：通过plan_data字段序列化存储
   - DAGPlanStep ↔ TaskPlanStepModel：大部分字段直接映射，需要序列化复杂对象
   - WorkerResult/TaskResult ↔ TaskResultModel：通过content和structured_data存储

2. **主要差异**：
   - SQLModel包含更多元数据字段（时间戳、统计信息等）
   - LangGraph类型更专注于业务逻辑
   - 需要序列化/反序列化处理复杂对象

3. **转换要点**：
   - 使用JSON序列化存储复杂对象
   - 注意枚举类型的转换
   - 保持数据完整性和一致性

---

*生成时间: 2025-08-26*  
*版本: v1.0*
