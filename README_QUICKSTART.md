# 快速开始指南

## 📋 环境要求

- **Python**: 3.12+ （推荐使用 `uv` 管理依赖）
- **Node.js**: 18+ （前端开发需要）
- **数据库**: PostgreSQL（用于 LangGraph Store）+ Redis（实时状态管理）

## 🚀 安装与配置

### 1. 克隆项目并安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd QFlowAgent

# 安装Python依赖（推荐使用uv）
uv pip install -e .[dev]

# 安装前端依赖（如需Web界面）
cd src/web
pnpm install
cd ../..
```

### 2. 配置环境变量

创建 `.env` 文件并配置必要的环境变量：

```bash
# 复制示例配置
cp .env.example .env
```

#### 必需配置（至少配置一个LLM提供方）

```ini
# LLM Provider - 选择至少一个配置
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_API_KEY=your_deepseek_key

ALI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
ALI_API_KEY=your_ali_key

OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_API_KEY=your_openrouter_key

# 其他支持的模型
GLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4/
GLM_API_KEY=your_glm_key

KIMI_BASE_URL=https://api.moonshot.cn/v1
KIMI_API_KEY=your_kimi_key

# 飞书配置（可选，如需飞书集成）
LARK_APP_ID=your_lark_app_id
LARK_APP_SECRET=your_lark_app_secret
```

#### 数据库配置（可选）

```ini
# MySQL/StarRocks配置（用于SQL执行）
ZEGO_MYSQL_URL=your_mysql_host
ZEGO_MYSQL_PORT=9030
ZEGO_MYSQL_DB=your_database
ZEGO_MYSQL_USER=your_username
ZEGO_MYSQL_PASSWORD=your_password

# SOCKS5代理（可选，仅数据库使用）
ZEGO_SOCKS_PROXY=127.0.0.1
ZEGO_SOCKS_PORT=1080
ZEGO_SOCKS_PROXY_USER=
ZEGO_SOCKS_PROXY_PASSWORD=
```

### 3. 初始化数据库（可选）

如果需要持久化存储，初始化PostgreSQL和Redis：

```bash
# 初始化PostgreSQL（LangGraph Store）
bash scripts/init_pg.sh

# 重新初始化（清空数据）
bash scripts/reinit_pg.sh

# 初始化Redis
bash scripts/reinit_redis.sh
```

## 🎮 运行方式

### 1. CLI模式（推荐新手）

最简单的运行方式，直接在命令行中使用：

```bash
# 运行本地CLI示例
uv run python apps/zego_cli.py
```

### 2. LangGraph开发模式

提供可视化调试界面：

```bash
# 启动LangGraph Dev服务
uv run langgraph dev

# 访问 http://localhost:2024 查看图形界面
```

### 3. Web界面模式

启动完整的前后端服务：

```bash
# 启动后端服务
./scripts/run_server.sh

# 新开终端，启动前端服务
./scripts/run_web.sh
```

访问地址：
- **前端**: http://localhost:3000
- **后端API**: http://localhost:2026
- **API文档**: http://localhost:2026/docs

### 4. 飞书集成模式

企业级协作场景：

```bash
# 启动飞书客户端（需先配置LARK_*环境变量）
uv run python apps/zego_lark.py
```

## 🔧 常用命令速查

### 开发命令

```bash
# 运行单元测试
uv run bash tests/test_sqlgen.sh

# 生成SQL测试样例
uv run bash tests/generate_daily_sql.sh

# 运行所有测试
uv run bash tests/runall.sh
```

### 数据库管理

```bash
# 初始化PostgreSQL
bash scripts/init_pg.sh

# 重新初始化PostgreSQL（清空数据）
bash scripts/reinit_pg.sh

# 清理PostgreSQL
bash scripts/delete_pg.sh

# 重新初始化Redis
bash scripts/reinit_redis.sh
```

### 服务管理

```bash
# 启动后端服务（端口2026）
./scripts/run_server.sh

# 启动前端服务（端口3000）
./scripts/run_web.sh

# 停止端口2026上的服务
./scripts/stop_2026.sh
```

## 🎯 快速验证

### 验证安装

```bash
# 检查Python环境
python --version  # 应该显示3.12+

# 检查uv安装
uv --version

# 检查依赖安装
uv run python -c "import src.langgraph; print('安装成功')"
```

### 验证配置

```bash
# 测试数据库连接（如已配置）
uv run pytest tests/test_connectivity.py::test_db_connectivity -v

# 测试LLM连接
uv run pytest tests/test_connectivity.py::test_llm_greeting -v
```

### 第一次运行

```bash
# 运行CLI示例，输入简单问题
uv run python apps/zego_cli.py

# 示例输入："分析推流成功率"
```

## ⚠️ 常见问题

### 1. 依赖安装失败

```bash
# 如果uv安装失败，可以使用pip
pip install -e .[dev]

# 如果前端依赖安装失败
cd src/web
npm install  # 或者 yarn install
```

### 2. 环境变量问题

- 确保至少配置一个LLM提供方的API Key
- 检查 `.env` 文件是否在项目根目录
- 验证API Key格式正确

### 3. 端口冲突

```bash
# 检查端口占用
lsof -i :2026  # 后端端口
lsof -i :3000  # 前端端口
lsof -i :2024  # LangGraph端口

# 杀死占用进程
kill -9 <PID>
```

### 4. 数据库连接问题

- 确保PostgreSQL和Redis服务正在运行
- 检查连接配置是否正确
- 查看服务日志排查具体错误

## 🎓 下一步

- 📖 查看 [开发指南](README_DEVELOPMENT.md) 了解代码结构
- 🏗️ 查看 [API架构文档](README_API.md) 了解技术实现
- 🔧 查看 [故障排查指南](README_TROUBLESHOOTING.md) 解决问题

---

*如有问题，请查看 [故障排查指南](README_TROUBLESHOOTING.md) 或提交 Issue*