### 快速验收测试

```bash
# 健康检查
bash scripts/curl_me_v2.sh health_check

# 完整验收流程
bash scripts/curl_me_v2.sh full_test
```

## 功能分类

### 1. 工作空间管理

```bash
# 列出所有工作空间
bash scripts/curl_me_v2.sh ws_list

# 创建新工作空间
bash scripts/curl_me_v2.sh ws_create

# 获取工作空间详情
bash scripts/curl_me_v2.sh ws_detail 1

# 获取工作空间统计信息
bash scripts/curl_me_v2.sh ws_stats 1

# 确保默认工作空间存在
bash scripts/curl_me_v2.sh ws_ensure_default
```

### 2. 任务管理

```bash
# 创建任务（使用默认工作空间）
bash scripts/curl_me_v2.sh task_create

# 创建任务（指定工作空间）
bash scripts/curl_me_v2.sh task_create_ws 2

# 列出任务
bash scripts/curl_me_v2.sh task_list

# 获取任务详情
bash scripts/curl_me_v2.sh task_detail task_1756111831_69251b5e

# 通过thread_id获取任务
bash scripts/curl_me_v2.sh task_by_thread thread_task_1756111831_69251b5e_1756111831

# 删除任务
bash scripts/curl_me_v2.sh task_delete task_1756111831_69251b5e
```

### 3. 任务规划

```bash
# 获取任务规划列表
bash scripts/curl_me_v2.sh plans task_1756111831_69251b5e

# 获取当前规划
bash scripts/curl_me_v2.sh plans_current task_1756111831_69251b5e
```

### 4. 任务步骤

```bash
# 获取任务步骤列表
bash scripts/curl_me_v2.sh steps task_1756111831_69251b5e

# 获取详细步骤信息
bash scripts/curl_me_v2.sh steps_detail task_1756111831_69251b5e
```

### 5. 任务结果

```bash
# 获取任务结果列表
bash scripts/curl_me_v2.sh results task_1756111831_69251b5e

# 获取最终结果
bash scripts/curl_me_v2.sh results_final task_1756111831_69251b5e
```

## 验收检查点

### 1. 基础功能验收

- [x] API 可访问性检查
- [x] 工作空间管理（创建、查询、统计）
- [x] 任务管理（创建、查询、删除）
- [x] 数据格式正确性

### 2. v2 API 特色功能

- [x] 工作空间多租户支持
- [x] SQLModel 数据模型
- [x] 任务规划、步骤、结果分离存储
- [x] 详细的任务追踪和状态管理

### 3. 向下兼容性

- [x] 前端 v1 API 兼容层
- [x] thread_id 映射
- [x] 状态转换正确性

## 预期输出示例

### 工作空间列表

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "default",
      "description": "默认工作空间 - 用于向下兼容",
      "config": {"is_default": true},
      "created_at": "2025-08-25T16:41:05.296071"
    }
  ]
}
```

### 任务创建

```json
{
  "id": 1,
  "query_id": "task_1756111831_69251b5e",
  "thread_id": "thread_task_1756111831_69251b5e_1756111831",
  "workspace_id": 1,
  "user_query": "v2 API验收测试任务",
  "status": "pending",
  "max_parallel_workers": 3,
  "recursion_limit": 30,
}
```

## 故障排除

### 连接失败

```bash
# 检查服务状态
lsof -i :2026

# 检查API可访问性
curl -s http://localhost:2026/api/v2/workspaces/
```

### jq 解析错误

这些警告通常不影响功能验收，是因为某些响应格式与 jq 期望格式略有差异。可以忽略或通过以下方式安装/更新 jq：

```bash
# macOS
brew install jq

# 或直接查看原始JSON输出
bash scripts/curl_me_v2.sh ws_list | python -m json.tool
```

## 验收标准

1. **所有 API 端点正常响应**（HTTP 200）
2. **数据格式符合预期**（JSON 结构正确）
3. **工作空间功能完整**（创建、查询、统计）
4. **任务生命周期管理正确**（创建、查询、状态追踪）
5. **向下兼容性保持**（前端可正常访问）

## 相关文件

- `scripts/curl_me_v2.sh` - 验收脚本主文件
- `scripts/curl_me.sh` - 原始 v1 API 脚本（参考）
- `.qoder/quests/storage-migration.md` - 存储迁移设计文档
- `src/web/src/api/v2.js` - 前端 v2 API 客户端
- `src/web/src/components/WorkspaceSelector.vue` - 工作空间选择组件

验收脚本确保了 v2 API 的功能完整性和向下兼容性，为前端迁移和后续开发提供了可靠的 API 基础。