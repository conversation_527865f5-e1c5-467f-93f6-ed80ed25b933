#!/bin/bash

# QFlowAgent Web服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 uv
    if ! command -v uv &> /dev/null; then
        log_error "uv 未安装，请先安装 uv"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        log_error "pnpm 未安装，请先安装 pnpm"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 安装前端依赖
install_frontend_deps() {
    log_info "安装前端依赖..."
    cd src/web
    
    if [ ! -d "node_modules" ]; then
        pnpm install
        log_success "前端依赖安装完成"
    else
        log_info "前端依赖已存在，跳过安装"
    fi
    
    cd ../..
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务 (端口: 2026)..."
    uv run uvicorn src.api.main:app --host 0.0.0.0 --port 2026 --reload &
    BACKEND_PID=$!
    echo $BACKEND_PID > .backend.pid
    log_success "后端服务已启动 (PID: $BACKEND_PID)"
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务 (端口: 2026)..."
    cd src/web
    pnpm run dev &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../../.frontend.pid
    cd ../..
    log_success "前端服务已启动 (PID: $FRONTEND_PID)"
}

# 清理函数
cleanup() {
    log_info "正在停止服务..."
    
    if [ -f .frontend.pid ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            log_info "前端服务已停止"
        fi
        rm -f .frontend.pid
    fi
    
    log_success "服务清理完成"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    log_info "启动 QFlowAgent Web 服务..."
    
    # 检查依赖
    check_dependencies
    
    # 安装前端依赖
    install_frontend_deps
    
    # 启动服务
    start_frontend
    
    log_success "单独启动前端"
    log_info "前端地址: http://localhost:3000"
    log_info "API文档: http://localhost:2026/docs (需要后端启动)"
    log_info ""
    log_info "按 Ctrl+C 停止前端服务"
    
    # 等待服务运行
    wait
}

# 运行主函数
main "$@"
