# QFlowAgent 存储架构迁移设计

## 概述

本设计旨在将QFlowAgent从依赖LangGraph内置checkpoint机制迁移到基于SQLModel的自定义存储方案，以支持多租户、高效查询和更好的维护性。

## 项目背景

**当前存储架构问题**:
- 依赖LangGraph的checkpoint机制，缺乏灵活性
- 多租户功能难以实现
- checkpoint_id查找困难，需要遍历所有记录
- LLM类型与存储类型不统一，维护成本高

**迁移目标**:
- 统一LLM类型与SQLModel类型
- 支持多租户数据隔离
- 提供清晰的FastAPI接口
- 保持向下兼容性

## 技术架构

### 现有存储架构分析

```mermaid
graph TB
    subgraph "当前架构"
        LG[LangGraph Executor] --> CP[AsyncPostgresSaver]
        CP --> PG[(PostgreSQL)]
        LG --> ST[AsyncPostgresStore]
        ST --> PG
        API[FastAPI Routes] --> CM[CheckpointManager]
        CM --> CP
    end
    
    subgraph "数据表"
        PG --> CPT[checkpoints表]
        PG --> SIT[store_items表]
    end
```

### 目标存储架构

```mermaid
graph TB
    subgraph "新架构"
        API[FastAPI Routes] --> TS[TaskService]
        TS --> TM[TaskModel]
        TM --> DB[(PostgreSQL)]
        
        LG[LangGraph Executor] --> TA[TaskAdapter]
        TA --> TS
        
        FRONT[Frontend] --> API
    end
    
    subgraph "数据模型"
        DB --> TASK[tasks表]
        DB --> PLAN[task_plans表]
        DB --> STEP[task_steps表]
        DB --> RES[task_results表]
        DB --> WS[workspaces表]
    end
```

## 数据模型设计

### 核心数据模型

#### 工作空间模型
```python
class WorkspaceModel(SQLModel, table=True):
    __tablename__ = "workspaces"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(max_length=100, index=True)
    description: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    # 关系
    tasks: List["TaskModel"] = Relationship(back_populates="workspace")
```

#### 任务模型
```python
class TaskModel(SQLModel, table=True):
    __tablename__ = "tasks"
    
    # 主键字段
    id: Optional[int] = Field(default=None, primary_key=True)
    query_id: str = Field(index=True, unique=True)  # 调用方指定
    thread_id: str = Field(index=True, unique=True)  # 兼容现有系统
    
    # 工作空间
    workspace_id: int = Field(foreign_key="workspaces.id", index=True)
    workspace: WorkspaceModel = Relationship(back_populates="tasks")
    
    # 任务内容
    user_query: str
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    
    # 配置信息
    max_parallel_workers: int = Field(default=5)
    recursion_limit: int = Field(default=100)
    config: Dict[str, Any] = Field(default_factory=dict, sa_type=JSON)
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    
    # 关系
    plans: List["TaskPlanModel"] = Relationship(back_populates="task")
    steps: List["TaskStepModel"] = Relationship(back_populates="task")
    results: List["TaskResultModel"] = Relationship(back_populates="task")
```

#### 任务规划模型
```python
class TaskPlanModel(SQLModel, table=True):
    __tablename__ = "task_plans"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    task_id: int = Field(foreign_key="tasks.id", index=True)
    
    # 规划内容
    goal: str
    thinking: Optional[str] = None
    plan_data: Dict[str, Any] = Field(sa_type=JSON)  # DAGPlan序列化
    
    # 版本控制
    version: int = Field(default=1)
    is_current: bool = Field(default=True, index=True)
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now)
    
    # 关系
    task: TaskModel = Relationship(back_populates="plans")
    steps: List["TaskStepModel"] = Relationship(back_populates="plan")
```

#### 任务步骤模型
```python
class TaskStepModel(SQLModel, table=True):
    __tablename__ = "task_steps"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    task_id: int = Field(foreign_key="tasks.id", index=True)
    plan_id: int = Field(foreign_key="task_plans.id", index=True)
    
    # 步骤信息
    step_id: int  # DAGPlanTask.id
    depends_on: List[int] = Field(default_factory=list, sa_type=JSON)
    status: StepStatus = Field(default=StepStatus.PENDING)
    
    # 查询参数
    query_params: Dict[str, Any] = Field(sa_type=JSON)
    
    # 执行结果
    query_result: Optional[Dict[str, Any]] = Field(default=None, sa_type=JSON)
    worker_result: Optional[Dict[str, Any]] = Field(default=None, sa_type=JSON)
    error_message: Optional[str] = None
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 关系
    task: TaskModel = Relationship(back_populates="steps")
    plan: TaskPlanModel = Relationship(back_populates="steps")
```

#### 任务结果模型
```python
class TaskResultModel(SQLModel, table=True):
    __tablename__ = "task_results"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    task_id: int = Field(foreign_key="tasks.id", index=True)
    
    # 结果内容
    result_type: ResultType = Field(default=ResultType.FINAL)
    content: str
    structured_data: Optional[Dict[str, Any]] = Field(default=None, sa_type=JSON)
    
    # 元数据
    token_usage: Optional[Dict[str, int]] = Field(default=None, sa_type=JSON)
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now)
    
    # 关系
    task: TaskModel = Relationship(back_populates="results")
```

### 枚举类型定义

```python
class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class StepStatus(str, Enum):
    PENDING = "pending"
    READY = "ready"
    RUNNING = "running"
    DONE = "done"
    FAILED = "failed"

class ResultType(str, Enum):
    INTERMEDIATE = "intermediate"
    FINAL = "final"
    ERROR = "error"
```

## FastAPI接口设计

### 任务管理接口

#### 创建任务
```http
POST /api/v2/tasks
Content-Type: application/json

{
  "query_id": "user_specified_id",
  "user_query": "分析推流成功率趋势",
  "workspace_id": 1,
  "max_parallel_workers": 5,
  "recursion_limit": 100,
  "config": {}
}
```

#### 获取任务规划
```http
GET /api/v2/tasks/{query_id}/plans
GET /api/v2/tasks/{query_id}/plans?version=2  # 指定版本

Response:
{
  "plans": [
    {
      "id": 1,
      "goal": "分析推流成功率趋势",
      "thinking": "需要查询推流数据...",
      "version": 1,
      "is_current": true,
      "plan_data": {...},
      "created_at": "2024-01-01T00:00:00"
    }
  ]
}
```

#### 获取任务步骤
```http
GET /api/v2/tasks/{query_id}/steps
GET /api/v2/tasks/{query_id}/steps?plan_version=1

Response:
{
  "steps": [
    {
      "id": 1,
      "step_id": 1,
      "status": "completed",
      "depends_on": [],
      "query_params": {...},
      "query_result": {...},
      "worker_result": {...},
      "created_at": "2024-01-01T00:00:00",
      "completed_at": "2024-01-01T00:01:00"
    }
  ]
}
```

#### 获取任务结论
```http
GET /api/v2/tasks/{query_id}/results

Response:
{
  "results": [
    {
      "id": 1,
      "result_type": "final",
      "content": "分析结论：推流成功率呈上升趋势...",
      "structured_data": {...},
      "token_usage": {"input": 1000, "output": 500},
      "created_at": "2024-01-01T00:02:00"
    }
  ]
}
```

### 工作空间接口

```http
POST /api/v2/workspaces
GET /api/v2/workspaces
GET /api/v2/workspaces/{workspace_id}
PUT /api/v2/workspaces/{workspace_id}
DELETE /api/v2/workspaces/{workspace_id}
```

## 服务层设计

### TaskService核心方法

```python
class TaskService:
    def __init__(self, db: AsyncSession, redis: RedisWrapper):
        self.db = db
        self.redis = redis
    
    async def create_task(self, request: TaskCreateRequest) -> TaskModel
    async def get_task_by_query_id(self, query_id: str) -> Optional[TaskModel]
    async def get_task_plans(self, query_id: str, version: Optional[int] = None) -> List[TaskPlanModel]
    async def get_task_steps(self, query_id: str, plan_version: Optional[int] = None) -> List[TaskStepModel]
    async def get_task_results(self, query_id: str) -> List[TaskResultModel]
    async def update_task_status(self, query_id: str, status: TaskStatus) -> bool
    async def save_plan(self, query_id: str, plan: DAGPlan) -> TaskPlanModel
    async def save_step_result(self, query_id: str, step_id: int, result: Dict) -> TaskStepModel
    async def save_final_result(self, query_id: str, content: str, data: Dict) -> TaskResultModel
```

### LangGraph适配器

```python
class TaskAdapter:
    """适配LangGraph执行与新存储模型"""
    
    async def execute_with_storage(self, query_id: str, input_data: Dict) -> str:
        # 1. 从数据库加载任务
        task = await self.task_service.get_task_by_query_id(query_id)
        
        # 2. 执行LangGraph工作流
        async for event in self.graph.astream_events(...):
            if event["event"] == "on_chain_start":
                await self._handle_chain_start(task, event)
            elif event["event"] == "on_chain_end":
                await self._handle_chain_end(task, event)
        
        # 3. 保存最终结果
        return await self._save_final_result(task)
    
    async def _handle_chain_start(self, task: TaskModel, event: Dict):
        if event["name"] == "planner_task":
            await self.task_service.update_task_status(task.query_id, TaskStatus.RUNNING)
    
    async def _handle_chain_end(self, task: TaskModel, event: Dict):
        if event["name"] == "planner_task" and "plan" in event["data"]:
            plan = event["data"]["plan"]
            await self.task_service.save_plan(task.query_id, plan)
        elif event["name"] == "worker_task":
            step_result = event["data"]
            await self.task_service.save_step_result(task.query_id, step_result["id"], step_result)
```

## 迁移策略

### 阶段一：并行运行
- 保持现有LangGraph checkpoint机制
- 新增SQLModel数据模型和接口
- 实现数据同步机制

### 阶段二：接口切换
- 前端逐步切换到新API
- 保持v1接口向下兼容
- 完成数据迁移验证

### 阶段三：清理优化
- 移除旧的checkpoint依赖
- 优化数据库结构
- 性能调优

### 数据迁移脚本

```python
async def migrate_checkpoint_to_sqlmodel():
    """迁移现有checkpoint数据到新模型"""
    async with get_store_and_checkpointer() as (store, checkpointer):
        # 1. 遍历所有checkpoint
        all_checkpoints = await checkpointer.alist({})
        
        # 2. 按thread_id分组
        tasks_data = {}
        for checkpoint in all_checkpoints:
            thread_id = checkpoint.config["configurable"]["thread_id"]
            if thread_id not in tasks_data:
                tasks_data[thread_id] = []
            tasks_data[thread_id].append(checkpoint)
        
        # 3. 转换为新模型
        for thread_id, checkpoints in tasks_data.items():
            await migrate_single_task(thread_id, checkpoints)

async def migrate_single_task(thread_id: str, checkpoints: List):
    # 解析任务基本信息
    latest = max(checkpoints, key=lambda x: x.metadata["step"])
    
    # 创建TaskModel
    task = TaskModel(
        query_id=f"migrated_{thread_id}",
        thread_id=thread_id,
        workspace_id=1,  # 默认工作空间
        user_query=extract_user_query(latest),
        status=extract_status(latest)
    )
    
    # 保存并创建关联数据
    await task_service.create_task(task)
    await migrate_plans_and_steps(task, checkpoints)
```

## 前端适配

### 状态管理更新

```javascript
// stores/taskStore.js
export const useTaskStore = defineStore('task', {
  state: () => ({
    currentTask: null,
    taskPlans: [],
    taskSteps: [],
    taskResults: []
  }),
  
  actions: {
    async createTask(taskData) {
      const response = await api.post('/api/v2/tasks', taskData)
      this.currentTask = response.data
      return response.data
    },
    
    async loadTaskPlans(queryId) {
      const response = await api.get(`/api/v2/tasks/${queryId}/plans`)
      this.taskPlans = response.data.plans
    },
    
    async loadTaskSteps(queryId) {
      const response = await api.get(`/api/v2/tasks/${queryId}/steps`)
      this.taskSteps = response.data.steps
    },
    
    async loadTaskResults(queryId) {
      const response = await api.get(`/api/v2/tasks/${queryId}/results`)
      this.taskResults = response.data.results
    }
  }
})
```

### 组件更新

```vue
<template>
  <div class="task-detail">
    <!-- 任务基本信息 -->
    <TaskHeader :task="currentTask" />
    
    <!-- 规划信息 -->
    <TaskPlans :plans="taskPlans" />
    
    <!-- 执行步骤 -->
    <TaskSteps :steps="taskSteps" />
    
    <!-- 分析结果 -->
    <TaskResults :results="taskResults" />
  </div>
</template>

<script setup>
const route = useRoute()
const taskStore = useTaskStore()
const { queryId } = route.params

onMounted(async () => {
  await taskStore.loadTaskPlans(queryId)
  await taskStore.loadTaskSteps(queryId)
  await taskStore.loadTaskResults(queryId)
})
</script>
```

## 性能优化

### 数据库优化
- 合理的索引设计
- 分页查询优化
- 连接池配置

### 缓存策略
```python
@lru_cache(maxsize=1000)
async def get_task_with_cache(query_id: str) -> TaskModel:
    return await task_service.get_task_by_query_id(query_id)

# Redis缓存热点数据
async def cache_task_summary(query_id: str, data: Dict):
    await redis.setex(f"task_summary:{query_id}", 3600, json.dumps(data))
```

### 查询优化
```python
# 使用selectinload避免N+1查询
async def get_task_with_relations(query_id: str):
    return await session.exec(
        select(TaskModel)
        .options(
            selectinload(TaskModel.plans),
            selectinload(TaskModel.steps),
            selectinload(TaskModel.results)
        )
        .where(TaskModel.query_id == query_id)
    ).first()
```

## 测试策略

### 单元测试
```python
@pytest.mark.asyncio
async def test_create_task():
    request = TaskCreateRequest(
        query_id="test_123",
        user_query="测试查询",
        workspace_id=1
    )
    
    task = await task_service.create_task(request)
    assert task.query_id == "test_123"
    assert task.status == TaskStatus.PENDING

@pytest.mark.asyncio 
async def test_task_execution():
    # 模拟完整任务执行流程
    task = await create_test_task()
    
    # 测试规划保存
    plan = create_test_plan()
    saved_plan = await task_service.save_plan(task.query_id, plan)
    assert saved_plan.is_current == True
    
    # 测试步骤执行
    for step in plan.tasks:
        result = await simulate_step_execution(step)
        await task_service.save_step_result(task.query_id, step.id, result)
```

### 集成测试
```python
@pytest.mark.asyncio
async def test_api_integration():
    # 测试完整API流程
    async with AsyncClient(app=app, base_url="http://test") as client:
        # 创建任务
        response = await client.post("/api/v2/tasks", json=task_data)
        assert response.status_code == 201
        
        query_id = response.json()["query_id"]
        
        # 检查规划
        plans_response = await client.get(f"/api/v2/tasks/{query_id}/plans")
        assert plans_response.status_code == 200
```

## 风险评估与应对

### 主要风险
1. **数据一致性风险**: 迁移过程中新旧系统数据不一致
2. **性能风险**: 新架构可能影响查询性能
3. **兼容性风险**: 现有功能可能受到影响

### 应对措施
1. **分阶段迁移**: 逐步切换，保持向下兼容
2. **数据校验**: 实施严格的数据验证机制
3. **回滚方案**: 准备快速回滚到原架构
4. **监控告警**: 实时监控系统性能和错误率

## 结论

本设计提供了从LangGraph checkpoint到SQLModel自定义存储的完整迁移方案，通过统一数据模型、清晰的API设计和渐进式迁移策略，既保证了系统的向下兼容性，又为未来的多租户和功能扩展奠定了基础。

关键优势：
- 统一的类型系统，降低维护成本
- 支持多租户数据隔离
- 高效的查询和索引设计
- 清晰的API接口
- 渐进式迁移，降低风险