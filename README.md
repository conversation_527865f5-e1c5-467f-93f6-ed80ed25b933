### QFlowAgent

**基于 LangGraph 的数据分析智能体** - 自动规划 → 并发分析 → 智能汇报

## 🎯 30秒了解

- **自动化分析流水线**：输入问题 → AI规划任务 → 并发执行 → 生成报告
- **数据查询解耦**：SQL生成/执行与LLM分析分离，避免重复查询
- **多LLM支持**：支持DeepSeek、阿里云、OpenRouter等多家模型
- **可视化界面**：Web控制台 + 飞书卡片 + 本地CLI

## ⚡ 3分钟上手

```bash
# 1. 安装依赖
uv pip install -e .[dev]

# 2. 配置环境变量（复制.env.example并填写）
cp .env.example .env

# 3. 运行示例
uv run python apps/zego_cli.py
```

## 📚 完整文档

| 文档 | 内容 | 适合人群 |
|------|------|----------|
| [快速开始](README_QUICKSTART.md) | 环境搭建、配置、运行方式 | 新用户 |
| [开发指南](README_DEVELOPMENT.md) | 代码结构、扩展开发、测试 | 开发者 |
| [API架构](README_API.md) | 接口文档、系统架构、技术实现 | 架构师 |
| [故障排查](README_TROUBLESHOOTING.md) | 常见问题、调试方法 | 运维人员 |

## 🚀 核心特性

### 智能分析流水线
```mermaid
graph LR
    A[用户问题] --> B[AI规划]
    B --> C[并发分析]
    C --> D[智能汇报]
    E[数据中心] --> C
    F[经验库] --> B
```

### 多种运行方式
- **CLI模式**：`uv run python apps/zego_cli.py` - 本地命令行
- **Web界面**：启动后端+前端服务 - 可视化操作
- **飞书集成**：`uv run python apps/zego_lark.py` - 企业协作
- **LangGraph Dev**：`uv run langgraph dev` - 开发调试

## 🏗️ 技术栈

- **后端**：FastAPI + LangGraph + AsyncPostgresStore
- **前端**：Vue 3 + Element Plus（暗黑模式）
- **数据库**：PostgreSQL（持久化） + Redis（实时状态）
- **LLM**：支持DeepSeek、阿里云、OpenRouter、GLM、Kimi等
- **部署**：支持本地开发、Docker容器化

## 🤝 贡献

欢迎提交Issue和Pull Request！详见[开发指南](README_DEVELOPMENT.md)

## 📄 许可证

[MIT License](LICENSE)

---

*更多技术细节请查看 [API架构文档](README_API.md)*
