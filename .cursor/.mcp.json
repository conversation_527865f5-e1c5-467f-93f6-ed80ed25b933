{"mcpServers": {"context7": {"command": "pnpx", "args": ["@upstash/context7-mcp@latest"]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "env": {"MCP_DESKTOP_MODE": "true", "MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false"}}, "code-reasoning": {"command": "pnpx", "args": ["@mettamatt/code-reasoning"]}, "Sequential thinking": {"command": "pnpx", "args": ["@modelcontextprotocol/server-sequential-thinking"]}, "Playwright": {"command": "pnpx", "args": ["@playwright/mcp@latest"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}}}}