# 故障排查指南

## 🚨 快速诊断

### 健康检查命令

```bash
# 检查整体状态
curl http://localhost:2026/api/health/

# 检查Redis连接
curl http://localhost:2026/api/health/redis

# 检查PostgreSQL连接  
curl http://localhost:2026/api/health/postgres

# 检查任务系统状态
curl http://localhost:2026/api/health/tasks
```

### 系统状态检查

```bash
# 检查端口占用
lsof -i :2026  # 后端端口
lsof -i :3000  # 前端端口
lsof -i :2024  # LangGraph端口

# 检查进程
ps aux | grep python
ps aux | grep node

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

## 🔧 环境问题

### 1. Python环境问题

#### 问题：Python版本不匹配
```
Error: This project requires Python 3.12+
```

**解决方案**：
```bash
# 检查Python版本
python --version

# 安装Python 3.12（macOS）
brew install python@3.12

# 使用pyenv管理版本
pyenv install 3.12.0
pyenv local 3.12.0
```

#### 问题：uv安装失败
```
Command 'uv' not found
```

**解决方案**：
```bash
# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用pip安装
pip install uv

# 或使用brew安装（macOS）
brew install uv
```

#### 问题：依赖安装失败
```
Failed to install dependencies
```

**解决方案**：
```bash
# 清理缓存重新安装
uv cache clean
uv pip install -e .[dev] --force-reinstall

# 使用pip作为备选
pip install -e .[dev]

# 检查requirements冲突
uv pip check
```

### 2. 环境变量问题

#### 问题：LLM配置错误
```
EnvironmentError: 至少需要配置一个LLM提供方
```

**解决方案**：
```bash
# 检查.env文件是否存在
ls -la .env

# 检查环境变量是否加载
echo $DEEPSEEK_API_KEY

# 验证API Key格式
curl -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
     https://api.deepseek.com/v1/models
```

#### 问题：环境变量未加载
```
KeyError: 'DEEPSEEK_API_KEY'
```

**解决方案**：
```bash
# 手动加载环境变量
export $(cat .env | xargs)

# 或在代码中加载
python -c "from dotenv import load_dotenv; load_dotenv(); import os; print(os.getenv('DEEPSEEK_API_KEY'))"

# 检查.env文件格式
cat .env | head -5
```

### 3. 端口冲突问题

#### 问题：端口被占用
```
OSError: [Errno 48] Address already in use
```

**解决方案**：
```bash
# 查找占用进程
lsof -i :2026

# 杀死占用进程
kill -9 <PID>

# 或使用脚本停止
./scripts/stop_2026.sh

# 使用不同端口启动
uvicorn src.api.main:app --port 2027
```

## 🗄️ 数据库问题

### 1. PostgreSQL问题

#### 问题：PostgreSQL连接失败
```
psycopg2.OperationalError: could not connect to server
```

**解决方案**：
```bash
# 检查PostgreSQL服务状态
brew services list | grep postgresql
# 或
systemctl status postgresql

# 启动PostgreSQL服务
brew services start postgresql
# 或
sudo systemctl start postgresql

# 检查连接
psql -h localhost -U postgres -d qflow_agent

# 重新初始化数据库
bash scripts/reinit_pg.sh
```

#### 问题：数据库不存在
```
psycopg2.OperationalError: database "qflow_agent" does not exist
```

**解决方案**：
```bash
# 创建数据库
createdb qflow_agent

# 或使用脚本初始化
bash scripts/init_pg.sh

# 手动创建
psql -c "CREATE DATABASE qflow_agent;"
```

#### 问题：权限不足
```
psycopg2.OperationalError: permission denied for database
```

**解决方案**：
```bash
# 修改用户权限
psql -c "ALTER USER postgres CREATEDB;"
psql -c "GRANT ALL PRIVILEGES ON DATABASE qflow_agent TO postgres;"

# 或使用超级用户
sudo -u postgres psql
```

### 2. Redis问题

#### 问题：Redis连接失败
```
redis.exceptions.ConnectionError: Error connecting to Redis
```

**解决方案**：
```bash
# 检查Redis服务状态
brew services list | grep redis
# 或
systemctl status redis

# 启动Redis服务
brew services start redis
# 或
sudo systemctl start redis

# 检查连接
redis-cli ping

# 重新初始化Redis
bash scripts/reinit_redis.sh
```

#### 问题：Redis内存不足
```
redis.exceptions.ResponseError: OOM command not allowed
```

**解决方案**：
```bash
# 检查内存使用
redis-cli info memory

# 清理数据
redis-cli flushall

# 配置内存限制
redis-cli config set maxmemory 2gb
redis-cli config set maxmemory-policy allkeys-lru
```

## 🤖 LLM服务问题

### 1. API请求失败

#### 问题：API密钥无效
```
AuthenticationError: Invalid API key
```

**解决方案**：
```bash
# 验证API密钥格式
echo $DEEPSEEK_API_KEY | wc -c  # 检查长度

# 测试API连接
curl -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
     https://api.deepseek.com/v1/models

# 重新获取API密钥
# 访问对应厂商官网重新申请
```

#### 问题：请求频率限制
```
RateLimitError: Rate limit exceeded
```

**解决方案**：
```bash
# 检查配置的并发数
grep max_parallel_workers .env

# 降低并发数
export MAX_PARALLEL_WORKERS=2

# 增加重试延迟
export BASE_DELAY=2.0

# 使用不同的API密钥轮换
```

#### 问题：网络连接超时
```
RequestTimeoutError: Request timed out
```

**解决方案**：
```bash
# 检查网络连接
ping api.deepseek.com

# 检查代理设置
echo $HTTP_PROXY
echo $HTTPS_PROXY

# 临时禁用代理
unset HTTP_PROXY HTTPS_PROXY

# 增加超时时间
export REQUEST_TIMEOUT=60
```

### 2. 模型相关问题

#### 问题：模型不存在
```
NotFoundError: The model does not exist
```

**解决方案**：
```python
# 检查可用模型
curl -H "Authorization: Bearer $API_KEY" \
     https://api.provider.com/v1/models

# 更新模型配置
# 在src/langgraph/common/models/new_llms.py中更新
```

#### 问题：Token限制超出
```
InvalidRequestError: This model's maximum context length is 32768 tokens
```

**解决方案**：
```python
# 检查输入长度
print(f"Input tokens: {len(messages)}")

# 截断输入
messages = messages[-10:]  # 只保留最后10条消息

# 使用更大上下文的模型
# 如：moonshot-v1-128k 替代 moonshot-v1-32k
```

## 🕸️ 网络问题

### 1. 代理配置问题

#### 问题：代理冲突
```
ProxyError: Cannot connect to proxy
```

**解决方案**：
```bash
# 检查代理配置
echo $HTTP_PROXY
echo $SOCKS_PROXY

# 临时禁用代理
unset HTTP_PROXY HTTPS_PROXY ALL_PROXY

# 检查SOCKS代理
nc -zv 127.0.0.1 1080

# 分离LLM和数据库代理
# LLM请求会自动使用llm_request_context()禁用代理
```

#### 问题：DNS解析失败
```
gaierror: [Errno -2] Name or service not known
```

**解决方案**：
```bash
# 检查DNS配置
nslookup api.deepseek.com

# 使用公共DNS
echo "nameserver *******" | sudo tee -a /etc/resolv.conf

# 检查hosts文件
cat /etc/hosts
```

### 2. SSL证书问题

#### 问题：SSL证书验证失败
```
SSLError: certificate verify failed
```

**解决方案**：
```bash
# 更新证书
# macOS
brew install ca-certificates
# Ubuntu
sudo apt-get update && sudo apt-get install ca-certificates

# 临时禁用SSL验证（不推荐）
export PYTHONHTTPSVERIFY=0

# 使用自定义证书
export REQUESTS_CA_BUNDLE=/path/to/cacert.pem
```

## 🎮 运行时问题

### 1. 任务执行问题

#### 问题：任务卡死不动
```
Task appears to be stuck in RUNNING state
```

**解决方案**：
```bash
# 检查任务状态
curl http://localhost:2026/api/tasks/{thread_id}

# 检查Redis中的状态
redis-cli hget task:{thread_id} status

# 强制重置任务状态
redis-cli hset task:{thread_id} status "failed"

# 检查worker进程
ps aux | grep worker
```

#### 问题：内存泄漏
```
MemoryError: Unable to allocate memory
```

**解决方案**：
```bash
# 检查内存使用
top -o %MEM

# 检查Python进程内存
ps -eo pid,ppid,cmd,%mem,%cpu --sort=-%mem | head

# 重启服务
./scripts/stop_2026.sh
./scripts/run_server.sh

# 配置内存限制
ulimit -v 2097152  # 2GB
```

#### 问题：任务结果丢失
```
KeyError: 'reporter_result' not found in state
```

**解决方案**：
```bash
# 检查PostgreSQL checkpoint
psql -d qflow_agent -c "SELECT * FROM checkpoints WHERE thread_id='xxx' ORDER BY created_at DESC LIMIT 5;"

# 检查Redis状态
redis-cli hgetall task:{thread_id}

# 手动触发checkpoint保存
curl -X POST http://localhost:2026/api/tasks/{thread_id}/checkpoint
```

### 2. 性能问题

#### 问题：响应慢
```
Request takes too long to respond
```

**解决方案**：
```bash
# 检查数据库连接池
curl http://localhost:2026/api/health/postgres

# 检查Redis延迟
redis-cli --latency

# 减少并发数
export MAX_PARALLEL_WORKERS=2

# 启用数据库查询缓存
export ENABLE_QUERY_CACHE=true

# 分析慢查询
tail -f /var/log/postgresql/postgresql.log | grep "slow query"
```

#### 问题：CPU使用率高
```
High CPU usage detected
```

**解决方案**：
```bash
# 检查进程CPU使用
top -o %CPU

# 检查是否有死循环
pstack <pid>

# 降低worker数量
export MAX_PARALLEL_WORKERS=1

# 增加任务间隔
export WORKER_DELAY=1.0
```

## 🧪 测试问题

### 1. 单元测试失败

#### 问题：测试环境配置错误
```
ImportError: No module named 'src'
```

**解决方案**：
```bash
# 检查PYTHONPATH
echo $PYTHONPATH

# 安装开发模式
uv pip install -e .[dev]

# 从项目根目录运行测试
cd /path/to/QFlowAgent
uv run pytest tests/
```

#### 问题：测试数据库连接失败
```
Tests skip due to missing database configuration
```

**解决方案**：
```bash
# 配置测试数据库
export ZEGO_MYSQL_URL=test_host
export ZEGO_MYSQL_USER=test_user

# 或跳过数据库测试
uv run pytest -m "not database"

# 使用Mock数据库
export USE_MOCK_DB=true
```

### 2. 集成测试问题

#### 问题：API测试失败
```
ConnectionError: Failed to connect to API server
```

**解决方案**：
```bash
# 启动测试服务器
./scripts/run_server.sh

# 检查服务是否启动
curl http://localhost:2026/api/health/

# 使用测试端口
export TEST_API_PORT=2027
uvicorn src.api.main:app --port 2027 &

# 等待服务启动
sleep 5 && curl http://localhost:2027/api/health/
```

## 📊 日志分析

### 1. 查看日志

```bash
# API服务日志
tail -f logs/api.log

# LangGraph执行日志
tail -f logs/langgraph.log

# 错误日志
tail -f logs/error.log

# 系统日志
sudo tail -f /var/log/syslog | grep qflow
```

### 2. 日志级别调整

```bash
# 设置调试级别
export LOG_LEVEL=DEBUG

# 启用详细SQL日志
export SQL_ECHO=true

# 启用LLM请求日志
export LLM_DEBUG=true
```

### 3. 常见错误模式

#### Token使用超限
```
INFO - Token usage: input=12543, output=2847, total=15390
ERROR - Token limit exceeded: 15390 > 128000
```

#### SQL执行失败
```
ERROR - SQL execution failed: (1146, "Table 'xxx' doesn't exist")
INFO - Retrying with exponential backoff: attempt 2/3
```

#### LLM请求失败
```
ERROR - LLM request failed: RateLimitError
INFO - Switching to backup provider: ali -> deepseek
```

## 🔄 恢复操作

### 1. 服务重启

```bash
# 停止所有服务
./scripts/stop_2026.sh
pkill -f "uvicorn"
pkill -f "langgraph"

# 清理临时文件
rm -rf logs/*.log
rm -rf tmp/*

# 重启服务
./scripts/run_server.sh
```

### 2. 数据恢复

```bash
# 恢复PostgreSQL数据
pg_restore -d qflow_agent backup.dump

# 清理Redis缓存
redis-cli flushall

# 重建索引
psql -d qflow_agent -c "REINDEX DATABASE qflow_agent;"
```

### 3. 配置重置

```bash
# 备份当前配置
cp .env .env.backup

# 恢复默认配置
cp .env.example .env

# 重新配置必要变量
vim .env
```

## 📞 获取帮助

### 1. 自助诊断工具

```bash
# 运行健康检查脚本
./scripts/health_check.sh

# 收集系统信息
./scripts/collect_info.sh > system_info.txt
```

### 2. 社区支持

- **GitHub Issues**: 提交bug报告和功能请求
- **开发文档**: 查看[开发指南](README_DEVELOPMENT.md)
- **API文档**: 查看[API架构文档](README_API.md)

### 3. 紧急恢复

如果系统完全无法启动：

```bash
# 1. 完全重置环境
rm -rf venv/ __pycache__/ .pytest_cache/
uv pip install -e .[dev] --force-reinstall

# 2. 重置数据库
bash scripts/delete_pg.sh
bash scripts/init_pg.sh

# 3. 清理Redis
redis-cli flushall

# 4. 使用最小配置启动
export MINIMAL_MODE=true
uv run python apps/zego_cli.py
```

---

*如果以上方法仍无法解决问题，请提交详细的错误日志到GitHub Issues*