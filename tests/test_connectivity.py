import importlib.util
import os

import pytest


@pytest.mark.asyncio
async def test_db_connectivity():
    """Basic DB connectivity test using async path."""
    from src.langgraph.zego_tools.ocean_executor import ocean_connection

    # Skip if DB envs are not configured
    if not os.getenv("ZEGO_MYSQL_URL"):
        pytest.skip("DB env not configured")

    ok = await ocean_connection.test_connection()
    assert ok is True


@pytest.mark.asyncio
async def test_llm_greeting():
    """Basic LLM greeting check."""
    # Skip cleanly if langchain is not installed
    if importlib.util.find_spec("langchain") is None or importlib.util.find_spec("langchain_core") is None:
        pytest.skip("langchain not installed")

    # Some environments may lack LLM credentials; allow opt-out
    required_envs = [
        "ALI_API_KEY",
        "DEEPSEEK_API_KEY",
        "KIMI_API_KEY",
        "GLM_API_KEY",
        "OPENROUTER_API_KEY",
    ]
    if not any(os.getenv(k) for k in required_envs):
        pytest.skip("LLM env not configured")

    from langchain_core.messages import HumanMessage  # late import after skip checks
    from src.langgraph.common.models.new_llms import get_default_llm as get_llm
    from src.langgraph.common.utils.llm_request_utils import llm_request_context

    async with llm_request_context():
        resp = await get_llm().ainvoke([HumanMessage(content="hello")])
    content = getattr(resp, "content", "")
    assert isinstance(content, str) and len(content) > 0


@pytest.mark.asyncio
async def test_smoke_query_non_empty():
    """执行一条常见指标查询，预期返回非空数据。"""
    # Skip if DB envs are not configured
    if not os.getenv("ZEGO_MYSQL_URL"):
        pytest.skip("DB env not configured")

    from src.langgraph.zego_tools.ocean_executor import execute_sql_async
    from src.langgraph.zego_tools.sql_generator import DataQueryParams, generate_sql

    params = DataQueryParams(metric_name="拉流成功率", query_title="[自检] 拉流成功率")
    sql = generate_sql(params)
    result = await execute_sql_async("ocean", sql, max_retries=2, base_delay=0.5)

    assert result.is_successful is True
    assert result.data is not None and len(result.data) > 0
