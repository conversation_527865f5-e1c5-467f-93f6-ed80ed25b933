"""
数据模型单元测试
验证SQLModel数据模型的正确性
"""

import asyncio

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlmodel import select, SQLModel
from src.storage.enums import ResultType, StepStatus, TaskStatus

from src.storage.models import TaskPlanModel, TaskPlanStepModel, TaskPlanStepModel, TaskResultModel, WorkspaceModel


# 测试数据库URL（使用内存SQLite）
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.mark.asyncio
async def test_simple_async():
    """简单的异步测试，验证pytest-asyncio是否工作"""
    await asyncio.sleep(0.1)
    assert True


@pytest.fixture
async def test_engine():
    """创建测试数据库引擎"""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False, connect_args={"check_same_thread": False})

    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)

    yield engine

    # 清理
    await engine.dispose()


@pytest.fixture
async def test_session(test_engine):
    """创建测试数据库会话"""
    async with AsyncSession(test_engine, expire_on_commit=False) as session:
        yield session


@pytest.mark.asyncio
class TestWorkspaceModel:
    """工作空间模型测试"""

    async def test_create_workspace(self, test_session: AsyncSession):
        """测试创建工作空间"""
        workspace = WorkspaceModel(name="test_workspace", description="测试工作空间", config={"test": True})

        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        assert workspace.id is not None
        assert workspace.name == "test_workspace"
        assert workspace.description == "测试工作空间"
        assert workspace.config["test"] is True
        assert workspace.created_at is not None
        assert workspace.updated_at is not None

    async def test_workspace_unique_name(self, test_session: AsyncSession):
        """测试工作空间名称唯一性"""
        # 创建第一个工作空间
        workspace1 = WorkspaceModel(name="unique_name")
        test_session.add(workspace1)
        await test_session.commit()

        # 尝试创建同名工作空间
        workspace2 = WorkspaceModel(name="unique_name")
        test_session.add(workspace2)

        with pytest.raises(Exception):  # 应该抛出唯一性约束异常
            await test_session.commit()


class TestTaskPlanStepModel:
    """任务模型测试"""

    async def test_create_task(self, test_session: AsyncSession):
        """测试创建任务"""
        # 先创建工作空间
        workspace = WorkspaceModel(name="test_workspace")
        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        # 创建任务
        task = TaskPlanStepModel(
            query_id="test_query_123",
            thread_id="test_thread_123",
            workspace_id=workspace.id,
            user_query="测试查询",
            status=TaskStatus.PENDING,
        )

        test_session.add(task)
        await test_session.commit()
        await test_session.refresh(task)

        assert task.id is not None
        assert task.query_id == "test_query_123"
        assert task.thread_id == "test_thread_123"
        assert task.workspace_id == workspace.id
        assert task.user_query == "测试查询"
        assert task.status == TaskStatus.PENDING
        assert not task.is_terminal_state

    async def test_task_progress_calculation(self, test_session: AsyncSession):
        """测试任务进度计算"""
        workspace = WorkspaceModel(name="test_workspace")
        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        task = TaskPlanStepModel(
            query_id="test_query",
            thread_id="test_thread",
            workspace_id=workspace.id,
            user_query="测试查询",
        )

    async def test_task_terminal_state(self, test_session: AsyncSession):
        """测试任务终止状态"""
        workspace = WorkspaceModel(name="test_workspace")
        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        task = TaskPlanStepModel(
            query_id="test_query", thread_id="test_thread", workspace_id=workspace.id, user_query="测试查询"
        )

        # 初始状态不是终止状态
        assert not task.is_terminal_state

        # 完成状态是终止状态
        task.status = TaskStatus.COMPLETED
        assert task.is_terminal_state

        # 失败状态是终止状态
        task.status = TaskStatus.FAILED
        assert task.is_terminal_state

        # 取消状态是终止状态
        task.status = TaskStatus.CANCELLED
        assert task.is_terminal_state


class TestTaskPlanModel:
    """任务规划模型测试"""

    async def test_create_task_plan(self, test_session: AsyncSession):
        """测试创建任务规划"""
        # 创建前置数据
        workspace = WorkspaceModel(name="test_workspace")
        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        task = TaskPlanStepModel(
            query_id="test_query", thread_id="test_thread", workspace_id=workspace.id, user_query="测试查询"
        )
        test_session.add(task)
        await test_session.commit()
        await test_session.refresh(task)

        # 创建规划
        plan = TaskPlanModel(
            task_id=task.id,
            goal="分析数据趋势",
            thinking="需要查询相关数据并分析",
            plan_data={"tasks": [{"id": 1, "desc": "查询数据"}]},
            round=1,
            final_round=True,
        )

        test_session.add(plan)
        await test_session.commit()
        await test_session.refresh(plan)

        assert plan.id is not None
        assert plan.task_id == task.id
        assert plan.goal == "分析数据趋势"
        assert plan.round == 1
        assert plan.final_round is True


class TestTaskPlanStepModel:
    """任务步骤模型测试"""

    async def test_create_task_step(self, test_session: AsyncSession):
        """测试创建任务步骤"""
        # 创建前置数据
        workspace = WorkspaceModel(name="test_workspace")
        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        task = TaskPlanStepModel(
            query_id="test_query", thread_id="test_thread", workspace_id=workspace.id, user_query="测试查询"
        )
        test_session.add(task)
        await test_session.commit()
        await test_session.refresh(task)

        plan = TaskPlanModel(task_id=task.id, goal="测试目标", plan_data={"tasks": []})
        test_session.add(plan)
        await test_session.commit()
        await test_session.refresh(plan)

        # 创建步骤
        step = TaskPlanStepModel(
            task_id=task.id,
            plan_id=plan.id,
            step_id=1,
            step_name="查询数据",
            depends_on=[],
            status=StepStatus.PENDING,
            query_params={"metric": "success_rate"},
        )

        test_session.add(step)
        await test_session.commit()
        await test_session.refresh(step)

        assert step.id is not None
        assert step.task_id == task.id
        assert step.plan_id == plan.id
        assert step.step_id == 1
        assert step.step_name == "查询数据"
        assert step.status == StepStatus.PENDING
        assert not step.is_ready  # PENDING 状态不是 READY
        assert not step.is_terminal_state

    async def test_step_status_transitions(self, test_session: AsyncSession):
        """测试步骤状态转换"""
        workspace = WorkspaceModel(name="test_workspace")
        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        task = TaskPlanStepModel(
            query_id="test_query", thread_id="test_thread", workspace_id=workspace.id, user_query="测试查询"
        )
        test_session.add(task)
        await test_session.commit()
        await test_session.refresh(task)

        plan = TaskPlanModel(task_id=task.id, goal="测试", plan_data={})
        test_session.add(plan)
        await test_session.commit()
        await test_session.refresh(plan)

        step = TaskPlanStepModel(task_id=task.id, plan_id=plan.id, step_id=1, step_name="测试步骤")

        # READY 状态
        step.status = StepStatus.READY
        assert step.is_ready
        assert not step.is_terminal_state

        # DONE 状态
        step.status = StepStatus.DONE
        assert not step.is_ready
        assert step.is_terminal_state

        # FAILED 状态
        step.status = StepStatus.FAILED
        assert not step.is_ready
        assert step.is_terminal_state


class TestTaskResultModel:
    """任务结果模型测试"""

    async def test_create_task_result(self, test_session: AsyncSession):
        """测试创建任务结果"""
        # 创建前置数据
        workspace = WorkspaceModel(name="test_workspace")
        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        task = TaskPlanStepModel(
            query_id="test_query", thread_id="test_thread", workspace_id=workspace.id, user_query="测试查询"
        )
        test_session.add(task)
        await test_session.commit()
        await test_session.refresh(task)

        # 创建结果
        result = TaskResultModel(
            task_id=task.id,
            result_type=ResultType.FINAL,
            content="分析结果：推流成功率为95%",
            structured_data={"success_rate": 0.95},
            token_usage={"input": 100, "output": 50},
        )

        test_session.add(result)
        await test_session.commit()
        await test_session.refresh(result)

        assert result.id is not None
        assert result.task_id == task.id
        assert result.result_type == ResultType.FINAL
        assert result.content == "分析结果：推流成功率为95%"
        assert result.structured_data["success_rate"] == 0.95
        assert result.token_usage["input"] == 100


class TestModelRelationships:
    """模型关系测试"""

    async def test_workspace_task_relationship(self, test_session: AsyncSession):
        """测试工作空间和任务的关系"""
        workspace = WorkspaceModel(name="test_workspace")
        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        task1 = TaskPlanStepModel(query_id="task1", thread_id="thread1", workspace_id=workspace.id, user_query="查询1")
        task2 = TaskPlanStepModel(query_id="task2", thread_id="thread2", workspace_id=workspace.id, user_query="查询2")

        test_session.add_all([task1, task2])
        await test_session.commit()

        # 查询工作空间及其任务
        result = await test_session.execute(select(WorkspaceModel).where(WorkspaceModel.id == workspace.id))
        ws = result.scalar_one()

        # 验证关系
        await test_session.refresh(ws, ["tasks"])
        assert len(ws.tasks) == 2
        assert {task.query_id for task in ws.tasks} == {"task1", "task2"}

    async def test_task_plan_relationship(self, test_session: AsyncSession):
        """测试任务和规划的关系"""
        workspace = WorkspaceModel(name="test_workspace")
        test_session.add(workspace)
        await test_session.commit()
        await test_session.refresh(workspace)

        task = TaskPlanStepModel(
            query_id="test_query", thread_id="test_thread", workspace_id=workspace.id, user_query="测试查询"
        )
        test_session.add(task)
        await test_session.commit()
        await test_session.refresh(task)

        plan1 = TaskPlanModel(task_id=task.id, goal="目标1", plan_data={}, round=1)
        plan2 = TaskPlanModel(task_id=task.id, goal="目标2", plan_data={}, round=2)

        test_session.add_all([plan1, plan2])
        await test_session.commit()

        # 查询任务及其规划
        result = await test_session.execute(select(TaskPlanStepModel).where(TaskPlanStepModel.id == task.id))
        task_with_plans = result.scalar_one()

        await test_session.refresh(task_with_plans, ["plans"])
        assert len(task_with_plans.plans) == 2
        assert {plan.round for plan in task_with_plans.plans} == {1, 2}


# 运行测试的辅助函数
async def run_model_tests():
    """运行所有模型测试"""
    print("开始运行数据模型测试...")

    # 这里可以添加测试运行逻辑
    # 实际测试应该通过 pytest 运行

    print("✅ 数据模型测试完成")


if __name__ == "__main__":
    # 运行基础测试
    asyncio.run(run_model_tests())
