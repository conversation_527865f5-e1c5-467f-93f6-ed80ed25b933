-- 指标: 登录成功率
-- 生成时间: 2025-08-26 15:07:07
-- 包含场景: 基础查询、维度下钻、过滤条件、错误码分布

-- === 基础查询 ===

SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (event = 'login' AND error NOT IN (10001001, 10009200, 10009201, 10009202, 60001041, 60001042, 60001043, 60001044, 60001045, 1100001, 1002001, 1002011, 1002012, 1002013, 1002005, 1002006, 1002007, 1002056, 1002018, 1002014, 50001011))
GROUP BY timestamp
ORDER BY timestamp
LIMIT 3000
    

