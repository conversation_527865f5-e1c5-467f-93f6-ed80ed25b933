-- 指标: 3s推流请求成功率
-- 生成时间: 2025-08-26 15:07:07
-- 包含场景: 基础查询、维度下钻、过滤条件、错误码分布

-- === 基础查询 ===

SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (10001001, 10009200, 10009201, 10009202, 60001041, 60001042, 60001043, 60001044, 60001045, 1103001, 1103044, 1103099, 1000002, 12301011, 63000002, 52001012, 63000001, 12301014, 15000002))
GROUP BY timestamp
ORDER BY timestamp
LIMIT 3000
    

