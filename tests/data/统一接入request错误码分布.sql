-- 指标: 统一接入request错误码分布
-- 生成时间: 2025-08-26 15:07:07
-- 包含场景: 基础查询、维度下钻、过滤条件、错误码分布

-- === 基础查询 ===

WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(100.0 * SUM(err_cnt) / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (event = 'zegoconn_request' AND error NOT IN (666, 777, 888))
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc
LIMIT 3000


-- === 测试场景: error_distribution ===

WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(100.0 * SUM(err_cnt) / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (event = 'zegoconn_request' AND error NOT IN (666, 777, 888))
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc
LIMIT 3000


