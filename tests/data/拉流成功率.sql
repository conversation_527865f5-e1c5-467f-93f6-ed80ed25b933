-- 指标: 拉流成功率
-- 生成时间: 2025-08-26 15:07:07
-- 包含场景: 基础查询、维度下钻、过滤条件、错误码分布

-- === 基础查询 ===

SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (event = 'play' AND error NOT IN (10001001, 10009200, 10009201, 10009202, 60001041, 60001042, 60001043, 60001044, 60001045, 1104001, 1000014, 1103049, 12102001, 12301004, 12301011, 12301014, 32001004, 63000001, 63000002, 1002055, 1104042))
GROUP BY timestamp
ORDER BY timestamp
LIMIT 3000
    

-- === 测试场景: dimension_country ===

WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count,
        ROUND(100.0 * SUM(err_cnt) / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp),2) as dimension_percentage
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (event = 'play' AND error NOT IN (10001001, 10009200, 10009201, 10009202, 60001041, 60001042, 60001043, 60001044, 60001045, 1104001, 1000014, 1103049, 12102001, 12301004, 12301011, 12301014, 32001004, 63000001, 63000002, 1002055, 1104042))
    GROUP BY timestamp, country
)
SELECT
    timestamp,
    country,
    metric_value,
    total_count,
    dimension_percentage
FROM daily_stats
where dimension_percentage > 1
ORDER BY timestamp, country, dimension_percentage DESC
LIMIT 3000


-- === 测试场景: dimension_sdk_version ===

WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count,
        ROUND(100.0 * SUM(err_cnt) / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp),2) as dimension_percentage
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (event = 'play' AND error NOT IN (10001001, 10009200, 10009201, 10009202, 60001041, 60001042, 60001043, 60001044, 60001045, 1104001, 1000014, 1103049, 12102001, 12301004, 12301011, 12301014, 32001004, 63000001, 63000002, 1002055, 1104042))
    GROUP BY timestamp, sdk_version
)
SELECT
    timestamp,
    sdk_version,
    metric_value,
    total_count,
    dimension_percentage
FROM daily_stats
where dimension_percentage > 1
ORDER BY timestamp, sdk_version, dimension_percentage DESC
LIMIT 3000


-- === 测试场景: dimension_app_id ===

WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count,
        ROUND(100.0 * SUM(err_cnt) / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp),2) as dimension_percentage
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (event = 'play' AND error NOT IN (10001001, 10009200, 10009201, 10009202, 60001041, 60001042, 60001043, 60001044, 60001045, 1104001, 1000014, 1103049, 12102001, 12301004, 12301011, 12301014, 32001004, 63000001, 63000002, 1002055, 1104042))
    GROUP BY timestamp, app_id
)
SELECT
    timestamp,
    app_id,
    metric_value,
    total_count,
    dimension_percentage
FROM daily_stats
where dimension_percentage > 1
ORDER BY timestamp, app_id, dimension_percentage DESC
LIMIT 3000


-- === 测试场景: filter_appid_3575801176_country_all ===

SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (app_id = 3575801176) AND (event = 'play' AND error NOT IN (10001001, 10009200, 10009201, 10009202, 60001041, 60001042, 60001043, 60001044, 60001045, 1104001, 1000014, 1103049, 12102001, 12301004, 12301011, 12301014, 32001004, 63000001, 63000002, 1002055, 1104042))
GROUP BY timestamp
ORDER BY timestamp
LIMIT 3000
    

-- === 测试场景: filter_appid_all_country_沙特阿拉伯 ===

SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (country = '沙特阿拉伯') AND (event = 'play' AND error NOT IN (10001001, 10009200, 10009201, 10009202, 60001041, 60001042, 60001043, 60001044, 60001045, 1104001, 1000014, 1103049, 12102001, 12301004, 12301011, 12301014, 32001004, 63000001, 63000002, 1002055, 1104042))
GROUP BY timestamp
ORDER BY timestamp
LIMIT 3000
    

-- === 测试场景: filter_appid_3206531758_country_美国 ===

SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (app_id = 3206531758) AND (country = '美国') AND (event = 'play' AND error NOT IN (10001001, 10009200, 10009201, 10009202, 60001041, 60001042, 60001043, 60001044, 60001045, 1104001, 1000014, 1103049, 12102001, 12301004, 12301011, 12301014, 32001004, 63000001, 63000002, 1002055, 1104042))
GROUP BY timestamp
ORDER BY timestamp
LIMIT 3000
    

