-- 指标: 拉流调度成功率
-- 生成时间: 2025-08-26 15:07:07
-- 包含场景: 基础查询、维度下钻、过滤条件、错误码分布

-- === 基础查询 ===

SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN '2025-08-19 15:07:07' AND '2025-08-26 15:07:07' AND (event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004))
GROUP BY timestamp
ORDER BY timestamp
LIMIT 3000
    

