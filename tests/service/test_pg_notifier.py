"""
PostgreSQL 通知器 (pg_notifier) 单元测试

测试涵盖：
- 基本监听功能
- 多消费者支持
- 上下文管理器
- 错误处理
- 并发访问安全性
- 资源清理
- 重复回调处理
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio

# 确保项目根目录在 Python 路径中
ROOT = Path(__file__).resolve().parents[2]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

# 模拟所有外部依赖，避免导入项目模块时的依赖问题
sys.modules["asyncpg"] = MagicMock()
sys.modules["pydantic"] = MagicMock()
sys.modules["sqlalchemy"] = MagicMock()
sys.modules["src.storage.database"] = MagicMock()

# 直接执行pg_notifier.py代码来定义PostgreSQLNotifier类
exec(open(ROOT / "src" / "storage" / "pg_notifier.py").read())


class TestPostgreSQLNotifier:
    """PostgreSQL 通知器单元测试类"""

    @pytest_asyncio.fixture
    async def notifier(self):
        """创建测试用的通知器实例"""
        notifier = PostgreSQLNotifier()

        # 模拟连接
        mock_conn = AsyncMock()
        notifier._connection = mock_conn
        notifier._initialized = True

        yield notifier

        # 清理
        await notifier.close()

    @pytest.mark.asyncio
    async def test_initialization(self, notifier):
        """测试初始化功能"""
        assert notifier._connection is not None
        assert notifier._initialized is True
        assert isinstance(notifier._listeners, dict)
        assert isinstance(notifier._active_listeners, dict)
        assert isinstance(notifier._lock, asyncio.Lock)

    @pytest.mark.asyncio
    async def test_single_consumer_listen(self, notifier):
        """测试单个消费者监听"""
        channel = "test_channel"
        received_messages = []

        async def callback(data: Dict[str, Any]):
            received_messages.append(data)

        # 监听channel
        await notifier.listen(channel, callback)

        # 验证内部状态
        assert channel in notifier._listeners
        assert len(notifier._listeners[channel]) == 1
        assert notifier._active_listeners[channel] == 1
        assert notifier._listeners[channel][0] == callback

        # 模拟接收通知
        test_data = {"type": "test", "message": "Hello World"}
        payload = json.dumps(test_data)
        notifier._handle_notification(notifier._connection, 123, channel, payload)

        # 等待异步回调完成
        await asyncio.sleep(0.01)

        # 验证回调被调用
        assert len(received_messages) == 1
        assert received_messages[0] == test_data

    @pytest.mark.asyncio
    async def test_multiple_consumers_same_channel(self, notifier):
        """测试同一channel的多个消费者"""
        channel = "multi_consumer_channel"
        received_messages = []

        async def consumer1(data: Dict[str, Any]):
            received_messages.append(f"consumer1: {data}")

        async def consumer2(data: Dict[str, Any]):
            received_messages.append(f"consumer2: {data}")

        async def consumer3(data: Dict[str, Any]):
            received_messages.append(f"consumer3: {data}")

        # 添加三个消费者
        await notifier.listen(channel, consumer1)
        await notifier.listen(channel, consumer2)
        await notifier.listen(channel, consumer3)

        # 验证所有消费者都已注册
        assert len(notifier._listeners[channel]) == 3
        assert notifier._active_listeners[channel] == 3

        # 模拟接收通知
        test_data = {"type": "broadcast", "message": "Hello All"}
        payload = json.dumps(test_data)
        notifier._handle_notification(notifier._connection, 123, channel, payload)

        # 等待异步回调完成
        await asyncio.sleep(0.01)

        # 验证所有消费者都收到了消息
        assert len(received_messages) == 3
        assert any("consumer1:" in msg for msg in received_messages)
        assert any("consumer2:" in msg for msg in received_messages)
        assert any("consumer3:" in msg for msg in received_messages)

    @pytest.mark.asyncio
    async def test_duplicate_callback_prevention(self, notifier):
        """测试重复回调的预防"""
        channel = "duplicate_test_channel"

        async def callback(data: Dict[str, Any]):
            pass

        # 第一次添加
        await notifier.listen(channel, callback)
        assert len(notifier._listeners[channel]) == 1
        assert notifier._active_listeners[channel] == 1

        # 第二次添加相同回调（应该被忽略）
        await notifier.listen(channel, callback)
        assert len(notifier._listeners[channel]) == 1  # 数量不变
        assert notifier._active_listeners[channel] == 1  # 计数不变

    @pytest.mark.asyncio
    async def test_remove_specific_consumer(self, notifier):
        """测试移除特定消费者"""
        channel = "remove_test_channel"

        async def consumer1(data: Dict[str, Any]):
            pass

        async def consumer2(data: Dict[str, Any]):
            pass

        async def consumer3(data: Dict[str, Any]):
            pass

        # 添加三个消费者
        await notifier.listen(channel, consumer1)
        await notifier.listen(channel, consumer2)
        await notifier.listen(channel, consumer3)

        assert len(notifier._listeners[channel]) == 3

        # 移除中间的消费者
        await notifier.unlisten(channel, consumer2)

        # 验证只移除了consumer2
        assert len(notifier._listeners[channel]) == 2
        assert notifier._active_listeners[channel] == 2
        assert consumer1 in notifier._listeners[channel]
        assert consumer2 not in notifier._listeners[channel]
        assert consumer3 in notifier._listeners[channel]

        # 移除另一个消费者
        await notifier.unlisten(channel, consumer1)

        # 验证只剩下consumer3
        assert len(notifier._listeners[channel]) == 1
        assert consumer3 in notifier._listeners[channel]

        # 移除最后一个消费者
        await notifier.unlisten(channel, consumer3)

        # 验证channel已被清理
        assert channel not in notifier._listeners
        assert channel not in notifier._active_listeners

    @pytest.mark.asyncio
    async def test_context_manager_basic(self, notifier):
        """测试上下文管理器基本功能"""
        channel = "context_test_channel"
        received_messages = []

        async def callback(data: Dict[str, Any]):
            received_messages.append(data)

        # 使用上下文管理器
        async with notifier.listen_context(channel, callback):
            # 验证消费者已注册
            assert channel in notifier._listeners
            assert len(notifier._listeners[channel]) == 1

            # 发送通知
            test_data = {"type": "context_test", "message": "Hello Context"}
            payload = json.dumps(test_data)
            notifier._handle_notification(notifier._connection, 123, channel, payload)
            await asyncio.sleep(0.01)

            # 验证消息被接收
            assert len(received_messages) == 1
            assert received_messages[0] == test_data

        # 退出上下文后，消费者应该被自动移除
        assert channel not in notifier._listeners

    @pytest.mark.asyncio
    async def test_context_manager_multiple_consumers(self, notifier):
        """测试上下文管理器多消费者场景"""
        channel = "multi_context_channel"
        received_messages = []

        async def consumer_a(data: Dict[str, Any]):
            received_messages.append(f"A: {data}")

        async def consumer_b(data: Dict[str, Any]):
            received_messages.append(f"B: {data}")

        # 嵌套的上下文管理器
        async with notifier.listen_context(channel, consumer_a):
            assert len(notifier._listeners[channel]) == 1

            async with notifier.listen_context(channel, consumer_b):
                assert len(notifier._listeners[channel]) == 2

                # 发送通知
                test_data = {"type": "nested_context_test"}
                payload = json.dumps(test_data)
                notifier._handle_notification(notifier._connection, 123, channel, payload)
                await asyncio.sleep(0.01)

                # 验证两个消费者都收到了消息
                assert len(received_messages) == 2
                assert any("A:" in msg for msg in received_messages)
                assert any("B:" in msg for msg in received_messages)

            # consumer_b退出后，只剩下consumer_a
            assert len(notifier._listeners[channel]) == 1

        # 所有上下文退出后，channel应该被清理
        assert channel not in notifier._listeners

    @pytest.mark.asyncio
    async def test_concurrent_access_safety(self, notifier):
        """测试并发访问安全性"""
        channel = "concurrent_test_channel"
        received_messages = []
        concurrent_operations = []

        async def concurrent_consumer(operation_id: int):
            async def callback(data: Dict[str, Any]):
                received_messages.append(f"op_{operation_id}: {data}")

            # 每个操作都在自己的上下文中
            async with notifier.listen_context(channel, callback):
                concurrent_operations.append(f"start_{operation_id}")
                await asyncio.sleep(0.01)  # 模拟并发操作
                concurrent_operations.append(f"end_{operation_id}")

        # 并发执行多个操作
        tasks = [concurrent_consumer(i) for i in range(5)]
        await asyncio.gather(*tasks)

        # 验证所有操作都完成了
        assert len(concurrent_operations) == 10  # 5个start + 5个end
        assert len([op for op in concurrent_operations if op.startswith("start_")]) == 5
        assert len([op for op in concurrent_operations if op.startswith("end_")]) == 5

        # 验证最终状态是干净的（所有上下文都已退出）
        assert channel not in notifier._listeners

    @pytest.mark.asyncio
    async def test_error_handling_in_callback(self, notifier):
        """测试回调函数中的错误处理"""
        channel = "error_test_channel"
        error_messages = []

        async def error_callback(data: Dict[str, Any]):
            error_messages.append("error_callback_called")
            raise Exception("Test error in callback")

        async def normal_callback(data: Dict[str, Any]):
            error_messages.append("normal_callback_called")

        # 添加两个消费者：一个会抛出异常，一个正常
        await notifier.listen(channel, error_callback)
        await notifier.listen(channel, normal_callback)

        # 发送通知（不应该因为一个回调异常而影响其他回调）
        test_data = {"type": "error_test"}
        payload = json.dumps(test_data)
        notifier._handle_notification(notifier._connection, 123, channel, payload)

        # 等待异步回调完成
        await asyncio.sleep(0.01)

        # 验证两个回调都被调用了（即使其中一个抛出了异常）
        assert "error_callback_called" in error_messages
        assert "normal_callback_called" in error_messages

    @pytest.mark.asyncio
    async def test_remove_nonexistent_callback(self, notifier):
        """测试移除不存在的回调"""
        channel = "nonexistent_callback_channel"

        async def existing_callback(data: Dict[str, Any]):
            pass

        async def nonexistent_callback(data: Dict[str, Any]):
            pass

        # 只添加一个回调
        await notifier.listen(channel, existing_callback)

        # 尝试移除不存在的回调（应该不会抛出异常）
        await notifier.unlisten(channel, nonexistent_callback)

        # 验证现有回调仍然存在
        assert len(notifier._listeners[channel]) == 1
        assert existing_callback in notifier._listeners[channel]

    @pytest.mark.asyncio
    async def test_remove_all_consumers_without_callback(self, notifier):
        """测试不指定回调时移除所有消费者"""
        channel = "remove_all_channel"

        async def consumer1(data: Dict[str, Any]):
            pass

        async def consumer2(data: Dict[str, Any]):
            pass

        # 添加两个消费者
        await notifier.listen(channel, consumer1)
        await notifier.listen(channel, consumer2)

        assert len(notifier._listeners[channel]) == 2

        # 不指定回调参数，移除所有消费者
        await notifier.unlisten(channel)

        # 验证channel已被清理
        assert channel not in notifier._listeners
        assert channel not in notifier._active_listeners

    @pytest.mark.asyncio
    async def test_channel_isolation(self, notifier):
        """测试不同channel之间的隔离性"""
        channel1 = "channel_1"
        channel2 = "channel_2"

        async def callback1(data: Dict[str, Any]):
            pass

        async def callback2(data: Dict[str, Any]):
            pass

        # 在两个不同的channel上添加消费者
        await notifier.listen(channel1, callback1)
        await notifier.listen(channel2, callback2)

        # 验证两个channel都存在且相互独立
        assert len(notifier._listeners[channel1]) == 1
        assert len(notifier._listeners[channel2]) == 1
        assert notifier._active_listeners[channel1] == 1
        assert notifier._active_listeners[channel2] == 1

        # 移除channel1的所有消费者
        await notifier.unlisten(channel1)

        # 验证channel1已被清理，但channel2不受影响
        assert channel1 not in notifier._listeners
        assert len(notifier._listeners[channel2]) == 1

    @pytest.mark.asyncio
    async def test_empty_payload_handling(self, notifier):
        """测试空payload的处理"""
        channel = "empty_payload_channel"

        async def callback(data: Dict[str, Any]):
            pass

        await notifier.listen(channel, callback)

        # 发送空payload
        notifier._handle_notification(notifier._connection, 123, channel, "")

        # 应该不会抛出异常
        await asyncio.sleep(0.01)

        # 验证监听器仍然存在（尽管payload无效）
        assert channel in notifier._listeners

    @pytest.mark.asyncio
    async def test_malformed_json_handling(self, notifier):
        """测试畸形JSON的处理"""
        channel = "malformed_json_channel"

        async def callback(data: Dict[str, Any]):
            pass

        await notifier.listen(channel, callback)

        # 发送畸形JSON
        notifier._handle_notification(notifier._connection, 123, channel, "{invalid json")

        # 应该不会抛出异常
        await asyncio.sleep(0.01)

        # 验证监听器仍然存在
        assert channel in notifier._listeners

    @pytest.mark.asyncio
    async def test_notify_functionality(self, notifier):
        """测试通知发送功能"""
        # Mock session with context manager support
        mock_session = AsyncMock()

        # Mock get_session function that returns a context manager
        class MockSessionContextManager:
            def __init__(self, session):
                self.session = session

            async def __aenter__(self):
                return self.session

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        # 声明全局变量
        global get_session

        # 保存原始函数
        original_get_session = get_session

        def mock_get_session():
            return MockSessionContextManager(mock_session)

        try:
            # 替换全局函数
            get_session = mock_get_session

            # 发送通知
            await notifier.notify("test_channel", {"message": "test"})

            # 验证session.execute被调用
            mock_session.execute.assert_called_once()
            mock_session.commit.assert_called_once()

        finally:
            # 恢复原始函数
            get_session = original_get_session


class TestPostgreSQLNotifierIntegration:
    """集成测试类 - 测试更接近实际使用场景的功能"""

    @pytest.mark.asyncio
    async def test_real_world_scenario(self):
        """测试接近真实世界的场景"""
        notifier = PostgreSQLNotifier()

        # 模拟连接
        mock_conn = AsyncMock()
        notifier._connection = mock_conn
        notifier._initialized = True

        try:
            # 模拟Web系统、API系统和日志系统同时监听任务更新
            task_channel = "task_updates"
            web_messages = []
            api_messages = []
            log_messages = []

            async def web_consumer(data: Dict[str, Any]):
                web_messages.append(f"Web: Task {data.get('task_id')} -> {data.get('status')}")

            async def api_consumer(data: Dict[str, Any]):
                api_messages.append(f"API: Task {data.get('task_id')} -> {data.get('status')}")

            async def log_consumer(data: Dict[str, Any]):
                log_messages.append(f"Log: Task {data.get('task_id')} -> {data.get('status')}")

            # 三个系统同时开始监听
            await notifier.listen(task_channel, web_consumer)
            await notifier.listen(task_channel, api_consumer)
            await notifier.listen(task_channel, log_consumer)

            # 模拟任务状态更新通知
            task_updates = [
                {"task_id": "task_001", "status": "running", "progress": 25},
                {"task_id": "task_001", "status": "running", "progress": 50},
                {"task_id": "task_001", "status": "completed", "progress": 100},
            ]

            for update in task_updates:
                payload = json.dumps(update)
                notifier._handle_notification(mock_conn, 123, task_channel, payload)

            # 等待所有异步回调完成
            await asyncio.sleep(0.05)

            # 验证每个系统都收到了所有更新
            assert len(web_messages) == 3
            assert len(api_messages) == 3
            assert len(log_messages) == 3

            # 验证消息内容
            assert "task_001" in web_messages[0]
            assert "running" in web_messages[0]
            assert "completed" in web_messages[2]

        finally:
            await notifier.close()

    @pytest.mark.asyncio
    async def test_resource_cleanup_comprehensive(self):
        """测试全面的资源清理"""
        notifier = PostgreSQLNotifier()
        mock_conn = AsyncMock()
        notifier._connection = mock_conn
        notifier._initialized = True

        try:
            # 创建多个channel和消费者
            channels = ["channel_a", "channel_b", "channel_c"]
            consumers_per_channel = 3

            for channel in channels:
                for i in range(consumers_per_channel):

                    async def consumer(data: Dict[str, Any]):
                        pass

                    await notifier.listen(channel, consumer)

            # 验证所有channel和消费者都已创建
            for channel in channels:
                assert len(notifier._listeners[channel]) == consumers_per_channel
                assert notifier._active_listeners[channel] == consumers_per_channel

            # 逐步清理资源
            for channel in channels:
                for i in range(consumers_per_channel):
                    # 每次移除一个消费者
                    callbacks = notifier._listeners[channel]
                    if callbacks:
                        await notifier.unlisten(channel, callbacks[0])

            # 验证所有资源都已被清理
            for channel in channels:
                assert channel not in notifier._listeners
                assert channel not in notifier._active_listeners

        finally:
            await notifier.close()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
