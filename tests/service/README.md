# Service Layer Unit Tests

这个目录包含服务层的单元测试，专注于测试业务逻辑组件。

## PostgreSQL 通知器测试 (`test_pg_notifier.py`)

### 概述
该测试文件全面测试了 PostgreSQL 通知器的所有功能，包括多消费者支持、上下文管理器、并发安全性等。

### 测试分类

#### 1. 基础功能测试 (`TestPostgreSQLNotifier`)
- **test_initialization**: 测试初始化功能
- **test_single_consumer_listen**: 测试单个消费者监听
- **test_multiple_consumers_same_channel**: 测试同一channel的多个消费者
- **test_duplicate_callback_prevention**: 测试重复回调预防
- **test_remove_specific_consumer**: 测试移除特定消费者
- **test_context_manager_basic**: 测试上下文管理器基本功能
- **test_context_manager_multiple_consumers**: 测试上下文管理器多消费者场景
- **test_concurrent_access_safety**: 测试并发访问安全性
- **test_error_handling_in_callback**: 测试回调函数中的错误处理
- **test_remove_nonexistent_callback**: 测试移除不存在的回调
- **test_remove_all_consumers_without_callback**: 测试不指定回调时移除所有消费者
- **test_channel_isolation**: 测试不同channel之间的隔离性
- **test_empty_payload_handling**: 测试空payload的处理
- **test_malformed_json_handling**: 测试畸形JSON的处理
- **test_notify_functionality**: 测试通知发送功能

#### 2. 集成测试 (`TestPostgreSQLNotifierIntegration`)
- **test_real_world_scenario**: 测试接近真实世界的场景
- **test_resource_cleanup_comprehensive**: 测试全面的资源清理

### 运行测试

```bash
# 运行所有测试
uv run python -m pytest tests/service/ -v

# 运行特定测试文件
uv run python -m pytest tests/service/test_pg_notifier.py -v

# 运行特定测试用例
uv run python -m pytest tests/service/test_pg_notifier.py::TestPostgreSQLNotifier::test_multiple_consumers_same_channel -v
```

### 测试覆盖的功能

1. **多消费者支持**: 同一channel可以有多个消费者，所有消费者都会收到相同的通知
2. **上下文管理器**: 自动资源管理，确保资源正确释放
3. **并发安全性**: 使用异步锁保护并发访问
4. **错误处理**: 回调异常不会影响其他消费者
5. **资源清理**: 自动清理不再使用的监听器和channel
6. **重复预防**: 防止相同回调被重复添加
7. **隔离性**: 不同channel之间完全隔离

### 测试策略

- **Mock 依赖**: 使用 MagicMock 模拟外部依赖，避免测试时的复杂依赖
- **异步测试**: 使用 pytest-asyncio 支持异步测试
- **Fixture 模式**: 使用 pytest fixture 提供测试所需的组件
- **全面覆盖**: 覆盖正常流程、异常情况、边界条件等

### 扩展测试

当添加新的功能时，请相应地添加测试用例：

1. 在 `TestPostgreSQLNotifier` 中添加单元测试
2. 在 `TestPostgreSQLNotifierIntegration` 中添加集成测试
3. 确保测试覆盖率达到 90% 以上
4. 添加必要的文档和注释
