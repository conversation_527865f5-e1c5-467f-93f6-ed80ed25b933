"""
动态SQL生成测试
基于DataQueryParams.metric_name的Literal枚举值动态生成测试
"""

from __future__ import annotations

import os
import sys
import unittest
from datetime import datetime, timedelta
from pathlib import Path
from typing import get_args

# 确保可导入 src.*
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

from src.langgraph.zego_tools.sql_generator import DataQueryParams, generate_sql


class TestSQLGenDynamic(unittest.TestCase):
    """动态SQL生成测试类"""

    @classmethod
    def setUpClass(cls):
        """测试类初始化 - 清理旧的SQL文件"""
        test_data_dir = Path(PROJECT_ROOT) / "tests" / "data"
        test_data_dir.mkdir(parents=True, exist_ok=True)

        # 清理旧的SQL文件
        for sql_file in test_data_dir.glob("*.sql"):
            sql_file.unlink()
        print(f"已清理测试数据目录: {test_data_dir}")

    def setUp(self):
        """测试前准备"""
        self.time_range = timedelta(days=7)  # 使用7天范围，避免过大的数据量
        self.end_time = datetime.now()
        self.start_time = self.end_time - self.time_range

        # 获取所有可用的metric_name
        self.available_metrics = get_args(DataQueryParams.__annotations__["metric_name"])

        # 创建测试数据目录
        self.test_data_dir = Path(PROJECT_ROOT) / "tests" / "data"
        self.test_data_dir.mkdir(parents=True, exist_ok=True)

    def _save_sql_to_file(self, metric_name: str, sql: str, suffix: str = ""):
        """保存SQL到文件 - 每个指标一个文件，按顺序追加"""
        # 清理文件名中的特殊字符
        safe_name = metric_name.replace("/", "_").replace("\\", "_").replace(":", "_")
        filename = f"{safe_name}.sql"

        file_path = self.test_data_dir / filename

        # 如果文件不存在，创建并写入头部信息
        if not file_path.exists():
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(f"-- 指标: {metric_name}\n")
                f.write(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("-- 包含场景: 基础查询、维度下钻、过滤条件、错误码分布\n\n")

        # 追加SQL内容
        with open(file_path, "a", encoding="utf-8") as f:
            if suffix:
                f.write(f"-- === 测试场景: {suffix} ===\n")
            else:
                f.write("-- === 基础查询 ===\n")
            f.write(sql)
            f.write("\n\n")

    def test_all_metrics_sql_generation(self):
        """测试所有指标的SQL生成"""
        print(f"\n开始测试 {len(self.available_metrics)} 个指标的SQL生成...")

        successful_metrics = []
        failed_metrics = []

        for metric_name in self.available_metrics:
            try:
                # 创建基础查询参数
                params = DataQueryParams(
                    metric_name=metric_name,
                    time_start=self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                    time_end=self.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                    query_title=f"测试{metric_name}SQL生成",
                )

                # 生成SQL
                sql = generate_sql(params)

                # 基本验证
                self.assertIsInstance(sql, str)
                self.assertGreater(len(sql), 0)
                self.assertIn("SELECT", sql.upper())
                self.assertIn("FROM", sql.upper())

                # 保存SQL到文件
                self._save_sql_to_file(metric_name, sql)

                successful_metrics.append(metric_name)
                print(f"✅ {metric_name}: SQL生成成功")

            except Exception as e:
                failed_metrics.append((metric_name, str(e)))
                print(f"❌ {metric_name}: SQL生成失败 - {e}")

        # 输出统计结果
        print("\n=== SQL生成测试结果 ===")
        print(f"成功: {len(successful_metrics)}/{len(self.available_metrics)}")
        print(f"失败: {len(failed_metrics)}/{len(self.available_metrics)}")
        print(f"SQL文件保存到: {self.test_data_dir}")

        if failed_metrics:
            print("\n失败的指标:")
            for metric, error in failed_metrics:
                print(f"  - {metric}: {error}")

        # 要求100%成功，不能有任何失败
        self.assertEqual(len(failed_metrics), 0, f"有 {len(failed_metrics)} 个指标SQL生成失败，要求100%成功")

    def test_metrics_with_dimensions(self):
        """测试带维度下钻的SQL生成"""
        test_dimensions = ["country", "sdk_version", "app_id"]
        sample_metrics = ["拉流成功率", "推流成功率", "拉流错误码分布"]

        print("\n开始测试维度下钻SQL生成...")

        successful_tests = 0
        total_tests = 0

        for metric_name in sample_metrics:
            for dimension in test_dimensions:
                total_tests += 1
                try:
                    params = DataQueryParams(
                        metric_name=metric_name,
                        drilldown_dimension=dimension,
                        time_start=self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                        time_end=self.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                        query_title=f"测试{metric_name}按{dimension}下钻",
                    )

                    sql = generate_sql(params)

                    # 验证SQL包含GROUP BY
                    self.assertIn("GROUP BY", sql.upper())
                    self.assertIn(dimension, sql)

                    # 保存SQL到文件
                    self._save_sql_to_file(metric_name, sql, f"dimension_{dimension}")

                    successful_tests += 1
                    print(f"✅ {metric_name} + {dimension}: 维度下钻SQL生成成功")

                except Exception as e:
                    print(f"❌ {metric_name} + {dimension}: 维度下钻SQL生成失败 - {e}")

        print("\n=== 维度下钻测试结果 ===")
        print(f"成功: {successful_tests}/{total_tests}")

        # 要求100%成功，不能有任何失败
        self.assertEqual(
            total_tests - successful_tests,
            0,
            f"有 {total_tests - successful_tests} 个维度下钻SQL生成失败，要求100%成功",
        )

    def test_metrics_with_filters(self):
        """测试带过滤条件的SQL生成"""
        test_filters = [
            {"appid_filter": 3575801176, "country_filter": None},
            {"appid_filter": None, "country_filter": "沙特阿拉伯"},
            {"appid_filter": 3206531758, "country_filter": "美国"},
        ]

        sample_metrics = ["拉流成功率", "推流错误码分布"]

        print("\n开始测试过滤条件SQL生成...")

        successful_tests = 0
        total_tests = 0

        for metric_name in sample_metrics:
            for filter_config in test_filters:
                total_tests += 1
                try:
                    params = DataQueryParams(
                        metric_name=metric_name,
                        time_start=self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                        time_end=self.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                        appid_filter=filter_config["appid_filter"],
                        country_filter=filter_config["country_filter"],
                        query_title=f"测试{metric_name}过滤条件",
                    )

                    sql = generate_sql(params)

                    # 验证过滤条件在SQL中
                    if filter_config["appid_filter"]:
                        self.assertIn(str(filter_config["appid_filter"]), sql)
                    if filter_config["country_filter"]:
                        self.assertIn(filter_config["country_filter"], sql)

                    # 保存SQL到文件
                    filter_desc = f"appid_{filter_config['appid_filter'] or 'all'}_country_{filter_config['country_filter'] or 'all'}"
                    self._save_sql_to_file(metric_name, sql, f"filter_{filter_desc}")

                    successful_tests += 1
                    print(f"✅ {metric_name} + 过滤条件: SQL生成成功")

                except Exception as e:
                    print(f"❌ {metric_name} + 过滤条件: SQL生成失败 - {e}")

        print("\n=== 过滤条件测试结果 ===")
        print(f"成功: {successful_tests}/{total_tests}")

        # 要求100%成功，不能有任何失败
        self.assertEqual(
            total_tests - successful_tests,
            0,
            f"有 {total_tests - successful_tests} 个过滤条件SQL生成失败，要求100%成功",
        )

    def test_error_code_distribution_metrics(self):
        """专门测试错误码分布指标"""
        error_code_metrics = [m for m in self.available_metrics if "错误码分布" in m]

        print(f"\n开始测试 {len(error_code_metrics)} 个错误码分布指标...")

        successful_metrics = []
        failed_metrics = []

        for metric_name in error_code_metrics:
            try:
                params = DataQueryParams(
                    metric_name=metric_name,
                    time_start=self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                    time_end=self.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                    query_title=f"测试{metric_name}",
                )

                sql = generate_sql(params)

                # 错误码分布应该包含错误码相关的字段
                self.assertIn("error", sql.lower())

                # 保存SQL到文件
                self._save_sql_to_file(metric_name, sql, "error_distribution")

                successful_metrics.append(metric_name)
                print(f"✅ {metric_name}: 错误码分布SQL生成成功")

            except Exception as e:
                failed_metrics.append((metric_name, str(e)))
                print(f"❌ {metric_name}: 错误码分布SQL生成失败 - {e}")

        print("\n=== 错误码分布测试结果 ===")
        print(f"成功: {len(successful_metrics)}/{len(error_code_metrics)}")

        if failed_metrics:
            print("\n失败的错误码分布指标:")
            for metric, error in failed_metrics:
                print(f"  - {metric}: {error}")

        # 要求100%成功，不能有任何失败
        self.assertEqual(len(failed_metrics), 0, f"有 {len(failed_metrics)} 个错误码分布指标SQL生成失败，要求100%成功")


if __name__ == "__main__":
    unittest.main(verbosity=2)
