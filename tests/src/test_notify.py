import asyncio
from typing import List

import pytest
from langchain_core.messages import AIMessage

from src.lark.notify import GraphLarkNotifier, NotifyConfig


class FakeZlark:
    def __init__(self):
        self.reply_calls: List[dict] = []
        self.update_calls: List[dict] = []
        self._reply_none = False

    def reply_custom_card_msg(self, title: str, subtitle: str, elements: list, message_id: str, reply_in_thread: bool):
        self.reply_calls.append(
            {
                "title": title,
                "subtitle": subtitle,
                "elements_len": len(elements or []),
                "message_id": message_id,
                "reply_in_thread": reply_in_thread,
            }
        )
        if self._reply_none:
            return None
        return f"mid_{message_id}"

    def update_custom_card_msg(self, message_id: str, title: str, subtitle: str, elements: list):
        self.update_calls.append(
            {
                "message_id": message_id,
                "title": title,
                "subtitle": subtitle,
                "elements_len": len(elements or []),
            }
        )


@pytest.mark.asyncio
async def test_handle_update_no_deadlock():
    zlark = FakeZlark()
    cfg = NotifyConfig(debounce_ms=0, max_updates_per_minute=10000)
    notifier = GraphLarkNotifier(zlark, thread_message_id="tid-1", config=cfg)

    async def push_one(i: int):
        await notifier.update_state({"messages": [AIMessage(content=f"m{i}")]})

    # 并发多次调用，验证不会死锁
    tasks = [push_one(i) for i in range(50)]
    await asyncio.wait_for(asyncio.gather(*tasks), timeout=5)

    # 收尾强制刷新
    await notifier.flush(force=True)

    assert len(zlark.reply_calls) == 1
    assert len(zlark.update_calls) >= 1


@pytest.mark.asyncio
async def test_debounce_and_quota(monkeypatch):
    zlark = FakeZlark()
    cfg = NotifyConfig(debounce_ms=100, max_updates_per_minute=2)
    notifier = GraphLarkNotifier(zlark, thread_message_id="tid-2", config=cfg)

    # 可控时间源
    now = 0.0

    def fake_time():
        return now

    # patch 内部模块的 time.time
    monkeypatch.setattr("src.lark.notify.time.time", fake_time)

    # t=0，未过 debounce，不应刷新
    await notifier.update_state({"messages": [AIMessage(content="a")]})
    await notifier.update_state({"messages": [AIMessage(content="b")]})
    assert len(zlark.update_calls) == 0

    # t=0.2s，超过 debounce，触发第1次刷新
    now = 0.2
    await notifier.update_state({"messages": [AIMessage(content="c")]})
    assert len(zlark.update_calls) == 1


@pytest.mark.asyncio
async def test_reply_custom_card_none_fallback():
    zlark = FakeZlark()
    zlark._reply_none = True
    cfg = NotifyConfig(debounce_ms=0, max_updates_per_minute=10000)
    notifier = GraphLarkNotifier(zlark, thread_message_id="tid-4", config=cfg)

    await notifier.update_state({"messages": [AIMessage(content="foo")]})
    await notifier.flush(force=True)

    # 即使首次回复返回 None，也应使用 thread_message_id 作为更新目标
    assert len(zlark.reply_calls) == 1
    assert any(call["message_id"] == "tid-4" for call in zlark.update_calls)


@pytest.mark.asyncio
async def test_force_flush_overrides_limits(monkeypatch):
    zlark = FakeZlark()
    cfg = NotifyConfig(debounce_ms=10_000, max_updates_per_minute=0)
    notifier = GraphLarkNotifier(zlark, thread_message_id="tid-3", config=cfg)

    # 时间保持不变，且配额为0，常规路径不会刷新
    await notifier.update_state({"messages": [AIMessage(content="x")]})
    assert len(zlark.update_calls) == 0

    # 强制刷新应无视配额/时间限制
    await notifier.flush(force=True)
    assert len(zlark.reply_calls) == 1
    assert len(zlark.update_calls) == 1
