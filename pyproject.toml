[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "QFlowAgent"
version = "0.1.0"
description = "QFlowAgent"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiomysql>=0.2.0",
    "aiosqlite>=0.19.0",
    "asyncpg>=0.29.0",
    "dashscope>=1.24.0",
    "dotenv",
    "duckduckgo-search>=8.1.1",
    "fastapi>=0.104.0",
    "greenlet>=3.2.3",
    "httpx[socks]>=0.27.0",
    "httpx>=0.27.0",
    "json-repair>=0.48.0",
    "langchain-community>=0.3.27",
    "langchain-deepseek>=0.1.4",
    "langchain-experimental>=0.3.4",
    "langchain-mcp-adapters>=0.1.9",
    "langchain-openai>=0.3.28",
    "langchain-postgres>=0.0.15",
    "langchain-tavily>=0.2.11",
    "langchain>=0.3.27",
    "langgraph-api>=0.2.102",
    "langgraph-checkpoint-postgres>=2.0.23",
    "langgraph-cli[inmem]>=0.3.6",
    "langgraph>=0.6.6",
    "lark-oapi>=1.4.20",
    "markdownify>=1.1.0",
    "pandas>=2.3.1",
    "psycopg-pool>=3.2.6",
    "psycopg[binary,pool]>=3.2.9",
    "pymysql>=1.1.0",
    "PySocks>=1.7.1",
    "readabilipy>=0.3.0",
    "redis[hiredis]>=6.4.0",
    "sqlmodel>=0.0.24",
    "sqlparse>=0.5.3",
    "tabulate>=0.9.0",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.43",
]

[project.optional-dependencies]
dev = [
    "langgraph-cli[inmem]" 
]

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.black]
line-length = 120

[tool.ruff]
line-length = 120

[dependency-groups]
dev = [
    "anyio>=4.10.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "ruff>=0.12.9",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
asyncio_mode = "auto"
