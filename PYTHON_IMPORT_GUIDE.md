# Python Import 最佳实践指南

## 问题诊断与解决

### 1. 循环导入问题 ✅ 已解决

**问题现象：**
```
ImportError: cannot import name 'workflow_v2' from partially initialized module 'src'
(most likely due to a circular import)
```

**根本原因：**
- `src/__init__.py` 导出了复杂对象
- 复杂对象的构建依赖了其他模块
- 其他模块又导入了 `src` 中的对象
- 形成循环导入

**解决方案：**
1. **移除顶层导出**：不在 `src/__init__.py` 中导出复杂对象
2. **直接导入**：需要 `workflow_v2` 的模块直接从 `src.langgraph.graph_v2` 导入
3. **修改导入路径**：
   ```python
   # 修改前（错误）
   from src import workflow_v2

   # 修改后（正确）
   from src.langgraph.graph_v2 import workflow_v2
   ```

### 2. 包结构不完整 ✅ 已解决

**问题现象：**
```
ModuleNotFoundError: No module named 'src.langgraph.common'
```

**根本原因：**
- 多个目录缺少 `__init__.py` 文件
- Python无法识别这些目录为包

**解决方案：**
为所有包目录添加 `__init__.py` 文件：
```
src/langgraph/common/__init__.py
src/langgraph/common/models/__init__.py
src/langgraph/common/prompts/__init__.py
src/langgraph/common/utils/__init__.py
src/langgraph/memory/__init__.py
src/langgraph/nodes/__init__.py
src/langgraph/core/common/__init__.py
src/lark/__init__.py
src/lark/hb/__init__.py
src/web/__init__.py
```

## Import 最佳实践

### 1. __init__.py 文件原则

**✅ 好的做法：**
```python
# 轻量级，仅包含文档和空的 __all__
"""模块说明文档"""
__all__ = []
```

**❌ 避免的做法：**
```python
# 不要在 __init__.py 中导入复杂对象
from .complex_module import heavy_object  # 可能导致循环导入
```

### 2. 循环导入预防

**✅ 使用 TYPE_CHECKING（合理场景）：**
```python
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .some_module import SomeType  # 仅用于类型提示
```

**✅ 延迟导入：**
```python
def __getattr__(name: str):
    if name == "heavy_object":
        from .heavy_module import heavy_object
        return heavy_object
    raise AttributeError(f"module {__name__!r} has no attribute {name!r}")
```

**✅ 直接导入：**
```python
# 不要通过中间层导入
from src.langgraph.graph_v2 import workflow_v2  # 直接导入
# 而不是
from src import workflow_v2  # 通过 __init__.py 导入（不推荐）
```

### 3. 项目结构建议

```
src/
├── __init__.py                 # 空的，不导出任何东西
├── langgraph/
│   ├── __init__.py            # 可以导出核心功能
│   ├── graph_v2.py            # 核心图定义（workflow_v2）
│   ├── common/
│   │   ├── __init__.py        # 空的
│   │   ├── utils/
│   │   │   ├── __init__.py    # 空的
│   │   │   └── *.py
│   │   └── ...
│   └── ...
└── ...
```

### 4. 导入顺序规范

```python
# 1. 标准库导入
import asyncio
import logging
from typing import Set

# 2. 第三方库导入
import lark_oapi as lark
from langchain_core.messages import HumanMessage

# 3. 本地导入（按层级排序）
from src.langgraph.graph_v2 import workflow_v2
from src.lark.card.elements import card_element_markdown
from src.lark.notify import GraphLarkNotifier
```

## 调试技巧

### 1. 检查循环导入
```bash
# 查找可能的循环导入
python -c "import sys; sys.path.insert(0, '.'); import src"
```

### 2. 验证包结构
```bash
# 检查缺失的 __init__.py
find src -type d | while read dir; do
    if [ ! -f "$dir/__init__.py" ]; then
        echo "Missing __init__.py in: $dir"
    fi
done
```

### 3. 测试导入
```bash
# 测试关键模块导入
uv run python -c "from src.langgraph.graph_v2 import workflow_v2; print('✅ 导入成功')"
```

## 总结

通过以上修复，我们解决了：
1. ✅ 循环导入问题
2. ✅ 包结构不完整问题  
3. ✅ 导入路径错误问题

现在项目的import结构清晰、无循环依赖，可以正常运行。
