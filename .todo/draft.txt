------- 8/19-1-start
任务：
> 现在数据库的连接代码非常非常复杂，现在希望简化
> 1. 执行sql时，排队顺序执行，更安全，不要搞什么并发连接池，又复杂又危险，还有熔断之类的复杂逻辑
> 2. 保留sockets相关的处理
> 3. 能否只使用一个python库来处理连接？现在很多fallback处理可能是由于sockets管理过于复杂导致的技术债，我们倾向于只保留aiomysql

------- 8/19-1-1
1. 你需要通读原有连接器的代码，评估你的修改是否引入了其他风险
2. 从你的调试过程来看，数据库连接失败后，Langgraph仍然继续执行了，这不符合我们的健康检查目的，请修复健康检查的功能


------- 820

消息队列？
智能上下文选择器？

- 前端展示历史分析
- 批量执行飞书任务

common
    - 简化模型管理 ✅
    - 简化上下文管理（统一使用一个context_builder.py）
    - 区分消息为 System、Human、ai， 优化llm注意力


langgraph
    - 生成曲线图,给模型或人参考？  图的token可能比数据表便宜的多? 要测测一个图token占用情况

service
    - 测试 checkpoint_manager
    - 
web 
    - 展示历史任务
    - 批量执行任务

lark
    - 获取消息历史，与本地对比？
    - 检索消息历史，获取问题数据库？


配置 temperature

能否将worker的工作信息，添加到AIMessage中，而不是System或HumanMessage




1. 现在希望由workflow明确写清楚状态机逻辑，而不是靠continue来循环
2. 现在 planner_router 似乎没有用了
3. worker_task 仍保留着强烈的对象风格，希望整理清楚逻辑后，改成与planner reporter一样的functional风格，便于灵活调整工作流
请全面梳理这个workflow，优化架构




doing 

- 修改 checkpoint_manager 的 _format_checkpoint_to_task_detail
- 适配前端
- 检查任务列表 - 继续调checkpoint_manager
- 设计如何批量启动、监控任务


目标：
mvp-能识别封禁问题（需要优化非封禁的误判）
需达到可运营状态
- 1. 现在输出都在日志中，review效率很低，需要达到可观测效果
- 2. 批量执行
  - 现在支持并行执行、数据存储历史。但查看历史属于也依赖上面
- 3. 告警库、历史问题库（类型：自定义查询或具体告警规则）
    - 飞书机器人脚本拉取数据，存到数据库，每天定时刷新
    - 分析Agent索引待办项，自动执行或手动重跑（前期手动执行观察效果）





-   已跑任务能查看历史，查看分析流程 （仅需最新checkpoint即可，以state为准）
-   


技术债
    - worker_params太复杂，直接使用tasks, functional api不需要这么复杂的数据结构了
        - 比如，不需要current_task这种用法
        - 可以直接返回result，而不是放到state中用key来找
    - worker_result太复杂，直接使用tasks, 挂载result



1. ./src/langgraph中, state状态需要增加当前plan轮次、已执行task个数；默认限制3轮plan，40个task，达到上限则直接进入reporter阶段。限制放到configurable中，前端也可配置。
2. 使用 apps/zego_cli.py 调通现在的langgraph代码
3. 代码中很多地方正在使用plans[-1]，评估是否应该使用全量plan
4. 我修改了_format_checkpoint_to_task_detail的返回值，请帮我做前端适配，
> 前后端联调哦时，使用scripts/run_server.sh、scripts/run_web.sh 、浏览器工具调试


todo 废弃 worker_results


AI 增加判断
1. 是否毛刺
2. 是否已恢复（通过查看2天内的）





现在后端在 src/langgraph 中，向前端传递的数据
1. 每个task.result()返回的值，都会传给前端，前端在 `eventSource.on('updates', (event) => {`收到该返回值，该值是每个任务独立更新的内容，是增量的更新
2. @workflow中，每次writer(state)，都会触发前端的`eventSource.on('custom', (event) => {`，这个信息是全量的state，
3. 目前前端的缺陷：现在都是简单粗暴直接渲染了json出来，很难看，

现在的需求：
展示逻辑：
1. 对于custom的更新，展示state全量状态，获取关键信息展示，我们可以先只解析出 state.plans,并将每个plan中的task.query_params.query_title 展示出来，并展示 status 字符串，不需要做复杂枚举和类型转换，后端随时会更新status
展示为
    plan[0].goal
    > plan[0].thinking
        - plan[0].task[0].query_params.query_title status
        - plan[0].task[1].query_params.query_title status
        - plan[0].task[2].query_params.query_title status
    plan[1]
    > plan[1].thinking
        - plan[1].task[0].query_params.query_title status
        - plan[1].task[1].query_params.query_title status
        - plan[1].task[2].query_params.query_title status
注意，plan和task的数量是动态的

2. 对于update的更新，为避免刷新太多导致用户疲劳，我们要做动态的展示，只展示最新的update，显得模型在working，在更新即可，这应该只会吸引一小部分用户的注意力，可以参考现在 AI 聊天网站的设计，



任务详情页中，也需要展示与执行中的任务相同的逻辑，前端可以从api拿到对应任务的state,那么就能和进行中的任务有同样的渲染逻辑了
这部分代码需要复用，改一处两个页面都需要同步更新




1. 

现在项目前后端的类型，有很多硬编码的地方，是否有合理方案管理？现在后端改了代码话，前端也要跟着改，很不利于项目发展
请上网搜索最佳实践，能否使用protobuf grpc等方案来解决，如果不合适，调研其他方案


现在项目需要一个批量启动任务的功能，请端到端完成：有批量启动任务的API、有批量启动任务的前端


short-term


---- 后端似乎有时候会生成同样的plan，第二轮和第三轮的时候，应该是提示词给的一样了


前端
给任务列表一些额外功能， 
1. 任务列表：添加继续执行的功能，使用对应的config、thread_id、checkpoint_id，重新调用langgraph
2. 任务列表：添加删除任务功能，注意 删除任务的service先有接口，接口是空实现，这部分功能高风险，我会补充这部实现
3. 任务列表：去除检查点列，现在的状态列改为任务状态，增加agent状态列，和redis中存的执行状态一致
4. redis增加功能，如果一个任务上次更新时间距今已经1小时了，则标记agent状态为stop
5. 批量启动任务时，鼠标点击弹窗外面需要能关弹窗；







---- 8/22

1. start_task_execution直接删除，不保留轻量职责同时检索 running和agent_status，
2. 关于“建议把过滤条件放宽为 task.sse_key || task.status === 'running'（可选）。”你的理解是不对的，task.status实际上是langgraph的checkpoint拿到的最新状态，此时任务虽然没完成，但对应的任务进程可能早已结束。 这个误解是正常的，我们要把status改为langgraph_status或agent_status；而sse_key对应running，我觉得不要用sse_key了，搞一个stream_avaliable状态吧
3. 关于stream_avaliable，我们要确保进程中正在执行的任务，返回的任务信息中stream_avaliable都为true，这个保障需要更明显一些，需要抽离一个 stream_list_manager的类，代码也更明确
4. 这样梳理下来，我们发现，任务未完成，且stream_avaliable=false的，其实就是dead状态，基于此评估，对外我们可以合并langgraph_status和stream_avaliable，这两个都是内部状态，对外可以展示为：
4.1. 如果langgraph任务已完成，langgraph_status状态为done；此时stream_avaliable也应该为false，这个service要保证结束任务时维护好redis中的stream_list, 此时对外展示为done
4.2. 如果langgraph任务未完成，且正在执行，此时stream_avaliable应该为true，且stream_list也应该有该任务，此时对外展示running
4.3 如果langgraph任务未完成，进程中stream_list没有这个任务，说明这个任务是过期的service实例启动的，已经被强行终止了（常见于调试阶段，或服务异常中断），这种情况对外展示为dead
5. api只返回这个状态，前端做适配；
6. 对于dead状态的任务：增加api接口resume：使用对应thread_id的最新chekpoint的config，来再次调用zego_graph，即可恢复执行，这是langgraph的机制。 恢复后的任务仍为running状态。langgraph支持从任意checkpoint, 前端也要提供resume按钮
7. 前端已完成的任务不能提供继续执行按钮，langgraph后端没有这种功能，可以提供重新执行，对应功能就是使用该input重新执行一次create


---- 8/23

1. 帮我更新curl_me.sh，如何使用新的一套接口
    - 创建任务 不监听流
    - 创建任务 并监听流
    - 批量创建 2个任务
    - 删除任务

--------

前端点击






-------8/25
1. 请将graph的v1完全废弃
2. 既然langgraph已经做了存储的动作，api和service层是否可以优化？
3. 使用新的workflow_v2，新增api层的v3接口，废弃api的v1v2接口
4. 前端适配



-----8/26
- redis的发布订阅的key value也需要重新梳理方案，重新制定最精简核心的功能方案

---

检查单元测试，仍存在的功能需要修复测试，已经不存在的功能将测试删除


1. 不要删除sql测试，这个很重要，能否根据DataQueryParams.metric_name中的Literal来动态生成测试？由于
2. DAGPlan需要增加 round字段，并标记SkipJsonSchema，由workflow标记，以便优化这段代码
        # 如果没有指定轮次，自动递增
        if round is None:
            latest_plan = await self._get_latest_plan(task.id)
            round = (latest_plan.round + 1) if latest_plan else 1
3. 增加一组redi manager的测试，发布流和订阅流的功能

sql测试需要像之前一样，生成的sql保存到 ./tests/data中，以便用户能够查阅抽检

-
1. sql测试仍需优化：每个指标保存一个文件名；每个生成的sql按顺序写入，以免多次执行污染git
2. 根据 typemap ，我们的类型定义过于复杂，因为这个项目是先实现了langgraph，后又实现了SQLModel, 导致类型需要不断转换；对于这个问题，请评估能否进一步重构langgraph使得我们可以有一个更简单的实现
- 
3. 我们今天完成了超级大的重构，请检查重构所有文档





------- 0827

请重温 《 @QFlowAgent数据流分析.md 》 以及 《项目验收流程.md》，你刚刚实现了mock模式，可以绕开大模型请求专注于前端调试，请使用mock模式完成测试，根据 项目验收流程.md 验收文档对前端进行逐项自测，测试过程中请关注后端前端是否有各种报错。mock模式的前端验收通过后，可以刷新页面执行默认的真实查询请求。



你是全栈工程师，负责langgraph计算层、SQLModel存储层、fastapi服务层、web前端。
请继续根据当前现状，继续推进你的任务：
**请确保任务1自测验收完全通过后，再进行任务2！**
任务1: 根据 《项目调试指南.md》、《 QFlowAgent数据流分析.md 》、完成《项目验收流程.md》，直到每一条都测试通过 (目前任务结束后，eventSource关闭后，展示内容不见了，用户无法使用)
**请确保任务1自测验收完全通过后，再进行任务2！**
任务2：前端需要新增workspace功能，请全链路整理workspace相关的字段、数据流、，更新到QFlowAgent数据流分析.md中，并为前端增加切换workspace的功能，
任务3：需要为前端增加批量启动任务的功能，前端交互和我们的测试上，默认启动两个任务，分析1850816294推流成功率； 分析1850816294推流rtc子事件成功率；也需要支持mock模式。服务端api层也需要添加相应的新接口。这种启动方式的任务，默认前端展示最后一个任务的实时进展
任务4：整体评估在不影响功能的前提下代码是否有精简空间

提示：陷入困境时，请重温《QFlowAgent数据流分析.md》并深度思考







- P2 代码优化
看看sequence到底用没用上 get_incremental_manager

- P0 
TODO 测试第二轮为什么没有更新
1. 检查trigger的地方，数据是否正确
2. 检查是不是和没有传round有关

批量任务启动




------ 0829
1. 批量执行历史golden任务


2. plan.status 如何判断dead/挂起
    完成状态：有result就算完成
    进行中： 需要service内存维护一个已启动列表

    if result : 完成
    if service.running_tasks.contain(query_id): 进行中
    else : 挂起

    if db.status==running但进程中没有该任务，则fix_status为挂机


现在前端任务有两个状态：任务状态、Agent状态
这两个状态的实现是不对的