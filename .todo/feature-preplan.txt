
-----
项目背景：
公司有一套告警系统，告警内容：“appid=1234 拉流成功率 趋势下降”，或"沙特阿拉伯 拉流成功率 突变下降“

工作处理方式：
我需要分析下钻这个告警，看是哪里出的问题，我只做数据层面的分析，确认问题特征后，我需要反馈给技术团队进一步分析
比如：沙特阿拉伯 运营商stc 出现成功率下降，聚集 sdk_version=3.20.5，聚集错误码12200101上涨10%

我正在进行的项目：
我现在正在基于 langgraph的functional api, 为告警系统开发下游的智能分析系统，目标是为我自动化这项工作。

已完成的部分：
现在已经能为大模型提供sql查询到的数据，但具体查询什么数据现在是由大模型根据问题决定的，不是很可控，分析准确率低

现在需要进一步优化的功能：
我的设想是，可以预设一系列的预案，让大模型评估告警是否符合特定预案，如果符合则直接执行预案流程，如果不符合预案，再让大模型以现在的方式探索

```
成功率
    s1 - 成功率趋势 —— 是否有明显问题
        - 是 -> s2 错误码分析
        - 否 -> 回答用户
    
    s2 - 错误码分析 —— 是否有明显异常的错误码占比上涨
        - 是
        - 否
    s3 错误码下钻 sdk维度、country维度、isp维度
    s4 成功率趋势下钻 sdk维度、country维度、isp维度
```

如何组织这些预案方便维护？ 我不可能写非常多的if else来判断，请给我细化这个“预案”的功能


-----

我将给定一个预案，你需要将其落地，并测试预案系统是否符合预期工作

1. 某个国家的 拉流成功率下降
 - 查看30天整体成功率趋势
 - 下钻appid维度，评估是否是单独appid的影响，还是整体的影响
    - 如果是appid的单独问题
        - 下钻运营商维度，确认是否是 appid+isp的问题 
        - 如果运营商没有聚集性，查看appid在其他国家是否正常
            如果appid在其他国家也有问题，下钻sdk版本维度
 - 下钻运营商维度，评估是否是单独运营商的问题
    - 如果是运营商的单独问题
        - 下钻appid维度，查看影响范围

注意，这个预案只是我的草稿，你需要完善这个思路，我们可下钻的维度有 sdk_version、isp、country、app_id


-----
注意
1. langgraph流程确认：预案功能生成DAGPlan后，preplan_node批量查询数据后，我们可以直接转到planner, planner识别有plan未完成时，会自动进行派发，这样我们的派发任务代码只在planner实现就可以了 
2. 删除废弃代码，精简项目，删除随机生成的预案，只保留我的
3. 注册表不要搞v1v2，保留最新，时刻保持最佳实践

----- doing

1. 预案功能专注于DAGPlan的tasks字段，其他字段由preplan大模型填写,有助于后续大模型扩展
2. 预案的选择由preplan大模型进行选择，triggers等字段是无用的，让大模型来识别并选择预案
3. parameters等字段也是无用的，排查问题时，可能遇到多个appid有问题等，这需要大模型在执行预案过程中调整，
4. 大模型在分析过程中，比如发现 国家下，有两个appid有问题，那需要进入 appid分析预案，这可能涉及动态调整预案，也许我们提供的是一个决策树而不是预案？或者说是一个golden check?

第四个问题至关重要，决定了我们项目的走向，请评估如何推进这项功能，让这个项目的智能分析更可靠

---- pending







成功率-国家维度

step_name: check_issue_trend
- user_query_params
    - metric_name
    - where =['appid=xxx','country = xxx']
- goal: 确认问题基本特征
- action: 查看30天整体成功率趋势
- condition:
    condition-1 确认问题
        - output: 
            issue_start_time: 问题开始时间
            issue_end_time: 问题结束时间
            metric_value_change: 指标波动值
            sample_count: 指标样本数量
            next_step: check_issue_group_by_appid
    condition-2 没有明显问题
        - output: report_no_problem


step_name: check_issue_group_by_appid
- step_goal: 确认不同appid的表现，尝试缩小范围
- user_query_params
    - metric_name
    - where =['appid=xxx','country = xxx']
- step_params
    - group_by_field: appid
- condition:
    condition-1 仅一个appid有问题
        - output: 
            is_all_appid_have_issue? 
            is_special_appid_have_issue?
            this_appid_sample_ct_percent: 该appid的样本数量占比 （如果父维度拆解下来只有这一个appid，则问题聚集性无效，只能说明父维度下只有一个appid有用量）
            metric_value_change: 指标波动值
            sample_count: 指标样本数量
            next_step: check_issue_group_by_appid

